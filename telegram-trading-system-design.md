# Telegram信号交易系统技术设计文档

## 1. 项目概述

### 1.1 系统目标
构建一个基于Telegram群组信号的自动化交易系统，支持实时交易和历史信号回测，具备完整的前端控制台和API接口。

### 1.2 核心功能
- Telegram群组信号自动采集和解析
- 实时交易信号执行（直接对接交易所API）
- 基于历史信号和K线数据的自建回测系统
- 信号源性能评估和统计
- Web控制台管理界面

### 1.3 技术架构原则
- 微服务架构，组件解耦
- 前后端分离
- 容器化部署
- 直接交易所API对接
- 自建回测引擎
- 数据一致性保证

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "外部服务"
        TG[Telegram群组]
        BINANCE[Binance API]
        OKEX[OKX API]
        BYBIT[Bybit API]
        KLINE_API[K线数据API]
    end
    
    subgraph "前端服务"
        NEXTJS[Next.js 14 App Router]
    end
    
    subgraph "后端服务"
        FASTAPI[FastAPI主服务]
        COLLECTOR[Telegram信号采集器]
        TRADING_ENGINE[交易引擎]
        BACKTEST_ENGINE[回测引擎]
        SCHEDULER[Celery任务调度]
        MARKET_DATA[市场数据服务]
    end
    
    subgraph "数据存储"
        POSTGRES[(PostgreSQL 15)]
        REDIS[(Redis 7)]
        TIMESCALE[(TimescaleDB - K线数据)]
    end
    
    TG --> COLLECTOR
    COLLECTOR --> POSTGRES
    NEXTJS --> FASTAPI
    FASTAPI --> TRADING_ENGINE
    FASTAPI --> BACKTEST_ENGINE
    TRADING_ENGINE --> BINANCE
    TRADING_ENGINE --> OKEX
    TRADING_ENGINE --> BYBIT
    BACKTEST_ENGINE --> TIMESCALE
    MARKET_DATA --> KLINE_API
    MARKET_DATA --> TIMESCALE
    SCHEDULER --> POSTGRES
    FASTAPI --> REDIS
```

### 2.2 服务间通信架构

```mermaid
graph TB
    subgraph "服务通信架构"
        FASTAPI_MAIN[FastAPI主服务]
        TELEGRAM_COLLECTOR[Telegram采集器]
        TRADING_ENGINE[交易引擎]
        BACKTEST_ENGINE[回测引擎]
        MARKET_DATA_SERVICE[市场数据服务]
        
        subgraph "消息队列"
            CELERY_BROKER[Redis Broker]
            CELERY_WORKER[Celery Worker]
        end
        
        subgraph "外部API"
            EXCHANGE_APIS[交易所APIs]
            MARKET_APIS[市场数据APIs]
        end
    end
    
    FASTAPI_MAIN --> CELERY_BROKER
    CELERY_BROKER --> CELERY_WORKER
    CELERY_WORKER --> TELEGRAM_COLLECTOR
    CELERY_WORKER --> TRADING_ENGINE
    CELERY_WORKER --> BACKTEST_ENGINE
    CELERY_WORKER --> MARKET_DATA_SERVICE
    TRADING_ENGINE --> EXCHANGE_APIS
    MARKET_DATA_SERVICE --> MARKET_APIS
    BACKTEST_ENGINE --> MARKET_DATA_SERVICE
```

## 3. 数据库设计

### 3.1 数据库架构

- **主数据库**: PostgreSQL 15 (信号数据、用户数据、交易数据、回测数据)
- **时序数据库**: TimescaleDB (K线数据、价格数据)
- **缓存数据库**: Redis 7 (会话缓存、任务队列、实时数据)

### 3.2 数据表结构

```mermaid
erDiagram
    signal_sources {
        uuid id PK
        varchar name
        varchar telegram_chat_id
        varchar telegram_chat_title
        varchar telegram_chat_type
        json parsing_config
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    telegram_signals {
        uuid id PK
        uuid source_id FK
        timestamp signal_timestamp
        varchar pair
        varchar action
        decimal price
        decimal confidence_score
        text original_message
        json parsed_data
        varchar status
        timestamp created_at
    }
    
    backtest_sessions {
        uuid id PK
        varchar session_name
        varchar strategy_name
        varchar pair
        varchar timeframe
        date start_date
        date end_date
        decimal initial_balance
        json strategy_params
        varchar status
        json results_summary
        timestamp created_at
        timestamp completed_at
    }
    
    backtest_trades {
        uuid id PK
        uuid session_id FK
        uuid signal_id FK
        varchar pair
        varchar action
        decimal entry_price
        decimal exit_price
        decimal quantity
        decimal profit_loss
        timestamp entry_time
        timestamp exit_time
        varchar exit_reason
    }
    
    trading_positions {
        uuid id PK
        varchar exchange_order_id
        uuid source_signal_id FK
        varchar exchange
        varchar pair
        varchar side
        decimal entry_price
        decimal current_price
        decimal quantity
        decimal unrealized_pnl
        decimal realized_pnl
        varchar status
        timestamp opened_at
        timestamp closed_at
    }
    
    kline_data {
        varchar symbol PK
        timestamp timestamp PK
        varchar timeframe PK
        decimal open
        decimal high
        decimal low
        decimal close
        decimal volume
        varchar exchange
    }
    
    exchange_accounts {
        uuid id PK
        varchar exchange
        varchar api_key
        varchar api_secret
        varchar passphrase
        boolean is_testnet
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    signal_performance {
        uuid id PK
        uuid signal_id FK
        decimal profit_ratio
        integer hold_duration_minutes
        varchar result_category
        timestamp calculated_at
    }
    
    signal_sources ||--o{ telegram_signals : generates
    telegram_signals ||--o{ backtest_trades : triggers
    telegram_signals ||--o{ trading_positions : triggers
    telegram_signals ||--o{ signal_performance : measures
    backtest_sessions ||--o{ backtest_trades : contains
```

### 3.3 表详细定义

#### signal_sources (信号源配置)
- `id`: 主键UUID
- `name`: 信号源名称
- `telegram_chat_id`: Telegram群组/频道ID
- `telegram_chat_title`: 群组/频道标题
- `telegram_chat_type`: 聊天类型(group/channel/private)
- `parsing_config`: 信号解析配置JSON
- `is_active`: 是否激活
- `created_at/updated_at`: 时间戳

#### telegram_signals (Telegram信号)
- `id`: 主键UUID
- `source_id`: 关联信号源
- `signal_timestamp`: 信号时间戳
- `pair`: 交易对
- `action`: 操作类型(BUY/SELL)
- `price`: 价格
- `confidence_score`: 信号置信度(0-1)
- `original_message`: 原始消息
- `parsed_data`: 解析后的结构化数据
- `status`: 处理状态

#### backtest_sessions (回测会话)
- `id`: 主键UUID
- `session_name`: 会话名称
- `strategy_name`: 策略名称
- `pair`: 交易对
- `timeframe`: 时间框架
- `start_date/end_date`: 回测日期范围
- `initial_balance`: 初始资金
- `strategy_params`: 策略参数
- `status`: 会话状态
- `results_summary`: 结果摘要

## 4. API接口设计

### 4.1 认证与授权

**认证方式**: JWT Bearer Token

**权限级别**:
- `admin`: 全部权限
- `trader`: 交易相关权限
- `viewer`: 只读权限

### 4.2 API端点设计

```mermaid
graph LR
    subgraph "Authentication"
        AUTH_LOGIN[POST /api/auth/login]
        AUTH_REFRESH[POST /api/auth/refresh]
        AUTH_LOGOUT[POST /api/auth/logout]
    end
    
    subgraph "Signal Management"
        SIG_LIST[GET /api/signals]
        SIG_CREATE[POST /api/signals]
        SIG_GET[GET /api/signals/{id}]
        SIG_PERF[GET /api/signals/performance]
        SIG_SOURCES[GET /api/signals/sources]
        SIG_SOURCE_CREATE[POST /api/signals/sources]
    end
    
    subgraph "Trading Control"
        TRADE_STATUS[GET /api/trading/status]
        TRADE_POSITIONS[GET /api/trading/positions]
        TRADE_FORCE_BUY[POST /api/trading/force-buy]
        TRADE_FORCE_SELL[POST /api/trading/force-sell]
        TRADE_START[POST /api/trading/start]
        TRADE_STOP[POST /api/trading/stop]
    end
    
    subgraph "Backtest Management"
        BT_CREATE[POST /api/backtest]
        BT_LIST[GET /api/backtest]
        BT_STATUS[GET /api/backtest/{id}/status]
        BT_RESULTS[GET /api/backtest/{id}/results]
        BT_DELETE[DELETE /api/backtest/{id}]
    end
    
    subgraph "Analytics"
        ANALYTICS_OVERVIEW[GET /api/analytics/overview]
        ANALYTICS_PERFORMANCE[GET /api/analytics/performance]
        ANALYTICS_SIGNALS[GET /api/analytics/signals]
    end
```

### 4.3 API数据格式

#### 请求格式规范
```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid",
  "data": {
    // 业务数据
  }
}
```

#### 响应格式规范
```json
{
  "success": true,
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "uuid",
  "data": {
    // 响应数据
  },
  "error": null
}
```

### 4.4 WebSocket事件设计

```mermaid
graph TD
    subgraph "WebSocket Events"
        WS_CONNECT[connection]
        WS_SIGNAL[signal_received]
        WS_TRADE[trade_update]
        WS_BACKTEST[backtest_progress]
        WS_ERROR[error_notification]
    end
    
    subgraph "Event Payloads"
        SIGNAL_PAYLOAD[signal_id, pair, action, price, timestamp]
        TRADE_PAYLOAD[trade_id, pair, status, pnl, timestamp]
        BACKTEST_PAYLOAD[session_id, progress, current_trade, estimated_completion]
    end
    
    WS_SIGNAL --> SIGNAL_PAYLOAD
    WS_TRADE --> TRADE_PAYLOAD
    WS_BACKTEST --> BACKTEST_PAYLOAD
```

## 5. 前端架构设计

### 5.1 技术栈选择

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **组件库**: Shadcn/ui
- **状态管理**: Zustand
- **数据获取**: SWR
- **图表**: Recharts + TradingView
- **实时通信**: Socket.io Client

### 5.2 目录结构

```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   │   └── page.tsx
│   │   └── layout.tsx
│   ├── dashboard/
│   │   ├── page.tsx
│   │   └── loading.tsx
│   ├── signals/
│   │   ├── page.tsx
│   │   ├── sources/
│   │   │   └── page.tsx
│   │   └── [id]/
│   │       └── page.tsx
│   ├── trading/
│   │   ├── page.tsx
│   │   ├── positions/
│   │   │   └── page.tsx
│   │   └── performance/
│   │       └── page.tsx
│   ├── backtest/
│   │   ├── page.tsx
│   │   ├── create/
│   │   │   └── page.tsx
│   │   └── [id]/
│   │       └── page.tsx
│   ├── analytics/
│   │   └── page.tsx
│   ├── settings/
│   │   └── page.tsx
│   ├── layout.tsx
│   ├── loading.tsx
│   ├── error.tsx
│   └── not-found.tsx
├── components/
│   ├── ui/
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── table.tsx
│   │   ├── chart.tsx
│   │   └── modal.tsx
│   ├── layout/
│   │   ├── sidebar.tsx
│   │   ├── header.tsx
│   │   └── breadcrumb.tsx
│   ├── signals/
│   │   ├── signal-list.tsx
│   │   ├── signal-card.tsx
│   │   └── signal-performance.tsx
│   ├── trading/
│   │   ├── position-table.tsx
│   │   ├── trading-controls.tsx
│   │   └── pnl-chart.tsx
│   └── backtest/
│       ├── backtest-form.tsx
│       ├── backtest-results.tsx
│       └── backtest-comparison.tsx
├── lib/
│   ├── api.ts
│   ├── auth.ts
│   ├── websocket.ts
│   ├── utils.ts
│   └── validations.ts
├── hooks/
│   ├── use-auth.ts
│   ├── use-signals.ts
│   ├── use-trading.ts
│   └── use-backtest.ts
├── store/
│   ├── auth-store.ts
│   ├── signal-store.ts
│   ├── trading-store.ts
│   └── backtest-store.ts
└── types/
    ├── api.ts
    ├── signal.ts
    ├── trading.ts
    └── backtest.ts
```

### 5.3 状态管理设计

```mermaid
graph TB
    subgraph "Zustand Stores"
        AUTH_STORE[AuthStore]
        SIGNAL_STORE[SignalStore]
        TRADING_STORE[TradingStore]
        BACKTEST_STORE[BacktestStore]
        UI_STORE[UIStore]
    end
    
    subgraph "Store Actions"
        AUTH_ACTIONS[login, logout, refreshToken, checkAuth]
        SIGNAL_ACTIONS[fetchSignals, createSignal, updateSignal, deleteSignal]
        TRADING_ACTIONS[fetchPositions, forceBuy, forceSell, updateStatus]
        BACKTEST_ACTIONS[createBacktest, fetchResults, deleteBacktest]
        UI_ACTIONS[setTheme, setLoading, showNotification]
    end
    
    AUTH_STORE --> AUTH_ACTIONS
    SIGNAL_STORE --> SIGNAL_ACTIONS
    TRADING_STORE --> TRADING_ACTIONS
    BACKTEST_STORE --> BACKTEST_ACTIONS
    UI_STORE --> UI_ACTIONS
```

### 5.4 页面组件设计

#### 主要页面组件

**Dashboard页面**:
- 系统状态概览
- 实时信号流
- 交易统计
- 性能图表

**信号管理页面**:
- 信号源配置
- 信号列表和过滤
- 信号详情查看
- 信号性能分析

**交易控制页面**:
- 当前持仓
- 交易控制按钮
- 实时PnL图表
- 交易历史

**回测分析页面**:
- 回测任务创建
- 回测结果展示
- 多策略对比
- 详细分析报告

## 6. 后端架构设计

### 6.1 FastAPI应用结构

```
app/
├── api/
│   ├── v1/
│   │   ├── auth.py
│   │   ├── signals.py
│   │   ├── trading.py
│   │   ├── backtest.py
│   │   └── analytics.py
│   └── deps.py
├── core/
│   ├── config.py
│   ├── security.py
│   ├── database.py
│   └── logging.py
├── models/
│   ├── user.py
│   ├── signal.py
│   ├── trading.py
│   └── backtest.py
├── schemas/
│   ├── user.py
│   ├── signal.py
│   ├── trading.py
│   └── backtest.py
├── services/
│   ├── auth_service.py
│   ├── signal_service.py
│   ├── trading_service.py
│   ├── backtest_service.py
│   ├── exchange_service.py
│   └── market_data_service.py
├── utils/
│   ├── telegram_parser.py
│   ├── signal_validator.py
│   ├── docker_manager.py
│   └── notifications.py
├── tasks/
│   ├── signal_collector.py
│   ├── performance_calculator.py
│   └── cleanup_tasks.py
└── main.py
```

### 6.2 核心服务设计

#### SignalService (信号服务)
- `collect_telegram_signals()`: 采集Telegram信号
- `parse_signal_message()`: 解析信号消息
- `validate_signal()`: 验证信号有效性
- `store_signal()`: 存储信号到数据库
- `get_signals_for_backtest()`: 获取回测用历史信号

#### TradingService (交易服务)
- `execute_signal()`: 执行信号交易
- `place_order()`: 下单交易
- `cancel_order()`: 取消订单
- `get_positions()`: 获取当前持仓
- `update_position_status()`: 更新持仓状态
- `calculate_position_size()`: 计算仓位大小

#### BacktestService (回测服务)
- `create_backtest_session()`: 创建回测会话
- `run_backtest()`: 执行回测
- `calculate_performance_metrics()`: 计算性能指标
- `generate_backtest_report()`: 生成回测报告
- `compare_strategies()`: 策略对比

#### ExchangeService (交易所服务)
- `get_account_balance()`: 获取账户余额
- `place_market_order()`: 市价下单
- `place_limit_order()`: 限价下单
- `get_order_status()`: 获取订单状态
- `get_trade_history()`: 获取交易历史

#### MarketDataService (市场数据服务)
- `fetch_kline_data()`: 获取K线数据
- `get_current_price()`: 获取当前价格
- `subscribe_price_updates()`: 订阅价格更新
- `store_kline_data()`: 存储K线数据
- `get_historical_data()`: 获取历史数据

### 6.3 数据流程设计

#### 信号处理流程

```mermaid
flowchart TD
    A[Telegram消息接收] --> B[消息解析]
    B --> C[信号提取]
    C --> D[数据验证]
    D --> E{验证通过?}
    E -->|是| F[存储到数据库]
    E -->|否| G[记录错误日志]
    F --> H[触发WebSocket通知]
    F --> I[执行交易逻辑]
    I --> J[计算仓位大小]
    J --> K[调用交易所API]
    K --> L[更新交易状态]
```

#### 回测执行流程

```mermaid
flowchart TD
    A[用户创建回测任务] --> B[验证回测参数]
    B --> C[查询历史信号]
    C --> D[生成Freqtrade策略文件]
    D --> E[创建Docker容器]
    E --> F[启动Freqtrade回测]
    F --> G[监控回测进度]
    G --> H[回测完成]
    H --> I[解析回测结果]
    I --> J[存储结果到数据库]
    J --> K[清理临时文件]
    K --> L[通知用户完成]
```

## 7. 部署架构设计

### 7.1 容器编排

```mermaid
graph TB
    subgraph "容器编排"
        NGINX[Nginx反向代理]
        NEXTJS_CONTAINER[Next.js容器]
        FASTAPI_CONTAINER[FastAPI容器]
        CELERY_CONTAINER[Celery Worker容器]
        POSTGRES_CONTAINER[PostgreSQL容器]
        REDIS_CONTAINER[Redis容器]
        TIMESCALE_CONTAINER[TimescaleDB容器]
        
        subgraph "数据卷"
            POSTGRES_DATA[postgres_data]
            REDIS_DATA[redis_data]
            TIMESCALE_DATA[timescale_data]
        end
        
        subgraph "网络"
            FRONTEND_NETWORK[frontend_network]
            BACKEND_NETWORK[backend_network]
            DATABASE_NETWORK[database_network]
        end
    end
    
    NGINX --> NEXTJS_CONTAINER
    NGINX --> FASTAPI_CONTAINER
    FASTAPI_CONTAINER --> POSTGRES_CONTAINER
    FASTAPI_CONTAINER --> REDIS_CONTAINER
    CELERY_CONTAINER --> REDIS_CONTAINER
    CELERY_CONTAINER --> POSTGRES_CONTAINER
    FASTAPI_CONTAINER --> TIMESCALE_CONTAINER
```

### 7.2 Docker Compose配置结构

```yaml
# docker-compose.yml 结构
services:
  nginx:
    image: nginx:alpine
    ports: [80:80, 443:443]
    volumes: [./nginx.conf:/etc/nginx/nginx.conf]
    
  frontend:
    build: ./frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8000
    
  backend:
    build: ./backend
    environment:
      - DATABASE_URL=************************************/db
      - REDIS_URL=redis://redis:6379
    depends_on: [postgres, redis]
    
  celery:
    build: ./backend
    command: celery worker
    depends_on: [postgres, redis]
    
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=trading_db
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes: [postgres_data:/var/lib/postgresql/data]
    
  redis:
    image: redis:7-alpine
    volumes: [redis_data:/data]
    
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=market_data
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes: [timescale_data:/var/lib/postgresql/data]
    ports: [5433:5432]

volumes:
  postgres_data:
  redis_data:
  timescale_data:

networks:
  default:
    driver: bridge
```

### 7.3 环境配置管理

```mermaid
graph LR
    subgraph "配置管理"
        ENV_LOCAL[.env.local]
        ENV_DEV[.env.development]
        ENV_PROD[.env.production]
        
        subgraph "配置分类"
            DATABASE_CONFIG[数据库配置]
            API_CONFIG[API配置]
            TELEGRAM_CONFIG[Telegram配置]
            FREQTRADE_CONFIG[Freqtrade配置]
            SECURITY_CONFIG[安全配置]
        end
    end
    
    ENV_LOCAL --> DATABASE_CONFIG
    ENV_DEV --> API_CONFIG
    ENV_PROD --> TELEGRAM_CONFIG
    DATABASE_CONFIG --> FREQTRADE_CONFIG
    API_CONFIG --> SECURITY_CONFIG
```

## 8. 技术规范与要求

### 8.1 代码质量标准

**前端代码规范**:
- TypeScript严格模式
- ESLint + Prettier
- Husky预提交检查
- 组件单元测试
- E2E测试覆盖

**后端代码规范**:
- Python类型提示
- Black代码格式化
- Pytest单元测试
- 接口文档自动生成
- 异常处理统一规范

### 8.2 性能要求

**API性能**:
- 平均响应时间 < 200ms
- 并发请求支持 > 1000/min
- 数据库查询优化
- Redis缓存策略
- 分页查询限制

**前端性能**:
- 首屏加载时间 < 3s
- 静态资源CDN
- 图片懒加载
- 代码分割优化
- PWA支持

### 8.3 安全要求

**认证授权**:
- JWT Token有效期管理
- Refresh Token机制
- 权限级别控制
- API访问限制
- CORS安全配置

**数据安全**:
- 敏感信息加密
- SQL注入防护
- XSS攻击防护
- HTTPS强制使用
- 数据备份策略

### 8.4 监控与日志

**应用监控**:
- 性能指标收集
- 错误日志聚合
- 业务指标统计
- 系统资源监控
- 告警通知机制

**日志规范**:
- 结构化日志格式
- 不同级别日志分离
- 敏感信息脱敏
- 日志轮转策略
- 集中化日志管理

## 9. 开发约定

### 9.1 Git工作流

- 主分支: `main`
- 开发分支: `develop`
- 功能分支: `feature/*`
- 修复分支: `hotfix/*`
- 发布分支: `release/*`

### 9.2 提交规范

```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 重构代码
test: 测试相关
chore: 构建工具、辅助工具等
```

### 9.3 版本管理

- 语义化版本号: `MAJOR.MINOR.PATCH`
- 版本标签: `v1.0.0`
- 变更日志: `CHANGELOG.md`
- 发布说明: GitHub Releases

### 9.4 文档要求

- API文档自动生成
- 组件文档: Storybook
- 技术文档: Markdown
- 部署文档: README
- 变更文档: CHANGELOG

---

这个技术设计文档提供了完整的系统架构和实现细节，可以直接指导开发团队进行编码实现。文档涵盖了从数据库设计到前端界面的所有技术层面，确保系统的可维护性和可扩展性。 