services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend
      - frontend
    networks:
      - husky-network

  backend:
    build: ./backend
    environment:
      - DATABASE_URL=************************************************/husky
      - REDIS_URL=redis://************:6379/15
    # 不暴露端口，只通过 nginx 访问
    volumes:
      - ./backend:/app
      - ./backend/logs:/app/logs
    networks:
      - husky-network

  frontend:
    build: ./frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost/api
    # 不暴露端口，只通过 nginx 访问
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - husky-network

networks:
  husky-network:
    driver: bridge