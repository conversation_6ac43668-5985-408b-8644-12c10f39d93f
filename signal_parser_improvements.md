# 信号解析器改进建议

## 当前问题分析

基于对signals.md中7个信号的测试，发现以下主要问题：

### 1. 止盈目标提取失败 (4/7信号)
- SKATE信号：未提取到4个止盈目标
- TURBO信号：只提取到1个，应该是6个
- DF信号：只提取到1个，应该是4个
- ETH信号：提取错误，混淆了JTO的目标

### 2. 信号类型识别错误 (2/7信号)
- TURBO信号：明确标注"Short"但识别为LONG
- ETH信号：明确标注"SHORT/SELL"但识别为LONG

### 3. 特殊关键词处理不当 (2/7信号)
- DF信号："ABOVE ABOVE ABOVE - 3115" 应该表示入场价格为3115
- ETH信号："BELOW BELOW BELOW - 2521" 应该表示入场价格为2521

### 4. 杠杆范围处理错误 (3/7信号)
- SKATE信号："25x – 75x" 应该取中间值50，实际取了25
- H信号："20X TO 75X" 应该取中间值47.5，实际取了20
- DF信号：杠杆被错误识别为3115而不是10

### 5. 多信号混淆 (1/7信号)
- ETH+JTO信号：两个信号混在一起，解析结果混乱

## 具体改进方案

### 1. 改进止盈目标提取

```python
# 增强止盈目标模式
'take_profit_patterns': [
    # 现有模式...
    
    # 新增模式：
    r'(?:TP|Take\s*Profit)\s*[:：\-]+\s*([0-9,.\s\$\-]+)',  # TP :- 474$ - 469.5$ - 461$ - 453$ - 444$
    r'(?:Target|Targets)\s*[:：]?\s*([0-9,.\s\-\$]+)',      # Targets: 3130 - 3145 - 3160 - 3175
    r'([0-9.]+)\s*–?\s*(?:First\s*Breakout|Momentum\s*Builds|Bullish\s*Surge|Moon\s*Zone)',  # 特殊描述格式
    r'🥇\s*([0-9.]+)|🥈\s*([0-9.]+)|🥉\s*([0-9.]+)',      # 奖牌emoji格式
    r'TP\d+:\s*([0-9.]+)',                                   # TP1: 1.9458格式
]
```

### 2. 改进信号类型识别

```python
# 增强信号类型识别优先级
def _extract_signal_type(self, message: str) -> Optional[SignalType]:
    # 优先检查明确的文本标识
    if re.search(r'\b(?:SHORT|SELL)\b', message, re.IGNORECASE):
        return SignalType.SHORT
    if re.search(r'\b(?:LONG|BUY)\b', message, re.IGNORECASE):
        return SignalType.LONG
    
    # 然后检查emoji和其他模式
    # ...
```

### 3. 处理特殊关键词

```python
# 新增特殊关键词处理
'special_entry_patterns': [
    r'(?:ABOVE\s+ABOVE\s+ABOVE|ABOVE)\s*[-:]?\s*([0-9.]+)',  # ABOVE格式
    r'(?:BELOW\s+BELOW\s+BELOW|BELOW)\s*[-:]?\s*([0-9.]+)',  # BELOW格式
]
```

### 4. 改进杠杆范围处理

```python
def _extract_leverage(self, message: str) -> int:
    # 处理范围格式
    range_pattern = r'(?:leverage|杠杆)[:：]?\s*(\d+)x?\s*[–\-到TO]\s*(\d+)x?'
    match = re.search(range_pattern, message, re.IGNORECASE)
    if match:
        low = int(match.group(1))
        high = int(match.group(2))
        return (low + high) // 2  # 取中间值
    
    # 现有单一杠杆处理...
```

### 5. 多信号分离处理

```python
def _split_multiple_signals(self, message: str) -> List[str]:
    # 按分隔符分离多个信号
    separators = ['--', '---', '\n\n---\n\n']
    signals = [message]
    
    for sep in separators:
        new_signals = []
        for signal in signals:
            new_signals.extend(signal.split(sep))
        signals = new_signals
    
    return [s.strip() for s in signals if len(s.strip()) > 50]
```

## 优先级改进顺序

1. **高优先级**：信号类型识别错误修复
2. **高优先级**：止盈目标提取改进
3. **中优先级**：杠杆范围处理改进
4. **中优先级**：特殊关键词处理
5. **低优先级**：多信号分离处理

## 预期改进效果

实施这些改进后，预期准确率从当前的14.3%提升到80%以上。