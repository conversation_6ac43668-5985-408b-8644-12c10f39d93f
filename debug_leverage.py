#!/usr/bin/env python3
"""
杠杆解析调试脚本
"""

import asyncio
import sys
import os
import re

# 添加backend路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.signal_parser import EnhancedSignalParser

# 测试信号
DF_SIGNAL = """Long/Buy #DF/USDT  

Entry Point ABOVE ABOVE ABOVE - 3115

Targets: 3130 - 3145 - 3160 - 3175

Leverage - 10x

Stop Loss - 2990"""

ETH_SIGNAL = """SHORT/SELL #ETH/USDT 

Entry Point BELOW BELOW BELOW - 2521

Targets: 2508 - 2495 - 2483 - 2470

Leverage - 10x
Stop Loss - 2560


--

JTO/USDT📈

1.9055 - 1.8755 多

TP1: 1.9458
TP2: 1.9725
TP3: 1.9947

SL: 1.8400"""

def debug_leverage(signal_text, signal_name):
    print(f"\n{'='*60}")
    print(f"调试杠杆: {signal_name}")
    print(f"{'='*60}")
    
    parser = EnhancedSignalParser()
    
    print("原始信号:")
    print(signal_text)
    print("\n" + "-"*40)
    
    # 测试杠杆解析
    leverage = parser._extract_leverage(signal_text)
    print(f"解析的杠杆: {leverage}")
    
    # 手动测试各种模式
    print("\n手动测试各种杠杆模式:")
    
    # 1. 测试"Leverage - 10x"模式
    leverage_dash_pattern = re.compile(r'Leverage\s*-\s*(\d+)x?', re.IGNORECASE)
    match = leverage_dash_pattern.search(signal_text)
    if match:
        print(f"Leverage - 10x 模式匹配: {match.group(1)}")
    else:
        print("Leverage - 10x 模式未匹配")
    
    # 2. 测试行尾x格式
    line_end_x_pattern = re.compile(r'\b(\d+)x\s*$', re.IGNORECASE | re.MULTILINE)
    matches = line_end_x_pattern.findall(signal_text)
    if matches:
        print(f"行尾x格式匹配: {matches}")
    else:
        print("行尾x格式未匹配")
    
    # 3. 检查是否有信号分离
    if '--' in signal_text:
        first_signal = signal_text.split('--')[0]
        print(f"\n分离后的第一个信号:")
        print(repr(first_signal))
        
        # 在分离后的信号中测试
        leverage_after_split = parser._extract_leverage(first_signal)
        print(f"分离后解析的杠杆: {leverage_after_split}")
        
        # 手动测试分离后的信号
        match = leverage_dash_pattern.search(first_signal)
        if match:
            print(f"分离后 Leverage - 10x 模式匹配: {match.group(1)}")
        
        matches = line_end_x_pattern.findall(first_signal)
        if matches:
            print(f"分离后行尾x格式匹配: {matches}")

def main():
    debug_leverage(DF_SIGNAL, "DF/USDT")
    debug_leverage(ETH_SIGNAL, "ETH/USDT")

if __name__ == "__main__":
    main()