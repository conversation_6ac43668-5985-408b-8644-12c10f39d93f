# Telegram信号交易系统

🚀 基于Telegram群组信号的自动化交易系统，支持实时交易和历史信号回测。

## ✨ 核心功能

- 📊 **Telegram信号采集**: 自动监控多个Telegram群组，实时采集交易信号
- 🤖 **智能信号解析**: 使用NLP技术解析消息内容，提取交易对、价格、止损等信息
- 💹 **Freqtrade集成**: 无缝对接Freqtrade交易引擎，支持实时交易执行
- 📈 **历史信号回测**: 基于历史信号数据进行策略回测和性能评估
- 🎛️ **Web管理界面**: 现代化的React前端界面，支持信号源管理和系统监控
- 📊 **实时数据展示**: WebSocket实时推送交易状态和系统信息
- 🔍 **信号源评估**: 自动统计信号源表现，计算可靠性评分

## 🏗️ 技术架构

- **前端**: Next.js 14 + TypeScript + Tailwind CSS + React Query
- **后端**: Python + FastAPI + SQLAlchemy + Celery + Redis
- **数据库**: PostgreSQL (主要数据) + SQLite (Freqtrade)
- **交易引擎**: Freqtrade + CCXT
- **部署**: Docker + Docker Compose
- **监控**: WebSocket + 实时通知

## 📁 项目结构

```
telegram-trading-system/
├── backend/                    # FastAPI后端应用
│   ├── app/
│   │   ├── api/               # API路由
│   │   ├── models.py          # 数据库模型
│   │   ├── schemas.py         # Pydantic模式
│   │   ├── services/          # 业务服务
│   │   │   ├── telegram_collector.py
│   │   │   ├── signal_parser.py
│   │   │   ├── freqtrade_client.py
│   │   │   └── backtest_manager.py
│   │   ├── tasks.py           # Celery异步任务
│   │   └── config.py          # 配置管理
│   ├── requirements.txt
│   ├── Dockerfile
│   └── env_example.txt
├── frontend/                   # Next.js前端应用
│   ├── src/
│   │   ├── app/               # App Router页面
│   │   ├── components/        # React组件
│   │   └── lib/               # 工具函数
│   ├── package.json
│   ├── Dockerfile
│   └── tailwind.config.js
├── freqtrade/                  # Freqtrade配置
│   ├── config.json            # 主配置文件
│   └── strategies/            # 交易策略
├── docker-compose.yml          # 容器编排
└── README.md
```

## 🚀 快速部署

### 方式一：Docker Compose (推荐)

1. **克隆项目**
```bash
git clone https://github.com/your-username/telegram-trading-system.git
cd telegram-trading-system
```

2. **配置环境变量**
```bash
# 复制环境配置模板
cp backend/env_example.txt backend/.env

# 编辑配置文件
nano backend/.env
```

主要配置项：
```env
# Telegram配置
TELEGRAM_API_ID=你的API_ID
TELEGRAM_API_HASH=你的API_HASH

# 数据库配置
DATABASE_URL=********************************************/telegram_trading

# 交易所API (可选，用于实盘交易)
BINANCE_API_KEY=你的币安API_KEY
BINANCE_SECRET_KEY=你的币安SECRET_KEY

# JWT密钥 (生产环境请更改)
SECRET_KEY=your_secret_key_here_change_in_production
```

3. **启动服务**
```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f backend
```

4. **访问系统**
- 前端界面：http://localhost:3000
- 后端API：http://localhost:8000
- API文档：http://localhost:8000/docs
- Freqtrade API：http://localhost:8080

### 方式二：开发环境

1. **后端开发环境**
```bash
cd backend

# 安装virtualenv (如果还没安装)
pip install virtualenv

# 创建虚拟环境
python -m virtualenv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
uvicorn main:app --reload --port 8000
```

2. **前端开发环境**
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

3. **启动Celery和Redis**
```bash
# 启动Redis (使用Docker)
docker run -d -p 6379:6379 redis:alpine

# 启动Celery Worker
cd backend
celery -A app.celery_app worker --loglevel=info

# 启动Celery Beat (定时任务)
celery -A app.celery_app beat --loglevel=info
```

## 📋 系统配置

### 1. Telegram设置

1. 访问 https://my.telegram.org/apps 创建应用
2. 获取API ID和API Hash
3. 在系统Web界面中使用手机号登录Telegram
4. 系统会自动获取您加入的所有群组和频道，无需手动添加

### 2. 信号源配置

在Web界面中添加信号源：
- 登录Telegram账户后，选择要监听的群组或频道
- 系统会自动获取群组名称和ID
- 可以搜索和筛选特定的群组
- 支持群组、频道和私聊的监听

### 3. Freqtrade配置

编辑 `freqtrade/config.json`：
- 配置交易所API密钥（实盘交易）
- 设置交易对白名单
- 调整风险管理参数

### 4. 信号解析规则

系统支持多种信号格式：
```
# 买入信号示例
🟢 BUY BTCUSDT
Entry: $43,250
SL: $42,000
TP: $45,000

# 卖出信号示例
🔴 SELL ETHUSDT
Entry: 2580
Stop Loss: 2650
Take Profit: 2450

# 中文信号示例
📈 做多 ADAUSDT
入场价格：0.485
止损：0.470
止盈：0.520
```

## 🎯 使用指南

### 1. 监控信号
- 系统自动监控配置的Telegram群组
- 实时解析和分类交易信号
- 在仪表板查看最新信号状态

### 2. 管理信号源
- 添加/删除信号源
- 查看信号源统计和可靠性评分
- 启用/禁用特定信号源

### 3. 执行交易
- 信号自动发送到Freqtrade
- 支持干跑模式和实盘交易
- 实时监控交易状态

### 4. 历史回测
- 选择时间范围和信号源
- 配置回测参数和过滤器
- 查看详细的回测结果和统计

### 5. 性能分析
- 信号源表现对比
- 交易统计和盈亏分析
- 策略优化建议

## ⚙️ 高级配置

### 环境变量详解

```env
# 数据库配置
DATABASE_URL=postgresql://user:password@host:port/database
REDIS_URL=redis://host:port/db

# Telegram配置
TELEGRAM_API_ID=你的API_ID
TELEGRAM_API_HASH=你的API_HASH

# 安全配置
SECRET_KEY=JWT签名密钥
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Freqtrade配置
FREQTRADE_API_URL=http://freqtrade:8080
FREQTRADE_API_USERNAME=freqtrade
FREQTRADE_API_PASSWORD=freqtrade

# 交易所配置
BINANCE_API_KEY=API密钥
BINANCE_SECRET_KEY=API秘钥

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 应用配置
DEBUG=False
ENVIRONMENT=production
```

### Docker服务说明

- **postgres**: PostgreSQL数据库
- **redis**: Redis缓存和消息队列
- **backend**: FastAPI后端服务
- **frontend**: Next.js前端应用
- **celery**: Celery任务处理器
- **freqtrade**: Freqtrade交易引擎

## 🔧 故障排除

### 常见问题

1. **Telegram无法接收消息**
   - 检查API ID和API Hash是否正确
   - 确认已成功登录Telegram账户
   - 确认要监听的群组或频道已选中
   - 验证群组Chat ID

2. **Freqtrade连接失败**
   - 检查Freqtrade是否正常启动
   - 验证API配置和端口
   - 查看防火墙设置

3. **信号解析失败**
   - 检查信号格式是否支持
   - 查看解析器日志
   - 尝试手动测试解析规则

4. **数据库连接问题**
   - 检查PostgreSQL服务状态
   - 验证数据库连接字符串
   - 确认数据库权限

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend
docker-compose logs celery

# 实时日志
docker-compose logs -f backend
```

## 🛡️ 安全建议

1. **生产环境部署**
   - 更改默认密码和密钥
   - 使用HTTPS
   - 配置防火墙规则
   - 定期备份数据库

2. **API安全**
   - 限制API访问IP
   - 使用强密码
   - 定期轮换密钥

3. **交易安全**
   - 先在测试网络验证
   - 设置合理的风险限制
   - 监控异常交易

## 📞 支持与反馈

- 技术问题：提交GitHub Issue
- 功能建议：创建Feature Request
- 文档改进：提交Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

---

⚠️ **风险提示**: 本系统仅供学习和研究使用。加密货币交易存在风险，请在充分理解和测试后再用于实盘交易。作者不对任何交易损失承担责任。
cd frontend && npm run dev
```

### 4. 访问应用

- 前端控制台: http://localhost:3000
- 后端API文档: http://localhost:8000/docs
- Freqtrade UI: http://localhost:8080

## 🔧 开发指南

### 后端开发

```bash
cd backend
pip install -r requirements.txt
python -m uvicorn main:app --reload
```

### 前端开发

```bash
cd frontend
npm install
npm run dev
```

## 📝 功能特性

- ✅ Telegram群组信号自动采集
- ✅ 实时交易信号执行
- ✅ 历史信号回测分析
- ✅ 信号源性能评估
- ✅ Web控制台管理界面
- ✅ 容器化部署

## 🔒 安全说明

请确保：
1. 妥善保管API密钥和私钥
2. 使用强密码和安全的数据库配置
3. 在生产环境中启用HTTPS
4. 定期备份重要数据

## �� 许可证

MIT License 