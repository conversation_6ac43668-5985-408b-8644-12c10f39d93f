'use client'

import Link from 'next/link'
import { useEffect, useState } from 'react'
import { 
  ArrowLeftIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  ChatBubbleLeftRightIcon,
  ArrowPathIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  WifiIcon,
  MagnifyingGlassIcon,
  UserIcon,
  PhoneIcon,
  KeyIcon,
  ClockIcon,
  DocumentArrowDownIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { CheckIcon } from '@heroicons/react/24/solid'
import { SignalSource, TelegramDialog, TelegramUser, sourceAPI } from '@/lib/api'

function getReliabilityColor(reliability: number) {
  if (reliability >= 80) return 'text-green-600 bg-green-100'
  if (reliability >= 60) return 'text-yellow-600 bg-yellow-100'
  return 'text-red-600 bg-red-100'
}

function getReliabilityText(reliability: number) {
  if (reliability >= 80) return '优秀'
  if (reliability >= 60) return '良好'
  return '一般'
}

interface AddSourceForm {
  name: string
  telegram_chat_id: string
  description: string
  is_active: boolean
}

interface TelegramAuthForm {
  phone_number: string
  code: string
  password: string
}

interface ConfirmDialog {
  isOpen: boolean
  title: string
  message: string
  type?: 'danger' | 'success' | 'info'
  onConfirm: () => void
  onCancel: () => void
}

interface HistoryConfigDialog {
  isOpen: boolean
  sourceId: number
  sourceName: string
}

interface HistoryConfig {
  fetchType: 'days' | 'count'
  daysBack: number
  messageLimit: number
  skipReplies: boolean
}

export default function SourcesPage() {
  const [sources, setSources] = useState<SignalSource[]>([])
  const [telegramDialogs, setTelegramDialogs] = useState<TelegramDialog[]>([])
  const [filteredDialogs, setFilteredDialogs] = useState<TelegramDialog[]>([])
  const [telegramUser, setTelegramUser] = useState<TelegramUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showTelegramAuth, setShowTelegramAuth] = useState(false)
  const [editingSource, setEditingSource] = useState<SignalSource | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [testingConnection, setTestingConnection] = useState<number | null>(null)
  const [fetchingHistory, setFetchingHistory] = useState<number | null>(null)
  const [loadingDialogs, setLoadingDialogs] = useState(false)
  const [refreshingDialogs, setRefreshingDialogs] = useState(false)
  const [authenticating, setAuthenticating] = useState(false)
  const [dialogSearchQuery, setDialogSearchQuery] = useState('')
  const [selectedDialogId, setSelectedDialogId] = useState('')
  const [selectedDialogIds, setSelectedDialogIds] = useState<string[]>([])
  const [needsTwoFactor, setNeedsTwoFactor] = useState(false)
  const [codeSent, setCodeSent] = useState(false)
  const [savingDialogs, setSavingDialogs] = useState(false)
  const [showHistoryConfigDialog, setShowHistoryConfigDialog] = useState<HistoryConfigDialog>({
    isOpen: false,
    sourceId: 0,
    sourceName: ''
  })
  const [historyConfig, setHistoryConfig] = useState<HistoryConfig>({
    fetchType: 'days',
    daysBack: 30,
    messageLimit: 100,
    skipReplies: true
  })
  
  const [formData, setFormData] = useState<AddSourceForm>({
    name: '',
    telegram_chat_id: '',
    description: '',
    is_active: true
  })
  
  const [authData, setAuthData] = useState<TelegramAuthForm>({
    phone_number: '',
    code: '',
    password: ''
  })
  
  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialog>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    onCancel: () => {}
  })

  // 获取信号源列表
  const fetchSources = async () => {
    try {
      setLoading(true)
      const data = await sourceAPI.getSources(false)
      setSources(data)
      setError(null)
    } catch (err: any) {
      console.error('获取信号源失败:', err)
      setError(err.message || '获取信号源失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取Telegram用户信息
  const fetchTelegramUser = async () => {
    try {
      const response = await sourceAPI.getTelegramUser()
      if (response.success && response.user) {
        setTelegramUser(response.user)
      } else {
        setTelegramUser(null)
      }
    } catch (err: any) {
      console.error('获取Telegram用户失败:', err)
      setTelegramUser(null)
    }
  }

  // 获取Telegram对话列表
  const fetchTelegramDialogs = async () => {
    try {
      setLoadingDialogs(true)
      const response = await sourceAPI.getTelegramDialogs(undefined, 100)
      if (response.success) {
        setTelegramDialogs(response.dialogs)
        setFilteredDialogs(response.dialogs)
        setError(null)
      } else {
        setError(response.message || '获取对话列表失败')
      }
    } catch (err: any) {
      console.error('获取对话列表失败:', err)
      setError(err.message || '获取对话列表失败')
    } finally {
      setLoadingDialogs(false)
    }
  }

  // Telegram认证
  const handleTelegramAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setAuthenticating(true)
    
    try {
      const response = await sourceAPI.authenticateTelegram(authData)
      
      if (response.success) {
        if (response.code_sent) {
          setCodeSent(true)
          setError(null)
        } else if (response.two_factor_required) {
          setNeedsTwoFactor(true)
          setError(null)
        } else if (response.authenticated) {
          setTelegramUser(response.user_info || null)
          setShowTelegramAuth(false)
          resetAuthForm()
          await fetchTelegramDialogs()
        }
      } else if (response.two_factor_required) {
        // 需要两步验证也是正常流程
        setNeedsTwoFactor(true)
        setError(null)
      } else {
        setError(response.error || '认证失败')
        // 如果是验证码发送限制错误，重置状态让用户可以重新输入手机号
        if (response.error && response.error.includes('等待')) {
          setCodeSent(false)
          setNeedsTwoFactor(false)
        }
      }
    } catch (err: any) {
      console.error('Telegram认证失败:', err)
      setError(err.message || 'Telegram认证失败')
    } finally {
      setAuthenticating(false)
    }
  }

  // Telegram登出
  const handleTelegramLogout = async () => {
    try {
      const response = await sourceAPI.logoutTelegram()
      if (response.success) {
        setTelegramUser(null)
        setTelegramDialogs([])
        setFilteredDialogs([])
      } else {
        setError(response.error || '登出失败')
      }
    } catch (err: any) {
      console.error('Telegram登出失败:', err)
      setError(err.message || 'Telegram登出失败')
    }
  }

  // 重置认证表单
  const resetAuthForm = () => {
    setAuthData({
      phone_number: '',
      code: '',
      password: ''
    })
    setCodeSent(false)
    setNeedsTwoFactor(false)
    setError(null)
  }

  // 重新开始认证流程
  const restartAuth = async () => {
    try {
      // 重置后端Telegram客户端状态
      await sourceAPI.resetTelegram()
    } catch (err) {
      console.error('重置Telegram状态失败:', err)
    }
    
    // 重置前端状态
    resetAuthForm()
    setError(null)
  }

  // 刷新Telegram对话列表
  const refreshTelegramDialogs = async () => {
    try {
      setRefreshingDialogs(true)
      const response = await sourceAPI.refreshTelegramDialogs()
      if (response.success) {
        await fetchTelegramDialogs()
      } else {
        setError(response.message)
      }
    } catch (err: any) {
      console.error('刷新对话列表失败:', err)
      setError(err.message || '刷新对话列表失败')
    } finally {
      setRefreshingDialogs(false)
    }
  }

  // 搜索对话
  const handleDialogSearch = (query: string) => {
    setDialogSearchQuery(query)
    if (!query.trim()) {
      setFilteredDialogs(telegramDialogs)
      return
    }

    const filtered = telegramDialogs.filter(dialog => {
      const searchFields = [
        dialog.username || '',
        dialog.id,
        dialog.title,
        dialog.description || ''
      ]
      return searchFields.some(field => 
        field.toLowerCase().includes(query.toLowerCase())
      )
    })
    setFilteredDialogs(filtered)
  }

  // 选择对话
  const handleDialogSelect = (dialog: TelegramDialog) => {
    setSelectedDialogId(dialog.id)
    setFormData({
      ...formData,
      telegram_chat_id: dialog.id,
      name: dialog.title // 自动设置群组名称为信号源名称
    })
  }

  // 处理多选对话
  const handleDialogMultiSelect = (dialogId: string, checked: boolean) => {
    setSelectedDialogIds(prev => {
      if (checked) {
        return [...prev, dialogId]
      } else {
        return prev.filter(id => id !== dialogId)
      }
    })
  }

  // 全选/全不选
  const handleSelectAllDialogs = (checked: boolean) => {
    if (checked) {
      setSelectedDialogIds(filteredDialogs.map(d => d.id))
    } else {
      setSelectedDialogIds([])
    }
  }

  // 保存选择的对话为信号源
  const handleSaveSelectedDialogs = async () => {
    if (selectedDialogIds.length === 0) {
      setError('请至少选择一个对话')
      return
    }

    setSavingDialogs(true)
    try {
      const response = await sourceAPI.selectTelegramDialogs(selectedDialogIds)
      
      if (response.success) {
        setError(null)
        setSelectedDialogIds([])
        await fetchSources() // 刷新信号源列表
        
        // 显示成功消息
        setConfirmDialog({
          isOpen: true,
          title: '保存成功',
          message: response.message,
          type: 'success',
          onConfirm: () => setConfirmDialog(prev => ({ ...prev, isOpen: false })),
          onCancel: () => setConfirmDialog(prev => ({ ...prev, isOpen: false }))
        })
      } else {
        setError(response.message || '保存对话失败')
      }
    } catch (err: any) {
      console.error('保存对话失败:', err)
      setError(err.message || '保存对话失败')
    } finally {
      setSavingDialogs(false)
    }
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    
    try {
      if (editingSource) {
        await sourceAPI.updateSource(editingSource.id, formData)
      } else {
        await sourceAPI.createSource(formData)
      }
      
      await fetchSources()
      handleCancel()
      setError(null)
    } catch (err: any) {
      console.error('保存信号源失败:', err)
      setError(err.message || '保存信号源失败')
    } finally {
      setSubmitting(false)
    }
  }

  // 删除信号源
  const handleDelete = (sourceId: number, sourceName: string) => {
    setConfirmDialog({
      isOpen: true,
      title: '确认删除',
      message: `确定要删除信号源 "${sourceName}" 吗？此操作不可恢复。`,
      type: 'danger',
      onConfirm: async () => {
        try {
          await sourceAPI.deleteSource(sourceId)
          await fetchSources()
          setConfirmDialog(prev => ({ ...prev, isOpen: false }))
          setError(null)
        } catch (err: any) {
          console.error('删除信号源失败:', err)
          setError(err.message || '删除信号源失败')
        }
      },
      onCancel: () => setConfirmDialog(prev => ({ ...prev, isOpen: false }))
    })
  }

  // 切换状态
  const handleToggleStatus = async (source: SignalSource) => {
    try {
      await sourceAPI.updateSource(source.id, { is_active: !source.is_active })
      await fetchSources()
      setError(null)
    } catch (err: any) {
      console.error('切换状态失败:', err)
      setError(err.message || '切换状态失败')
    }
  }

  // 编辑信号源
  const handleEdit = (source: SignalSource) => {
    setEditingSource(source)
    setFormData({
      name: source.name,
      telegram_chat_id: source.telegram_chat_id,
      description: source.description || '',
      is_active: source.is_active
    })
    setSelectedDialogId(source.telegram_chat_id)
    setShowAddForm(true)
  }

  // 测试连接
  const handleTestConnection = async (sourceId: number) => {
    setTestingConnection(sourceId)
    try {
      const response = await sourceAPI.testSourceConnection(sourceId)
      if (response.success) {
        setError(null)
        // 可以显示成功消息
      } else {
        setError(response.message || '连接测试失败')
      }
    } catch (err: any) {
      console.error('测试连接失败:', err)
      setError(err.message || '测试连接失败')
    } finally {
      setTestingConnection(null)
    }
  }

  // 拉取历史信号
  const handleFetchHistory = async (sourceId: number, sourceName: string) => {
    if (!telegramUser) {
      setError('请先登录Telegram账户')
      return
    }

    // 显示配置弹窗
    setShowHistoryConfigDialog({
      isOpen: true,
      sourceId,
      sourceName
    })
  }

  // 执行拉取历史信号
  const handleExecuteFetchHistory = async () => {
    const { sourceId, sourceName } = showHistoryConfigDialog
    
    setFetchingHistory(sourceId)
    setShowHistoryConfigDialog(prev => ({ ...prev, isOpen: false }))
    
    try {
      const params: any = {
        skip_replies: historyConfig.skipReplies
      }

      if (historyConfig.fetchType === 'days') {
        // 只指定天数，拉取这期间内的所有消息
        params.days_back = historyConfig.daysBack
        params.limit = 10000 // 设置一个较大的值，让后端根据实际情况处理
      } else {
        // 指定消息数量限制
        params.limit = historyConfig.messageLimit
        params.days_back = 365 // 设置一个较大的天数范围
      }

      const response = await sourceAPI.fetchSourceHistory(sourceId, params)
      
      if (response.success) {
        setError(null)
        await fetchSources() // 刷新信号源列表以更新统计数据
        
        // 显示成功结果
        const fetchTypeText = historyConfig.fetchType === 'days' 
          ? `过去${historyConfig.daysBack}天内` 
          : `最近${historyConfig.messageLimit}条消息中`
        
        setConfirmDialog({
          isOpen: true,
          title: '拉取完成',
          message: `从信号源 "${sourceName}" ${fetchTypeText}成功拉取历史信号！\n\n📊 处理统计：\n• 处理消息：${response.data?.total_processed || 0} 条\n• 发现信号：${response.data?.signals_found || 0} 条\n• 跳过消息：${response.data?.skipped || 0} 条\n• 重复信号：${response.data?.duplicates || 0} 条`,
          type: 'success',
          onConfirm: () => setConfirmDialog(prev => ({ ...prev, isOpen: false })),
          onCancel: () => setConfirmDialog(prev => ({ ...prev, isOpen: false }))
        })
      } else {
        setError(response.message || '拉取历史信号失败')
      }
    } catch (err: any) {
      console.error('拉取历史信号失败:', err)
      setError(err.message || '拉取历史信号失败')
    } finally {
      setFetchingHistory(null)
    }
  }

  // 取消操作
  const handleCancel = () => {
    setShowAddForm(false)
    setEditingSource(null)
    setFormData({
      name: '',
      telegram_chat_id: '',
      description: '',
      is_active: true
    })
    setSelectedDialogId('')
  }

  // 显示添加表单
  const handleShowAddForm = () => {
    setShowAddForm(true)
    if (telegramUser && telegramDialogs.length === 0) {
      fetchTelegramDialogs()
    }
  }

  // 显示Telegram认证
  const handleShowTelegramAuth = () => {
    setShowTelegramAuth(true)
    resetAuthForm()
  }

  useEffect(() => {
    fetchSources()
    fetchTelegramUser()
  }, [])

  // 当检测到用户已登录时，自动获取对话列表
  useEffect(() => {
    if (telegramUser) {
      fetchTelegramDialogs()
    }
  }, [telegramUser])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载信号源数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* 顶部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200"
              >
                <ArrowLeftIcon className="h-5 w-5" />
                <span className="font-medium">返回仪表板</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">信号源管理</h1>
                  <p className="text-sm text-gray-500">管理Telegram信号源</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Telegram用户状态 */}
              {telegramUser ? (
                <div className="flex items-center space-x-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <UserIcon className="h-4 w-4 mr-1" />
                    {telegramUser.username || telegramUser.first_name || telegramUser.phone}
                  </div>
                  <button
                    onClick={handleTelegramLogout}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    登出
                  </button>
                </div>
              ) : (
                <button
                  onClick={handleShowTelegramAuth}
                  className="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <UserIcon className="h-4 w-4 mr-2" />
                  Telegram登录
                </button>
              )}
              
              <button
                onClick={handleShowAddForm}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                添加信号源
              </button>
            </div>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* 错误提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        {/* 自动登录成功提示 */}
        {telegramUser && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm text-green-800">
                  ✨ 已自动登录到Telegram账户，系统将监听您选择的群组消息
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 首次登录提示 */}
        {!telegramUser && (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex">
              <UserIcon className="h-5 w-5 text-blue-400" />
              <div className="ml-3">
                <p className="text-sm text-blue-800">
                  💡 首次使用需要登录Telegram账户，下次启动时会自动登录
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 信号源列表 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">信号源列表</h2>
            <p className="text-sm text-gray-500 mt-1">管理您的Telegram信号源</p>
          </div>
          
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50/50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    信号源信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    统计信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    可靠性
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sources.map((source) => {
                  const reliability = source.total_signals > 0 
                    ? Math.round((source.successful_signals / source.total_signals) * 100) 
                    : 0
                  
                  return (
                    <tr key={source.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{source.name}</div>
                          <div className="text-sm text-gray-500">ID: {source.telegram_chat_id}</div>
                          {source.description && (
                            <div className="text-sm text-gray-400 mt-1">{source.description}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          总信号: {source.total_signals}
                        </div>
                        <div className="text-sm text-green-600">
                          成功: {source.successful_signals}
                        </div>
                        <div className="text-sm text-red-600">
                          失败: {source.failed_signals}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getReliabilityColor(reliability)}`}>
                          {reliability}% {getReliabilityText(reliability)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handleToggleStatus(source)}
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors ${
                            source.is_active
                              ? 'bg-green-100 text-green-800 hover:bg-green-200'
                              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                          }`}
                        >
                          {source.is_active ? (
                            <>
                              <EyeIcon className="h-3 w-3 mr-1" />
                              活跃
                            </>
                          ) : (
                            <>
                              <EyeSlashIcon className="h-3 w-3 mr-1" />
                              停用
                            </>
                          )}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleTestConnection(source.id)}
                            disabled={testingConnection === source.id}
                            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                            title="测试连接"
                          >
                            {testingConnection === source.id ? (
                              <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full" />
                            ) : (
                              <WifiIcon className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleFetchHistory(source.id, source.name)}
                            disabled={fetchingHistory === source.id || !telegramUser}
                            className="text-green-600 hover:text-green-900 disabled:opacity-50"
                            title={!telegramUser ? "请先登录Telegram账户" : "拉取历史信号"}
                          >
                            {fetchingHistory === source.id ? (
                              <div className="animate-spin h-4 w-4 border-2 border-green-600 border-t-transparent rounded-full" />
                            ) : (
                              <DocumentArrowDownIcon className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleEdit(source)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="编辑"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(source.id, source.name)}
                            className="text-red-600 hover:text-red-900"
                            title="删除"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
            
            {sources.length === 0 && (
              <div className="text-center py-12">
                <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">暂无信号源</h3>
                <p className="mt-1 text-sm text-gray-500">开始添加您的第一个Telegram信号源</p>
                <div className="mt-6">
                  <button
                    onClick={handleShowAddForm}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <PlusIcon className="h-5 w-5 mr-2" />
                    添加信号源
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Telegram认证弹窗 */}
      {showTelegramAuth && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Telegram登录</h3>
              <button
                onClick={() => setShowTelegramAuth(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            
            {/* 错误提示 */}
            {error && (
              <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                    {error.includes('等待') && (
                      <button
                        type="button"
                        onClick={() => restartAuth()}
                        className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                      >
                        重新开始认证
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            <form onSubmit={handleTelegramAuth} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  手机号码 <span className="text-red-500">*</span>
                </label>
                <div className="mt-1 relative">
                  <PhoneIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="tel"
                    value={authData.phone_number}
                    onChange={(e) => setAuthData({...authData, phone_number: e.target.value})}
                    placeholder="+86 138 0013 8000"
                    className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    required
                    disabled={codeSent && !(error && error.includes('等待'))}
                  />
                </div>
              </div>

              {codeSent && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    验证码 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={authData.code}
                    onChange={(e) => setAuthData({...authData, code: e.target.value})}
                    placeholder="请输入短信验证码"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    required
                  />
                </div>
              )}

              {needsTwoFactor && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    两步验证密码 <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1 relative">
                    <KeyIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="password"
                      value={authData.password}
                      onChange={(e) => setAuthData({...authData, password: e.target.value})}
                      placeholder="请输入两步验证密码"
                      className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
              )}

              <div className="flex justify-between pt-4">
                <div>
                  {(codeSent || needsTwoFactor) && (
                                         <button
                       type="button"
                       onClick={() => restartAuth()}
                       className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 underline"
                     >
                       重新开始
                     </button>
                  )}
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowTelegramAuth(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={authenticating}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                  >
                    {authenticating ? (
                      <>
                        <div className="animate-spin -ml-1 mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full inline-block" />
                        处理中...
                      </>
                    ) : needsTwoFactor ? '登录' : codeSent ? '验证' : '发送验证码'}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 添加/编辑信号源弹窗 */}
      {showAddForm && telegramUser && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingSource ? '编辑信号源' : '添加信号源'}
                </h3>
                <button
                  onClick={handleCancel}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>
            
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* 基本信息 */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    信号源名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      选择Telegram对话 <span className="text-red-500">*</span>
                    </label>
                    <button
                      type="button"
                      onClick={refreshTelegramDialogs}
                      disabled={refreshingDialogs}
                      className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                    >
                      <ArrowPathIcon className={`h-4 w-4 mr-1 ${refreshingDialogs ? 'animate-spin' : ''}`} />
                      刷新对话列表
                    </button>
                  </div>
                  
                  {/* 搜索框 */}
                  <div className="relative mb-3">
                    <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      value={dialogSearchQuery}
                      onChange={(e) => handleDialogSearch(e.target.value)}
                      placeholder="搜索对话ID、名称或描述..."
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>



                  {/* 对话列表 */}
                  <div className="border border-gray-300 rounded-lg max-h-60 overflow-y-auto">
                    {loadingDialogs ? (
                      <div className="p-4 text-center">
                        <div className="animate-spin h-6 w-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                        <p className="text-sm text-gray-500">加载对话列表中...</p>
                      </div>
                    ) : filteredDialogs.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">
                        <p className="text-sm">
                          {dialogSearchQuery ? '未找到匹配的对话' : '暂无可用对话'}
                        </p>
                        {!dialogSearchQuery && (
                          <button
                            type="button"
                            onClick={fetchTelegramDialogs}
                            className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
                          >
                            重新加载
                          </button>
                        )}
                      </div>
                    ) : (
                      filteredDialogs.map((dialog) => (
                        <div
                          key={dialog.id}
                          className={`p-3 border-b border-gray-200 last:border-b-0 hover:bg-gray-50 ${
                            selectedDialogId === dialog.id ? 'bg-blue-50 border-blue-200' : ''
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3 flex-1 min-w-0">
                              {/* 单选按钮 */}
                              <input
                                type="radio"
                                name="selectedDialog"
                                checked={selectedDialogId === dialog.id}
                                onChange={() => handleDialogSelect(dialog)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                              />
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center">
                                  <div className="flex-1">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                      {dialog.title}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      ID: {dialog.id}
                                    </p>
                                    {dialog.username && (
                                      <p className="text-xs text-gray-400">
                                        @{dialog.username}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="ml-3 flex-shrink-0">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {dialog.type}
                              </span>
                              {dialog.member_count && (
                                <div className="text-xs text-gray-500 mt-1">
                                  {dialog.member_count} 成员
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  {/* 手动输入选项 */}
                  <div className="mt-3">
                    <button
                      type="button"
                      onClick={() => {
                        const manualId = prompt('请输入Telegram对话ID (例如: -1001234567890):')
                        if (manualId) {
                          setSelectedDialogId(manualId)
                          setFormData({...formData, telegram_chat_id: manualId})
                        }
                      }}
                      className="text-sm text-gray-600 hover:text-gray-800 underline"
                    >
                      手动输入对话ID
                    </button>
                  </div>

                  {/* 已选择的对话显示 */}
                  {selectedDialogId && (
                    <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="text-sm text-blue-800">
                        <span className="font-medium">已选择:</span> {selectedDialogId}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    描述（可选）
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    rows={3}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="添加信号源的描述信息..."
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                    启用信号源
                  </label>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={submitting || !formData.telegram_chat_id}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {submitting ? (
                    <>
                      <div className="animate-spin -ml-1 mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full inline-block" />
                      保存中...
                    </>
                  ) : editingSource ? '更新' : '添加'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 历史信号配置弹窗 */}
      {showHistoryConfigDialog.isOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <ClockIcon className="h-6 w-6 text-blue-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">拉取历史信号</h3>
              </div>
              <button
                onClick={() => setShowHistoryConfigDialog(prev => ({ ...prev, isOpen: false }))}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-4">
                从信号源 <span className="font-medium">"{showHistoryConfigDialog.sourceName}"</span> 拉取历史信号
              </p>
              
              {/* 拉取方式选择 */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">拉取方式</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="fetchType"
                        value="days"
                        checked={historyConfig.fetchType === 'days'}
                        onChange={(e) => setHistoryConfig(prev => ({ ...prev, fetchType: e.target.value as 'days' | 'count' }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">按时间范围（拉取指定天数内的所有信号）</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="fetchType"
                        value="count"
                        checked={historyConfig.fetchType === 'count'}
                        onChange={(e) => setHistoryConfig(prev => ({ ...prev, fetchType: e.target.value as 'days' | 'count' }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">按消息数量（拉取指定数量的最新消息）</span>
                    </label>
                  </div>
                </div>

                {/* 天数输入 */}
                {historyConfig.fetchType === 'days' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      时间范围（天）
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="365"
                      value={historyConfig.daysBack}
                      onChange={(e) => setHistoryConfig(prev => ({ ...prev, daysBack: parseInt(e.target.value) || 1 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="输入天数"
                    />
                    <p className="text-xs text-gray-500 mt-1">拉取过去指定天数内的所有消息（1-365天）</p>
                  </div>
                )}

                {/* 消息数量输入 */}
                {historyConfig.fetchType === 'count' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      消息数量
                    </label>
                    <input
                      type="number"
                      min="10"
                      max="1000"
                      value={historyConfig.messageLimit}
                      onChange={(e) => setHistoryConfig(prev => ({ ...prev, messageLimit: parseInt(e.target.value) || 10 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="输入消息数量"
                    />
                    <p className="text-xs text-gray-500 mt-1">拉取最新的指定数量消息（10-1000条）</p>
                  </div>
                )}

                {/* 过滤选项 */}
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={historyConfig.skipReplies}
                      onChange={(e) => setHistoryConfig(prev => ({ ...prev, skipReplies: e.target.checked }))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">跳过回复消息（推荐）</span>
                  </label>
                  <p className="text-xs text-gray-500 mt-1 ml-6">只拉取原始信号消息，过滤掉回复和转发消息</p>
                </div>
              </div>
            </div>

            {/* 警告提示 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
                <div className="ml-3">
                  <p className="text-sm text-yellow-800">
                    <strong>注意：</strong>拉取大量历史消息可能需要较长时间，建议从小范围开始测试。已存在的信号不会重复添加。
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowHistoryConfigDialog(prev => ({ ...prev, isOpen: false }))}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handleExecuteFetchHistory}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                开始拉取
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 确认对话框 */}
      {confirmDialog.isOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              {confirmDialog.type === 'success' ? (
                <CheckIcon className="h-6 w-6 text-green-600 mr-3" />
              ) : confirmDialog.type === 'info' ? (
                <InformationCircleIcon className="h-6 w-6 text-blue-600 mr-3" />
              ) : (
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-3" />
              )}
              <h3 className="text-lg font-medium text-gray-900">{confirmDialog.title}</h3>
            </div>
            <p className="text-sm text-gray-500 mb-6 whitespace-pre-line">{confirmDialog.message}</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={confirmDialog.onCancel}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={confirmDialog.onConfirm}
                className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                  confirmDialog.type === 'success' ? 'bg-green-600 hover:bg-green-700' :
                  confirmDialog.type === 'info' ? 'bg-blue-600 hover:bg-blue-700' :
                  'bg-red-600 hover:bg-red-700'
                }`}
              >
                {confirmDialog.type === 'success' ? '确定' :
                 confirmDialog.type === 'info' ? '确认' :
                 '确认删除'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 