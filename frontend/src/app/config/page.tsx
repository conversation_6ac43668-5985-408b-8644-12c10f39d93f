'use client'

import { useState, useEffect } from 'react'
import { API_BASE_URL } from '@/lib/config'
import { 
  CogIcon, 
  CheckIcon, 
  XMarkIcon, 
  ExclamationTriangleIcon,
  ArrowPathIcon,
  ArrowLeftIcon,
  EyeIcon,
  EyeSlashIcon,
  ComputerDesktopIcon,
  ChatBubbleLeftRightIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  Cog8ToothIcon,
  SparklesIcon,
  CheckCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface ConfigItem {
  id: number
  key: string
  value?: string
  description?: string
  category: string
  config_type: string
  is_required: boolean
  is_sensitive: boolean
  validation_rules?: string
  sort_order: number
}

interface ConfigCategory {
  category: string
  configs: ConfigItem[]
}

interface ConfigValidationError {
  key: string
  error: string
  current_value?: string
}

const categoryNames = {
  telegram: 'Telegram配置',
  freqtrade: 'Freqtrade配置',
  exchange: '交易所配置',
  logging: '日志配置',
  trading: '交易配置',
  system: '系统配置',
  llm: 'LLM配置'
}

const categoryIcons = {
  telegram: ChatBubbleLeftRightIcon,
  freqtrade: Cog8ToothIcon,
  exchange: CurrencyDollarIcon,
  logging: DocumentTextIcon,
  trading: SparklesIcon,
  system: ComputerDesktopIcon,
  llm: SparklesIcon
}

const categoryColors = {
  telegram: 'from-blue-500 to-cyan-500',
  freqtrade: 'from-emerald-500 to-teal-500',
  exchange: 'from-yellow-500 to-orange-500',
  logging: 'from-purple-500 to-indigo-500',
  trading: 'from-pink-500 to-rose-500',
  system: 'from-gray-500 to-slate-500',
  llm: 'from-violet-500 to-purple-500'
}

export default function ConfigPage() {
  const [categories, setCategories] = useState<ConfigCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [errors, setErrors] = useState<ConfigValidationError[]>([])
  const [success, setSuccess] = useState(false)
  const [showSensitive, setShowSensitive] = useState<Record<string, boolean>>({})
  const [configValues, setConfigValues] = useState<Record<string, string>>({})
  const [activeCategory, setActiveCategory] = useState<string>('')
  const [autoSaveTimeouts, setAutoSaveTimeouts] = useState<Record<string, NodeJS.Timeout>>({})
  const [savingStates, setSavingStates] = useState<Record<string, boolean>>({})
  const [saveSuccessStates, setSaveSuccessStates] = useState<Record<string, boolean>>({})

  useEffect(() => {
    fetchConfigs()
  }, [])

  const fetchConfigs = async () => {
    try {
      setLoading(true)
      const response = await fetch(`${API_BASE_URL}/api/v1/config/categories?include_sensitive=true`)
      if (response.ok) {
        const data = await response.json()
        setCategories(data)
        
        // 初始化配置值
        const values: Record<string, string> = {}
        data.forEach((category: ConfigCategory) => {
          category.configs.forEach((config: ConfigItem) => {
            values[config.key] = config.value || ''
          })
        })
        setConfigValues(values)
        
        // 设置默认激活的分类
        if (data.length > 0 && !activeCategory) {
          setActiveCategory(data[0].category)
        }
      } else {
        console.error('Failed to fetch configs')
      }
    } catch (error) {
      console.error('Error fetching configs:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleConfigChange = (key: string, value: string) => {
    setConfigValues(prev => ({
      ...prev,
      [key]: value
    }))
    // 清除相关错误
    setErrors(prev => prev.filter(error => error.key !== key))
    
    // 清除之前的自动保存超时器
    if (autoSaveTimeouts[key]) {
      clearTimeout(autoSaveTimeouts[key])
    }
    
    // 设置新的自动保存超时器（1.5秒后保存）
    const timeoutId = setTimeout(() => {
      saveIndividualConfig(key, value)
      setAutoSaveTimeouts(prev => {
        const newTimeouts = { ...prev }
        delete newTimeouts[key]
        return newTimeouts
      })
    }, 1500)
    
    setAutoSaveTimeouts(prev => ({
      ...prev,
      [key]: timeoutId
    }))
  }

  const saveIndividualConfig = async (key: string, value: string) => {
    try {
      // 设置保存中状态
      setSavingStates(prev => ({ ...prev, [key]: true }))
      setSaveSuccessStates(prev => ({ ...prev, [key]: false }))
      
      const params = new URLSearchParams({
        value: value,
        reason: '单项配置自动保存'
      })
      
      const response = await fetch(`${API_BASE_URL}/api/v1/config/${key}?${params}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (response.ok) {
        console.log(`配置 ${key} 自动保存成功`)
        // 显示成功状态
        setSaveSuccessStates(prev => ({ ...prev, [key]: true }))
        // 清除可能的错误
        setErrors(prev => prev.filter(e => e.key !== key))
        
        // 3秒后清除成功状态
        setTimeout(() => {
          setSaveSuccessStates(prev => ({ ...prev, [key]: false }))
        }, 3000)
      } else {
        const errorData = await response.json()
        console.error(`配置 ${key} 自动保存失败:`, errorData)
        // 设置错误状态
        setErrors(prev => [...prev.filter(e => e.key !== key), {
          key: key,
          error: errorData.detail || '保存失败',
          current_value: value
        }])
      }
    } catch (error) {
      console.error(`配置 ${key} 自动保存异常:`, error)
      setErrors(prev => [...prev.filter(e => e.key !== key), {
        key: key,
        error: '网络错误',
        current_value: value
      }])
    } finally {
      // 清除保存中状态
      setSavingStates(prev => ({ ...prev, [key]: false }))
    }
  }

  const toggleSensitiveVisibility = (key: string) => {
    setShowSensitive(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  const validateConfigs = async () => {
    try {
      console.log('开始验证配置，发送数据：', configValues)
      
      const response = await fetch(`${API_BASE_URL}/api/v1/config/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(configValues)
      })

      console.log('验证响应状态：', response.status)

      if (response.ok) {
        const result = await response.json()
        console.log('验证结果详情：', result)
        return {
          isValid: result.is_valid,
          errors: result.errors || []
        }
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.log('验证失败，响应数据：', errorData)
        return {
          isValid: false,
          errors: [{ key: 'system', error: '验证请求失败' }]
        }
      }
    } catch (error) {
      console.error('Error validating configs:', error)
      return {
        isValid: false,
        errors: [{ key: 'system', error: '验证请求异常' }]
      }
    }
  }

  const saveConfigs = async () => {
    setSaving(true)
    setErrors([])
    setSuccess(false)

    try {
      console.log('开始保存配置，配置值：', configValues)
      
      // 先验证配置
      const validationResult = await validateConfigs()
      console.log('验证结果：', validationResult)
      
      if (!validationResult.isValid) {
        console.log('验证失败，错误信息：', validationResult.errors)
        setErrors(validationResult.errors)
        setSaving(false)
        return
      }

      console.log('验证通过，开始保存配置')
      
      // 保存配置
      const response = await fetch(`${API_BASE_URL}/api/v1/config/bulk-update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          configs: configValues,
          change_reason: '通过配置页面更新'
        })
      })

      console.log('保存响应状态：', response.status)
      
      if (response.ok) {
        console.log('配置保存成功')
        setSuccess(true)
        setTimeout(() => setSuccess(false), 3000)
        // 重新获取配置以确保同步
        await fetchConfigs()
      } else {
        const errorData = await response.json()
        console.log('保存失败，错误信息：', errorData)
        setErrors([{ key: 'system', error: errorData.detail || '保存失败' }])
      }
    } catch (error) {
      console.error('Error saving configs:', error)
      setErrors([{ key: 'system', error: '网络错误，请重试' }])
    } finally {
      setSaving(false)
    }
  }

  const renderConfigInput = (config: ConfigItem) => {
    const value = configValues[config.key] || ''
    const hasError = errors.some(error => error.key === config.key)
    const configError = errors.find(error => error.key === config.key)

    let inputElement

    switch (config.config_type) {
      case 'boolean':
        inputElement = (
          <div className="relative">
            <select
              className={`form-select appearance-none bg-white ${hasError ? 'border-red-300 ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'} rounded-lg transition-all duration-200`}
              value={value}
              onChange={(e) => handleConfigChange(config.key, e.target.value)}
            >
              <option value="false">❌ 否</option>
              <option value="true">✅ 是</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        )
        break

      case 'integer':
      case 'float':
        inputElement = (
          <div className="relative">
            <input
              type="number"
              step={config.config_type === 'float' ? '0.01' : '1'}
              className={`form-input ${hasError ? 'border-red-300 ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'} rounded-lg transition-all duration-200`}
              value={value}
              onChange={(e) => handleConfigChange(config.key, e.target.value)}
              placeholder={config.description}
            />
            <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
              <span className="text-gray-400 text-sm">
                {config.config_type === 'float' ? '0.00' : '123'}
              </span>
            </div>
          </div>
        )
        break

      case 'password':
        inputElement = (
          <div className="relative">
            <input
              type={showSensitive[config.key] ? 'text' : 'password'}
              className={`form-input pr-12 ${hasError ? 'border-red-300 ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'} rounded-lg transition-all duration-200`}
              value={value}
              onChange={(e) => handleConfigChange(config.key, e.target.value)}
              placeholder={config.description}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200"
              onClick={() => toggleSensitiveVisibility(config.key)}
            >
              {showSensitive[config.key] ? (
                <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              )}
            </button>
          </div>
        )
        break

      case 'list':
        inputElement = (
          <textarea
            className={`form-textarea ${hasError ? 'border-red-300 ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'} rounded-lg transition-all duration-200 resize-none`}
            rows={3}
            value={value}
            onChange={(e) => handleConfigChange(config.key, e.target.value)}
            placeholder="用逗号分隔多个值，例如：value1,value2,value3"
          />
        )
        break

      default:
        inputElement = (
          <input
            type="text"
            className={`form-input ${hasError ? 'border-red-300 ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'} rounded-lg transition-all duration-200`}
            value={value}
            onChange={(e) => handleConfigChange(config.key, e.target.value)}
            placeholder={config.description}
          />
        )
    }

    return (
      <div key={config.key} className="group">
        <div className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-all duration-200 hover:border-gray-300">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <label className="block text-sm font-semibold text-gray-900 mb-1">
                {config.description || config.key}
                {config.is_required && <span className="text-red-500 ml-1">*</span>}
                {config.is_sensitive && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <ShieldCheckIcon className="w-3 h-3 mr-1" />
                    敏感
                  </span>
                )}
              </label>
              <p className="text-xs text-gray-500 mb-3">{config.key}</p>
            </div>
            
            {/* 保存状态指示器 */}
            <div className="flex items-center space-x-2">
              {savingStates[config.key] && (
                <div className="flex items-center space-x-1 text-blue-600">
                  <ArrowPathIcon className="w-4 h-4 animate-spin" />
                  <span className="text-xs">保存中...</span>
                </div>
              )}
              {saveSuccessStates[config.key] && (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircleIcon className="w-4 h-4" />
                  <span className="text-xs">已保存</span>
                </div>
              )}
            </div>
          </div>
          
          {inputElement}
          
          {configError && (
            <div className="mt-3 flex items-start space-x-2">
              <ExclamationCircleIcon className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-red-600">{configError.error}</p>
            </div>
          )}
          
          {config.validation_rules && (
            <div className="mt-3 flex items-start space-x-2">
              <ExclamationTriangleIcon className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
              <p className="text-xs text-gray-500">
                验证规则: {config.validation_rules}
              </p>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 mx-auto mb-4">
              <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
              <div className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent animate-spin"></div>
            </div>
          </div>
          <p className="text-gray-600 font-medium">正在加载配置...</p>
          <p className="text-sm text-gray-500 mt-1">请稍候</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* 现代化顶部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200">
                <ArrowLeftIcon className="h-5 w-5" />
                <span className="font-medium">返回仪表板</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
                  <CogIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">系统配置</h1>
                  <p className="text-sm text-gray-500">管理系统运行参数</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={fetchConfigs}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                disabled={loading}
              >
                <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                <span>刷新</span>
              </button>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <CheckCircleIcon className="h-4 w-4 text-green-500" />
                <span>配置自动保存</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* 成功提示 */}
        {success && (
          <div className="mb-6 transform transition-all duration-300 ease-in-out">
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 shadow-lg">
              <div className="flex items-center">
                <CheckCircleIcon className="h-6 w-6 text-green-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800">
                    配置保存成功！
                  </p>
                  <p className="text-xs text-green-600 mt-1">
                    所有更改已应用到系统
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 系统错误 */}
        {errors.some(error => error.key === 'system') && (
          <div className="mb-6 transform transition-all duration-300 ease-in-out">
            <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-4 shadow-lg">
              <div className="flex items-center">
                <XMarkIcon className="h-6 w-6 text-red-500" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    保存失败
                  </h3>
                  <div className="mt-1 text-sm text-red-700">
                    {errors.find(error => error.key === 'system')?.error}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 配置分类导航 */}
        <div className="mb-8">
          <nav className="flex space-x-2 overflow-x-auto pb-2">
            {categories.map((category) => {
              const IconComponent = categoryIcons[category.category as keyof typeof categoryIcons]
              const isActive = activeCategory === category.category
              return (
                <button
                  key={category.category}
                  onClick={() => setActiveCategory(category.category)}
                  className={`flex-shrink-0 flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActive
                      ? 'bg-white text-blue-600 shadow-lg border border-blue-200'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                  }`}
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{categoryNames[category.category as keyof typeof categoryNames]}</span>
                  <span className={`text-xs px-2 py-0.5 rounded-full ${
                    isActive ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'
                  }`}>
                    {category.configs.length}
                  </span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* 配置内容 */}
        <div className="space-y-8">
          {categories
            .filter(category => !activeCategory || category.category === activeCategory)
            .map((category) => {
              const IconComponent = categoryIcons[category.category as keyof typeof categoryIcons]
              const colorClass = categoryColors[category.category as keyof typeof categoryColors]
              
              return (
                <div key={category.category} className="transform transition-all duration-300 ease-in-out">
                  <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
                    <div className={`bg-gradient-to-r ${colorClass} px-6 py-4`}>
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-white/20 rounded-lg">
                          <IconComponent className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h2 className="text-xl font-bold text-white">
                            {categoryNames[category.category as keyof typeof categoryNames]}
                          </h2>
                          <p className="text-white/80 text-sm">
                            {category.configs.length} 个配置项
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {category.configs.map(renderConfigInput)}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
        </div>
      </main>
    </div>
  )
} 