import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from './providers'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Telegram信号交易系统',
  description: '基于Telegram群组信号的自动化交易系统',
  keywords: ['交易', 'Telegram', '信号', '自动化', 'Freqtrade'],
  authors: [{ name: 'Trading System Team' }],
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#0ea5e9',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // 注入运行时配置
  const runtimeConfig = {
    API_BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  }

  return (
    <html lang="zh-CN" suppressHydrationWarning={true}>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `window.__RUNTIME_CONFIG__ = ${JSON.stringify(runtimeConfig)};`,
          }}
        />
      </head>
      <body className={`${inter.className} antialiased`}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
} 