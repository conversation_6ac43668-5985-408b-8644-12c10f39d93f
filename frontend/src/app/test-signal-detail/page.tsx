'use client'

import { useState } from 'react'
import TradingViewChart from '@/components/TradingViewChart'
import SignalParametersCard from '@/components/SignalParametersCard'
import { 
  ArrowLeftIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

export default function TestSignalDetailPage() {
  const [selectedSignal, setSelectedSignal] = useState('btc')

  // 测试信号数据
  const testSignals = {
    btc: {
      symbol: 'BTCUSDT',
      signal_type: 'LONG',
      entry_price: 45000,
      stop_loss: 43000,
      take_profit: 48000,
      leverage: 3,
      signal_time: new Date().toISOString()
    },
    eth: {
      symbol: 'ETHUSDT',
      signal_type: 'SHORT',
      entry_price: 2800,
      stop_loss: 2950,
      take_profit: 2600,
      leverage: 5,
      signal_time: new Date().toISOString()
    },
    sol: {
      symbol: 'SOLUSDT',
      signal_type: 'LONG',
      entry_price: 180,
      stop_loss: 170,
      take_profit: 200,
      leverage: 2,
      signal_time: new Date().toISOString()
    }
  }

  const currentSignal = testSignals[selectedSignal as keyof typeof testSignals]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">信号详情测试页面</h1>
                <p className="text-sm text-gray-500">测试止损点显示和K线图集成</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 信号选择器 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">选择测试信号</h2>
          </div>
          <div className="px-6 py-4">
            <div className="flex space-x-4">
              {Object.entries(testSignals).map(([key, signal]) => (
                <button
                  key={key}
                  onClick={() => setSelectedSignal(key)}
                  className={`px-4 py-2 rounded-lg border transition-colors ${
                    selectedSignal === key
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {signal.symbol} ({signal.signal_type})
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 基本信息 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">基本信息</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <p className="text-sm text-gray-500">交易对</p>
                <p className="text-lg font-semibold text-gray-900">{currentSignal.symbol}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">信号类型</p>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  currentSignal.signal_type === 'LONG' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {currentSignal.signal_type === 'LONG' ? '做多' : '做空'}
                </span>
              </div>
              <div>
                <p className="text-sm text-gray-500">入场价格</p>
                <p className="text-lg font-semibold text-blue-600">${currentSignal.entry_price}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">止损价格</p>
                <p className="text-lg font-semibold text-red-600">${currentSignal.stop_loss}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">止盈价格</p>
                <p className="text-lg font-semibold text-green-600">${currentSignal.take_profit}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">杠杆倍数</p>
                <p className="text-lg font-semibold text-purple-600">{currentSignal.leverage}x</p>
              </div>
            </div>
          </div>
        </div>

        {/* 信号参数分析 */}
        <SignalParametersCard signal={currentSignal} />

        {/* K线图表 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <ChartBarIcon className="h-5 w-5 mr-2 text-blue-600" />
              K线图表分析
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              图表上已标记入场价格、止损点、止盈点和信号时间
            </p>
          </div>
          <div className="px-6 py-4">
            <TradingViewChart
              symbol={currentSignal.symbol}
              entryPrice={currentSignal.entry_price}
              stopLoss={currentSignal.stop_loss}
              takeProfit={currentSignal.take_profit}
              signalType={currentSignal.signal_type}
              signalTime={currentSignal.signal_time}
              height={600}
              theme="light"
            />
          </div>
        </div>

        {/* 功能说明 */}
        <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">✨ 新功能说明</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-blue-800 mb-2">1. 止损点显示</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• ✅ 在基本信息中显示止损价格</li>
                <li>• ✅ 在参数分析卡片中详细展示</li>
                <li>• ✅ 计算风险百分比</li>
                <li>• ✅ 风险收益比分析</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-blue-800 mb-2">2. K线图集成</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• ✅ TradingView专业K线图</li>
                <li>• ✅ 自动标记入场、止损、止盈点</li>
                <li>• ✅ 信号时间垂直线标记</li>
                <li>• ✅ 风险收益比可视化</li>
              </ul>
            </div>
          </div>

          <div className="mt-4 p-4 bg-white rounded border border-blue-300">
            <h4 className="font-semibold text-blue-800 mb-2">📊 图表标记说明</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-blue-700">入场点标记</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-1 bg-red-500"></div>
                <span className="text-red-700">止损线</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-1 bg-green-500"></div>
                <span className="text-green-700">止盈线</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-1 h-3 bg-blue-400"></div>
                <span className="text-blue-600">信号时间</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
