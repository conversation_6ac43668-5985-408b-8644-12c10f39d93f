'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeftIcon, 
  ChartBarIcon, 
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationCircleIcon,
  BoltIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'
import { getApiBaseUrl } from '@/lib/config'
import TradingViewChart from '@/components/TradingViewChart'
import SignalParametersCard from '@/components/SignalParametersCard'

interface SignalDetail {
  signal: {
    id: number
    symbol: string
    signal_type: string
    entry_price?: number
    stop_loss?: number
    take_profit?: number
    leverage?: number
    status: string
    source_id: number
    signal_time: string
    received_at: string
    processed_at?: string
    raw_message: string
  }
  entry_points: Array<{
    id: number
    price: number
    allocation: number
    description: string
    status: string
    filled_quantity: number
    actual_price?: number
    order_id?: string
    created_at: string
    filled_at?: string
  }>
  take_profit_points: Array<{
    id: number
    price: number
    allocation: number
    description: string
    status: string
    filled_quantity: number
    actual_price?: number
    order_id?: string
    pnl?: number
    pnl_percentage?: number
    created_at: string
    filled_at?: string
  }>
  trades: Array<{
    id: number
    trade_id: string
    order_id: string
    symbol: string
    side: string
    entry_price: number
    exit_price?: number
    quantity: number
    pnl?: number
    pnl_percentage?: number
    fee?: number
    status: string
    entry_time?: string
    exit_time?: string
  }>
  statistics: {
    total_pnl: number
    total_entry_filled: number
    total_tp_filled: number
    entry_points_count: number
    tp_points_count: number
    trades_count: number
  }
}

// 状态颜色映射
function getStatusColor(status: string) {
  switch (status) {
    case 'executed':
    case 'completed':
    case 'filled':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'processing':
    case 'active':
    case 'partial':
      return 'bg-blue-100 text-blue-800'
    case 'failed':
      return 'bg-red-100 text-red-800'
    case 'cancelled':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'executed':
      return '已执行'
    case 'completed':
      return '已完成'
    case 'filled':
      return '已成交'
    case 'pending':
      return '等待中'
    case 'processing':
      return '处理中'
    case 'active':
      return '活跃'
    case 'partial':
      return '部分成交'
    case 'failed':
      return '失败'
    case 'cancelled':
      return '已取消'
    default:
      return status
  }
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'executed':
    case 'completed':
    case 'filled':
      return <CheckCircleIcon className="h-5 w-5 text-green-600" />
    case 'pending':
      return <ClockIcon className="h-5 w-5 text-yellow-600" />
    case 'processing':
    case 'active':
    case 'partial':
      return <ExclamationCircleIcon className="h-5 w-5 text-blue-600" />
    case 'failed':
      return <XCircleIcon className="h-5 w-5 text-red-600" />
    case 'cancelled':
      return <XCircleIcon className="h-5 w-5 text-gray-600" />
    default:
      return <ClockIcon className="h-5 w-5 text-gray-600" />
  }
}

function getSignalTypeColor(type: string) {
  switch (type.toLowerCase()) {
    case 'long':
    case 'buy':
      return 'bg-green-100 text-green-800'
    case 'short':
    case 'sell':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

function getSignalTypeText(type: string) {
  switch (type.toLowerCase()) {
    case 'long':
      return '做多'
    case 'short':
      return '做空'
    case 'buy':
      return '买入'
    case 'sell':
      return '卖出'
    default:
      return type
  }
}

export default function SignalDetailPage() {
  const params = useParams()
  const signalId = parseInt(params.id as string)
  const [signalDetail, setSignalDetail] = useState<SignalDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSignalDetail = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`${getApiBaseUrl()}/api/v1/signal-management/${signalId}/detail`)
      
      if (!response.ok) {
        throw new Error('获取信号详情失败')
      }
      
      const result = await response.json()
      
      if (result.success) {
        setSignalDetail(result.data)
      } else {
        setError(result.message || '获取信号详情失败')
      }
    } catch (err: any) {
      console.error('获取信号详情失败:', err)
      setError(err.message || '获取信号详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (signalId) {
      fetchSignalDetail()
    }
  }, [signalId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载信号详情中...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <XCircleIcon className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <div className="space-x-4">
            <button
              onClick={fetchSignalDetail}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              重试
            </button>
            <Link
              href="/signals/management"
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              返回列表
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (!signalDetail) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationCircleIcon className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">信号不存在</h2>
          <p className="text-gray-600 mb-4">找不到指定的信号记录</p>
          <Link
            href="/signals/management"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回列表
          </Link>
        </div>
      </div>
    )
  }

  const { signal, entry_points, take_profit_points, trades, statistics } = signalDetail

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link 
                href="/signals/management" 
                className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5" />
                <span>返回信号管理</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                  <BoltIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">信号详情</h1>
                  <p className="text-sm text-gray-500">#{signal.id} - {signal.symbol}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 基本信息卡片 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">基本信息</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="flex items-center space-x-3">
                <ChartBarIcon className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-500">交易对</p>
                  <p className="text-lg font-semibold text-gray-900">{signal.symbol}</p>
                </div>
              </div>
              
                             <div className="flex items-center space-x-3">
                 {signal.signal_type.toLowerCase() === 'long' ? (
                   <ArrowUpIcon className="h-8 w-8 text-green-600" />
                 ) : (
                   <ArrowDownIcon className="h-8 w-8 text-red-600" />
                 )}
                <div>
                  <p className="text-sm text-gray-500">信号类型</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSignalTypeColor(signal.signal_type)}`}>
                    {getSignalTypeText(signal.signal_type)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                {getStatusIcon(signal.status)}
                <div>
                  <p className="text-sm text-gray-500">状态</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(signal.status)}`}>
                    {getStatusText(signal.status)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-500">杠杆</p>
                  <p className="text-lg font-semibold text-gray-900">{signal.leverage}x</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <ClockIcon className="h-8 w-8 text-gray-600" />
                <div>
                  <p className="text-sm text-gray-500">信号时间</p>
                  <p className="text-sm text-gray-900">{new Date(signal.signal_time).toLocaleString()}</p>
                </div>
              </div>
              
              {signal.entry_price && (
                <div className="flex items-center space-x-3">
                  <ArrowUpIcon className="h-8 w-8 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-500">入场价格</p>
                    <p className="text-lg font-semibold text-gray-900">${signal.entry_price}</p>
                  </div>
                </div>
              )}

              {signal.stop_loss && (
                <div className="flex items-center space-x-3">
                  <XCircleIcon className="h-8 w-8 text-red-600" />
                  <div>
                    <p className="text-sm text-gray-500">止损价格</p>
                    <p className="text-lg font-semibold text-red-600">${signal.stop_loss}</p>
                  </div>
                </div>
              )}

              {signal.take_profit && (
                <div className="flex items-center space-x-3">
                  <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-500">止盈价格</p>
                    <p className="text-lg font-semibold text-green-600">${signal.take_profit}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-3">
                <BoltIcon className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-500">总盈亏</p>
                  <p className={`text-lg font-semibold ${statistics.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {statistics.total_pnl >= 0 ? '+' : ''}{(statistics.total_pnl || 0).toFixed(2)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 信号参数分析 */}
        <SignalParametersCard signal={signal} />

        {/* K线图表 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <ChartBarIcon className="h-5 w-5 mr-2 text-blue-600" />
              K线图表分析
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              图表上已标记入场价格、止损点、止盈点和信号时间
            </p>
          </div>
          <div className="px-6 py-4">
            <TradingViewChart
              symbol={signal.symbol}
              entryPrice={signal.entry_price}
              stopLoss={signal.stop_loss}
              takeProfit={signal.take_profit}
              signalType={signal.signal_type}
              signalTime={signal.signal_time}
              height={600}
              theme="light"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* 入场点 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                入场点 ({entry_points.length})
              </h3>
            </div>
            <div className="px-6 py-4">
              {entry_points.length > 0 ? (
                <div className="space-y-4">
                  {entry_points.map((ep) => (
                    <div key={ep.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg font-semibold text-gray-900">${ep.price}</span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ep.status)}`}>
                            {getStatusText(ep.status)}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">{(ep.allocation * 100).toFixed(1)}%</span>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <div>已成交数量: {ep.filled_quantity}</div>
                        {ep.actual_price && <div>实际价格: ${ep.actual_price}</div>}
                        {ep.description && <div>描述: {ep.description}</div>}
                        {ep.filled_at && <div>成交时间: {new Date(ep.filled_at).toLocaleString()}</div>}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">暂无入场点</p>
              )}
            </div>
          </div>

          {/* 止盈点 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                止盈点 ({take_profit_points.length})
              </h3>
            </div>
            <div className="px-6 py-4">
              {take_profit_points.length > 0 ? (
                <div className="space-y-4">
                  {take_profit_points.map((tp) => (
                    <div key={tp.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg font-semibold text-gray-900">${tp.price}</span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(tp.status)}`}>
                            {getStatusText(tp.status)}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">{(tp.allocation * 100).toFixed(1)}%</span>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <div>已成交数量: {tp.filled_quantity}</div>
                        {tp.actual_price && <div>实际价格: ${tp.actual_price}</div>}
                        {tp.pnl !== undefined && tp.pnl !== null && (
                          <div className={`font-medium ${tp.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            盈亏: {tp.pnl >= 0 ? '+' : ''}{tp.pnl.toFixed(2)}
                          </div>
                        )}
                        {tp.pnl_percentage !== undefined && tp.pnl_percentage !== null && (
                          <div className={`font-medium ${tp.pnl_percentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            盈亏比例: {tp.pnl_percentage >= 0 ? '+' : ''}{tp.pnl_percentage.toFixed(2)}%
                          </div>
                        )}
                        {tp.description && <div>描述: {tp.description}</div>}
                        {tp.filled_at && <div>成交时间: {new Date(tp.filled_at).toLocaleString()}</div>}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">暂无止盈点</p>
              )}
            </div>
          </div>
        </div>

        {/* 交易记录 */}
        {trades.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                交易记录 ({trades.length})
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      交易ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      方向
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      入场价格
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      出场价格
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      数量
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      盈亏
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {trades.map((trade) => (
                    <tr key={trade.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {trade.trade_id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          trade.side === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {trade.side === 'buy' ? '买入' : '卖出'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${trade.entry_price}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {trade.exit_price ? `$${trade.exit_price}` : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {trade.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {trade.pnl !== undefined && trade.pnl !== null ? (
                          <span className={`font-medium ${trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {trade.pnl >= 0 ? '+' : ''}{trade.pnl.toFixed(2)}
                          </span>
                        ) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(trade.status)}`}>
                          {getStatusText(trade.status)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* 统计信息 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">统计信息</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{statistics.entry_points_count}</p>
                <p className="text-sm text-gray-500">入场点数量</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{statistics.tp_points_count}</p>
                <p className="text-sm text-gray-500">止盈点数量</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{statistics.trades_count}</p>
                <p className="text-sm text-gray-500">交易记录</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{statistics.total_entry_filled}</p>
                <p className="text-sm text-gray-500">总入场数量</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{statistics.total_tp_filled}</p>
                <p className="text-sm text-gray-500">总止盈数量</p>
              </div>
              <div className="text-center">
                <p className={`text-2xl font-bold ${statistics.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {statistics.total_pnl >= 0 ? '+' : ''}{(statistics.total_pnl || 0).toFixed(2)}
                </p>
                <p className="text-sm text-gray-500">总盈亏</p>
              </div>
            </div>
          </div>
        </div>

        {/* 原始消息 */}
        {signal.raw_message && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">原始消息</h3>
            </div>
            <div className="px-6 py-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                  {signal.raw_message}
                </pre>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 