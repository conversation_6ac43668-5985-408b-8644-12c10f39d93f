'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { signalAPI, TradeEditData, EditableTradeEntryPoint, EditableTradeTakeProfitPoint } from '@/lib/api'
import { API_BASE_URL } from '@/lib/config'
import Link from 'next/link'
import { ArrowLeftIcon, PlusIcon, TrashIcon, AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline'
import { X } from 'lucide-react'

interface EntryPoint {
  id?: number
  price: number
  allocation: number
  description: string
  editable?: boolean
  status: string
}

interface TakeProfitPoint {
  id?: number
  price: number
  allocation: number
  description: string
  editable?: boolean
  status: string
}

interface AdvancedTradeConfig {
  symbol: string
  signal_type: string
  entry_points: EntryPoint[]
  auto_distribute_funds: boolean
  take_profit_points: TakeProfitPoint[]
  stop_loss: number | null
  total_quantity: number | null
  leverage: number
  raw_message: string
  description: string
}

// 状态颜色映射函数
const getStatusColor = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-400'
    case 'filled':
      return 'bg-green-500'
    case 'cancelled':
      return 'bg-gray-400'
    default:
      return 'bg-red-400'
  }
}

// 状态文本映射函数
const getStatusText = (status: string): string => {
  switch (status) {
    case 'pending':
      return '待成交'
    case 'filled':
      return '已成交'
    case 'cancelled':
      return '已取消'
    default:
      return status
  }
}

export default function AdvancedTradePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const editSignalId = searchParams.get('edit')
  const isEditMode = !!editSignalId
  
  const [loading, setLoading] = useState(false)
  const [loadingEditData, setLoadingEditData] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [editData, setEditData] = useState<TradeEditData | null>(null)
  
  // 文本解析相关状态
  const [showImportSection, setShowImportSection] = useState(false)
  const [importText, setImportText] = useState('')
  const [parsing, setParsing] = useState(false)
  const [parseResult, setParseResult] = useState<any>(null)

  const [config, setConfig] = useState<AdvancedTradeConfig>({
    symbol: 'BTCUSDT',
    signal_type: 'long',
    entry_points: [
      { price: 0, allocation: 1.0, description: '入场点 1', status: 'pending' }
    ],
    auto_distribute_funds: true,
    take_profit_points: [
      { price: 0, allocation: 1.0, description: '止盈点 1', status: 'pending' }
    ],
    stop_loss: null,
    total_quantity: null,
    leverage: 1,
    raw_message: '',
    description: ''
  })

  // 加载编辑数据
  const loadEditData = async (signalId: string) => {
    try {
      setLoadingEditData(true)
      setError(null)
      
      const response = await signalAPI.getTradeEditData(parseInt(signalId))
      
      if (response.success && response.data) {
        const data = response.data
        setEditData(data)
        
        // 将编辑数据转换为config格式
        const entryPoints: EntryPoint[] = data.entry_points.map(ep => ({
          id: ep.id,
          price: ep.price,
          allocation: ep.allocation,
          description: ep.description || `入场点 ${ep.id}`,
          editable: ep.editable,
          status: ep.status
        }))
        
        const takeProfitPoints: TakeProfitPoint[] = data.take_profit_points.map(tp => ({
          id: tp.id,
          price: tp.price,
          allocation: tp.allocation,
          description: tp.description || `止盈点 ${tp.id}`,
          editable: tp.editable,
          status: tp.status
        }))
        
        setConfig({
          symbol: data.symbol,
          signal_type: data.signal_type,
          entry_points: entryPoints,
          auto_distribute_funds: false, // 编辑模式下关闭自动分配
          take_profit_points: takeProfitPoints,
          stop_loss: data.stop_loss,
          total_quantity: null,
          leverage: data.leverage,
          raw_message: data.raw_message,
          description: ''
        })
      } else {
        setError('获取编辑数据失败')
      }
    } catch (err: any) {
      console.error('加载编辑数据失败:', err)
      setError(err.message || '加载编辑数据失败')
    } finally {
      setLoadingEditData(false)
    }
  }

  // 检查字段是否可编辑
  const isFieldEditable = (field: string): boolean => {
    if (!isEditMode || !editData) return true
    return editData.editable_fields[field as keyof typeof editData.editable_fields] || false
  }

  // 添加入场点
  const addEntryPoint = () => {
    // 编辑模式下只能添加可编辑的入场点
    if (isEditMode && !isFieldEditable('entry_points')) {
      setError('无法添加新的入场点，因为已有入场点已成交')
      return
    }
    
    const newEntry: EntryPoint = {
      price: 0,
      allocation: 0,
      description: `入场点 ${config.entry_points.length + 1}`,
      editable: true,
      status: 'pending'
    }
    setConfig(prev => ({
      ...prev,
      entry_points: [...prev.entry_points, newEntry]
    }))
  }

  // 删除入场点
  const removeEntryPoint = async (index: number) => {
    if (config.entry_points.length <= 1) return
    
    const entryPoint = config.entry_points[index]
    // 检查是否可编辑
    if (isEditMode && entryPoint.editable === false) {
      setError('无法删除已成交的入场点')
      return
    }
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/signals/${editSignalId}/entry_points/${entryPoint.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        setConfig(prev => ({
          ...prev,
          entry_points: prev.entry_points.filter((_, i) => i !== index)
        }))
      } else {
        setError('删除失败，请重试')
      }
    } catch (err: any) {
      console.error('删除入场点失败:', err)
      setError('删除失败，请重试')
    }
  }

  // 更新入场点
  const updateEntryPoint = (index: number, field: keyof EntryPoint, value: any) => {
    const entryPoint = config.entry_points[index]
    // 检查是否可编辑
    if (isEditMode && entryPoint.editable === false) {
      setError('无法修改已成交的入场点')
      return
    }
    
    setConfig(prev => ({
      ...prev,
      entry_points: prev.entry_points.map((entry, i) => 
        i === index ? { ...entry, [field]: value } : entry
      )
    }))
  }

  // 添加止盈点
  const addTakeProfitPoint = () => {
    // 编辑模式下只能添加可编辑的止盈点
    if (isEditMode && !isFieldEditable('take_profit_points')) {
      setError('无法添加新的止盈点，因为已有止盈点已成交')
      return
    }
    
    const newTp: TakeProfitPoint = {
      price: 0,
      allocation: 0,
      description: `止盈点 ${config.take_profit_points.length + 1}`,
      editable: true,
      status: 'pending'
    }
    setConfig(prev => ({
      ...prev,
      take_profit_points: [...prev.take_profit_points, newTp]
    }))
  }

  // 删除止盈点
  const removeTakeProfitPoint = async (index: number) => {
    if (config.take_profit_points.length <= 1) return
    
    const takeProfitPoint = config.take_profit_points[index]
    // 检查是否可编辑
    if (isEditMode && takeProfitPoint.editable === false) {
      setError('无法删除已成交的止盈点')
      return
    }
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/signals/${editSignalId}/take_profit_points/${takeProfitPoint.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        setConfig(prev => ({
          ...prev,
          take_profit_points: prev.take_profit_points.filter((_, i) => i !== index)
        }))
      } else {
        setError('删除失败，请重试')
      }
    } catch (err: any) {
      console.error('删除止盈点失败:', err)
      setError('删除失败，请重试')
    }
  }

  // 更新止盈点
  const updateTakeProfitPoint = (index: number, field: keyof TakeProfitPoint, value: any) => {
    const takeProfitPoint = config.take_profit_points[index]
    // 检查是否可编辑
    if (isEditMode && takeProfitPoint.editable === false) {
      setError('无法修改已成交的止盈点')
      return
    }
    
    setConfig(prev => ({
      ...prev,
      take_profit_points: prev.take_profit_points.map((tp, i) => 
        i === index ? { ...tp, [field]: value } : tp
      )
    }))
  }

  // 自动分配资金
  const autoDistributeEntryFunds = () => {
    // 编辑模式下只分配可编辑的入场点
    let editableEntries = config.entry_points
    if (isEditMode) {
      editableEntries = config.entry_points.filter(ep => ep.editable !== false)
    }
    
    const count = editableEntries.length
    if (count === 0) return
    
    const allocation = 1.0 / count
    setConfig(prev => ({
      ...prev,
      entry_points: prev.entry_points.map(entry => ({
        ...entry,
        allocation: (isEditMode && entry.editable === false) ? entry.allocation : parseFloat(allocation.toFixed(4))
      }))
    }))
  }

  // 自动分配止盈
  const autoDistributeTakeProfit = () => {
    // 编辑模式下只分配可编辑的止盈点
    let editableTps = config.take_profit_points
    if (isEditMode) {
      editableTps = config.take_profit_points.filter(tp => tp.editable !== false)
    }
    
    const count = editableTps.length
    if (count === 0) return
    
    const allocation = 1.0 / count
    setConfig(prev => ({
      ...prev,
      take_profit_points: prev.take_profit_points.map(tp => ({
        ...tp,
        allocation: (isEditMode && tp.editable === false) ? tp.allocation : parseFloat(allocation.toFixed(4))
      }))
    }))
  }

  // 验证配置
  const validateConfig = (): string | null => {
    if (!config.symbol.trim()) {
      return '请输入交易对'
    }

    // 验证入场点
    for (let i = 0; i < config.entry_points.length; i++) {
      const entry = config.entry_points[i]
      if (entry.price <= 0) {
        return `入场点 ${i + 1} 的价格必须大于0`
      }
      if (entry.allocation < 0 || entry.allocation > 1) {
        return `入场点 ${i + 1} 的资金分配必须在0-1之间`
      }
    }

    // 验证入场点分配总和
    const totalEntryAllocation = config.entry_points.reduce((sum, entry) => sum + entry.allocation, 0)
    if (Math.abs(totalEntryAllocation - 1.0) > 0.001) {
      return `入场点资金分配总和必须为100%，当前为${(totalEntryAllocation * 100).toFixed(1)}%`
    }

    // 验证止盈点
    for (let i = 0; i < config.take_profit_points.length; i++) {
      const tp = config.take_profit_points[i]
      if (tp.price <= 0) {
        return `止盈点 ${i + 1} 的价格必须大于0`
      }
      if (tp.allocation < 0 || tp.allocation > 1) {
        return `止盈点 ${i + 1} 的分配比例必须在0-1之间`
      }
    }

    // 验证止盈点分配总和
    const totalTpAllocation = config.take_profit_points.reduce((sum, tp) => sum + tp.allocation, 0)
    if (Math.abs(totalTpAllocation - 1.0) > 0.001) {
      return `止盈点分配总和必须为100%，当前为${(totalTpAllocation * 100).toFixed(1)}%`
    }

    return null
  }

  // 提交配置
  const handleSubmit = async () => {
    const validationError = validateConfig()
    if (validationError) {
      setError(validationError)
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log(`${isEditMode ? '更新' : '创建'}交易请求数据:`, config)
      
      // 准备提交数据
      const entryPointsToSubmit = config.entry_points.map(ep => {
        const entryData: any = {
          price: ep.price,
          allocation: ep.allocation,
          description: ep.description
        }
        
        // 如果有id字段，说明是现有记录，需要包含id
        if (ep.id !== undefined && ep.id !== null) {
          entryData.id = ep.id
        }
        
        return entryData
      })
      
      const takeProfitPointsToSubmit = config.take_profit_points.map(tp => {
        const tpData: any = {
          price: tp.price,
          allocation: tp.allocation,
          description: tp.description
        }
        
        // 如果有id字段，说明是现有记录，需要包含id
        if (tp.id !== undefined && tp.id !== null) {
          tpData.id = tp.id
        }
        
        return tpData
      })
      
      const requestData = {
        symbol: config.symbol,
        signal_type: config.signal_type,
        entry_points: entryPointsToSubmit,
        take_profit_points: takeProfitPointsToSubmit,
        stop_loss: config.stop_loss,
        leverage: config.leverage,
        raw_message: config.raw_message,
        description: config.description
      }
      
      console.log('发送到后端的数据:', requestData)
      
      let response
      if (isEditMode) {
        response = await signalAPI.updateTrade(parseInt(editSignalId!), requestData)
      } else {
        response = await signalAPI.createTrade(requestData)
      }
      
      console.log('后端响应:', response)
      
      if (response.success) {
        setSuccess(`交易${isEditMode ? '更新' : '创建'}成功！`)
        setTimeout(() => {
          router.push('/signals')
        }, 2000)
      } else {
        setError(response.message || `${isEditMode ? '更新' : '创建'}失败`)
      }
    } catch (err: any) {
      console.error(`${isEditMode ? '更新' : '创建'}交易失败:`, err)
      setError(err.response?.data?.detail || err.message || `${isEditMode ? '更新' : '创建'}失败`)
    } finally {
      setLoading(false)
    }
  }

  // 加载模板
  const loadTemplate = async () => {
    try {
      const response = await signalAPI.getAdvancedTradeTemplate()
      if (response.success) {
        setConfig(response.data)
      }
    } catch (err: any) {
      setError(err.message || '加载模板失败')
    }
  }

  // 从文本解析并导入配置
  const handleImportFromText = async () => {
    if (!importText.trim()) {
      setError('请输入交易信号文本')
      return
    }

    try {
      setParsing(true)
      const response = await signalAPI.parseSignalText(importText)
      
      if (response.success && response.data?.parsed) {
        // 将解析结果填充到表单
        const signalData = response.data.signal_data
        
        // 处理多个入场点
        let entryPoints: EntryPoint[] = []
        
        // 检查是否有 entry_price_range（优先级最高）
        if (signalData.entry_price_range && Array.isArray(signalData.entry_price_range) && signalData.entry_price_range.length > 0) {
          // 处理入场价格区间
          entryPoints = signalData.entry_price_range.map((price: number, index: number) => ({
            price: price,
            allocation: 1.0 / signalData.entry_price_range.length,
            description: `入场点 ${index + 1}`,
            status: 'pending'
          }))
        } else if (signalData.entry_prices && Array.isArray(signalData.entry_prices) && signalData.entry_prices.length > 0) {
          // 处理多个入场价格
          entryPoints = signalData.entry_prices.map((price: number, index: number) => ({
            price: price,
            allocation: 1.0 / signalData.entry_prices.length,
            description: `入场点 ${index + 1}`,
            status: 'pending'
          }))
        } else if (signalData.entry_price) {
          // 单个入场价格
          entryPoints = [{
            price: signalData.entry_price,
            allocation: 1.0,
            description: '主要入场点',
            status: 'pending'
          }]
        } else {
          // 保持现有配置
          entryPoints = config.entry_points
        }

        // 处理多个止盈点
        let takeProfitPoints: TakeProfitPoint[] = []
        
        // 检查是否有 take_profit_targets 数组（优先级最高）
        if (signalData.take_profit_targets && Array.isArray(signalData.take_profit_targets) && signalData.take_profit_targets.length > 0) {
          // 多个止盈目标
          takeProfitPoints = signalData.take_profit_targets.map((price: number, index: number) => ({
            price: price,
            allocation: 1.0 / signalData.take_profit_targets.length,
            description: `止盈点 ${index + 1}`,
            status: 'pending'
          }))
        } else if (signalData.take_profit) {
          // 单个止盈价格
          takeProfitPoints = [{
            price: signalData.take_profit,
            allocation: 1.0,
            description: '主要止盈点',
            status: 'pending'
          }]
        } else {
          // 保持现有配置
          takeProfitPoints = config.take_profit_points
        }

        setConfig(prev => ({
          ...prev,
          symbol: signalData.symbol || prev.symbol,
          signal_type: signalData.signal_type || prev.signal_type,
          entry_points: entryPoints,
          auto_distribute_funds: entryPoints.length > 1, // 如果有多个入场点，自动开启平均分配
          take_profit_points: takeProfitPoints,
          stop_loss: signalData.stop_loss || prev.stop_loss,
          total_quantity: signalData.quantity || prev.total_quantity,
          leverage: signalData.leverage || prev.leverage,
          raw_message: importText
        }))
        
        setParseResult(response.data)
        setError(null)
        setShowImportSection(false)
        
        // 显示成功信息
        const entryCount = entryPoints.length
        const tpCount = takeProfitPoints.length
        setSuccess(`解析成功！导入了 ${entryCount} 个入场点和 ${tpCount} 个止盈点`)
        setTimeout(() => setSuccess(null), 3000)
        
      } else {
        setParseResult(null)
        setError(response.message || '无法解析该文本为有效交易信号')
      }
    } catch (err: any) {
      setError(err.message || '解析失败')
      setParseResult(null)
    } finally {
      setParsing(false)
    }
  }

  // 清空导入内容
  const clearImport = () => {
    setImportText('')
    setParseResult(null)
    setShowImportSection(false)
  }

  // 加载编辑数据
  useEffect(() => {
    if (isEditMode && editSignalId) {
      loadEditData(editSignalId)
    }
  }, [isEditMode, editSignalId])

  // 监听自动分配开关
  useEffect(() => {
    if (config.auto_distribute_funds) {
      autoDistributeEntryFunds()
    }
  }, [config.auto_distribute_funds, config.entry_points.length])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* 头部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/signals" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200">
                <ArrowLeftIcon className="h-5 w-5" />
                <span className="font-medium">返回交易管理</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                  <AdjustmentsHorizontalIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {isEditMode ? '编辑交易' : '创建交易'}
                  </h1>
                  <p className="text-sm text-gray-500">
                    {isEditMode ? '修改多入场价和多止盈点的交易配置' : '支持多入场价和多止盈点的高级交易配置'}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {!isEditMode && (
                <>
                  <button
                    onClick={() => setShowImportSection(!showImportSection)}
                    className="px-4 py-2 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    {showImportSection ? '关闭导入' : '文本导入'}
                  </button>
                  <button
                    onClick={loadTemplate}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    加载模板
                  </button>
                </>
              )}
              <button
                onClick={handleSubmit}
                disabled={loading || loadingEditData}
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {loading ? 
                  (isEditMode ? '更新中...' : '创建中...') : 
                  (isEditMode ? '更新交易' : '创建交易')
                }
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 加载编辑数据指示器 */}
        {loadingEditData && (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  正在加载交易数据...
                </h3>
              </div>
            </div>
          </div>
        )}

        {/* 错误和成功提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  {error}
                </h3>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">
                  {success}
                </h3>
              </div>
            </div>
          </div>
        )}

        {/* 文本导入区域 */}
        {showImportSection && (
          <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">从文本导入交易信号</h3>
              <button
                onClick={clearImport}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                清空
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  交易信号文本
                </label>
                <textarea
                  value={importText}
                  onChange={(e) => setImportText(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="粘贴交易信号文本，例如：&#10;BTCUSDT LONG&#10;入场: 45000&#10;止损: 43000&#10;止盈: 47000&#10;杠杆: 10x"
                />
              </div>
              
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleImportFromText}
                  disabled={parsing || !importText.trim()}
                  className="px-4 py-2 bg-purple-600 text-white font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {parsing ? '解析中...' : '解析并导入'}
                </button>
                
                {parseResult && (
                  <div className="flex items-center text-sm text-green-600">
                    <span>✓ 解析成功，已导入配置</span>
                  </div>
                )}
              </div>
              
              {parseResult && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                  <h4 className="text-sm font-medium text-green-800 mb-2">解析结果预览：</h4>
                  <div className="text-sm text-green-700 space-y-1">
                    <div>交易对: {parseResult.signal_data?.symbol || '未识别'}</div>
                    <div>类型: {parseResult.signal_data?.signal_type || '未识别'}</div>
                    
                    {/* 显示入场价格 */}
                    {parseResult.signal_data?.entry_price_range && Array.isArray(parseResult.signal_data.entry_price_range) ? (
                      <div>入场价格区间: {parseResult.signal_data.entry_price_range.join(', ')}</div>
                    ) : parseResult.signal_data?.entry_prices && Array.isArray(parseResult.signal_data.entry_prices) ? (
                      <div>入场价格: {parseResult.signal_data.entry_prices.join(', ')}</div>
                    ) : parseResult.signal_data?.entry_price ? (
                      <div>入场价: {parseResult.signal_data.entry_price}</div>
                    ) : (
                      <div>入场价: 未识别</div>
                    )}
                    
                    {/* 显示止盈价格 */}
                    {parseResult.signal_data?.take_profit_targets && Array.isArray(parseResult.signal_data.take_profit_targets) ? (
                      <div>止盈目标: {parseResult.signal_data.take_profit_targets.join(', ')}</div>
                    ) : parseResult.signal_data?.take_profit ? (
                      <div>止盈价: {parseResult.signal_data.take_profit}</div>
                    ) : (
                      <div>止盈价: 未识别</div>
                    )}
                    
                    <div>止损价: {parseResult.signal_data?.stop_loss || '未识别'}</div>
                    <div>杠杆: {parseResult.signal_data?.leverage || 1}x</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：基本配置 */}
          <div className="space-y-6">
            {/* 基本信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    交易对 <span className="text-red-500">*</span>
                    {isEditMode && !isFieldEditable('symbol') && (
                      <span className="text-xs text-gray-500 ml-2">(不可修改)</span>
                    )}
                  </label>
                  <input
                    type="text"
                    value={config.symbol}
                    onChange={(e) => setConfig(prev => ({ ...prev, symbol: e.target.value.toUpperCase() }))}
                    disabled={isEditMode && !isFieldEditable('symbol')}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isEditMode && !isFieldEditable('symbol') ? 'bg-gray-100 cursor-not-allowed' : ''
                    }`}
                    placeholder="例如：BTCUSDT"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    交易类型
                    {isEditMode && !isFieldEditable('signal_type') && (
                      <span className="text-xs text-gray-500 ml-2">(不可修改)</span>
                    )}
                  </label>
                  <select
                    value={config.signal_type}
                    onChange={(e) => setConfig(prev => ({ ...prev, signal_type: e.target.value }))}
                    disabled={isEditMode && !isFieldEditable('signal_type')}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isEditMode && !isFieldEditable('signal_type') ? 'bg-gray-100 cursor-not-allowed' : ''
                    }`}
                  >
                    <option value="long">做多 (LONG)</option>
                    <option value="short">做空 (SHORT)</option>
                    <option value="buy">买入 (BUY)</option>
                    <option value="sell">卖出 (SELL)</option>
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      总交易数量
                    </label>
                    <input
                      type="number"
                      step="0.001"
                      value={config.total_quantity || ''}
                      onChange={(e) => setConfig(prev => ({ ...prev, total_quantity: e.target.value ? parseFloat(e.target.value) : null }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0.0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      杠杆倍数
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="125"
                      value={config.leverage}
                      onChange={(e) => setConfig(prev => ({ ...prev, leverage: parseInt(e.target.value) || 1 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      止损价格
                    </label>
                    <input
                      type="number"
                      step="0.00001"
                      value={config.stop_loss || ''}
                      onChange={(e) => setConfig(prev => ({ ...prev, stop_loss: e.target.value ? parseFloat(e.target.value) : null }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0.0"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    原始信号文本
                  </label>
                  <textarea
                    value={config.raw_message}
                    onChange={(e) => setConfig(prev => ({ ...prev, raw_message: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="原始交易信号文本或备注信息..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    描述
                  </label>
                  <textarea
                    value={config.description}
                    onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="交易配置的详细描述..."
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 右侧：入场点和止盈点配置 */}
          <div className="space-y-6">
            {/* 入场点配置 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">入场点配置</h3>
                <div className="flex items-center space-x-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.auto_distribute_funds}
                      onChange={(e) => setConfig(prev => ({ ...prev, auto_distribute_funds: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">自动分配</span>
                  </label>
                  {!config.auto_distribute_funds && (
                    <button
                      onClick={autoDistributeEntryFunds}
                      className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
                    >
                      平均分配
                    </button>
                  )}
                  <button
                    onClick={addEntryPoint}
                    className="p-1 text-blue-600 hover:text-blue-800"
                  >
                    <PlusIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="space-y-4">
                {config.entry_points.map((point, index) => (
                  <div
                    key={point.id || `new-${index}`}
                    className={`p-4 border border-gray-200 rounded-lg ${
                      !point.editable ? 'bg-gray-50' : ''
                    }`}
                  >
                    <div className="flex items-center gap-4">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(point.status)}`} title={getStatusText(point.status)} />
                      
                      <input
                        type="number"
                        value={point.price}
                        onChange={(e) => {
                          if (point.editable) {
                            const updatedPoints = [...config.entry_points];
                            updatedPoints[index].price = parseFloat(e.target.value) || 0;
                            setConfig(prev => ({ ...prev, entry_points: updatedPoints }));
                          }
                        }}
                        step="0.00001"
                        className="flex-1 p-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                        placeholder="价格"
                        disabled={!point.editable}
                      />
                      
                      <input
                        type="number"
                        value={point.allocation * 100}
                        onChange={(e) => {
                          if (point.editable) {
                            const updatedPoints = [...config.entry_points];
                            updatedPoints[index].allocation = (parseFloat(e.target.value) || 0) / 100;
                            setConfig(prev => ({ ...prev, entry_points: updatedPoints }));
                          }
                        }}
                        step="0.1"
                        className="w-24 p-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                        placeholder="0"
                        disabled={!point.editable}
                      />
                      <span className="text-sm text-gray-500">%</span>
                      
                      <input
                        type="text"
                        value={point.description}
                        onChange={(e) => {
                          if (point.editable) {
                            const updatedPoints = [...config.entry_points];
                            updatedPoints[index].description = e.target.value;
                            setConfig(prev => ({ ...prev, entry_points: updatedPoints }));
                          }
                        }}
                        className="flex-1 p-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                        placeholder="描述"
                        disabled={!point.editable}
                      />
                      
                      {point.editable && (
                        <button
                          onClick={async () => {
                            if (point.id) {
                              // 有ID的记录，调用删除API
                              try {
                                const response = await fetch(`${API_BASE_URL}/api/v1/signals/${editSignalId}/entry_points/${point.id}`, {
                                  method: 'DELETE',
                                  headers: {
                                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                                  }
                                });
                                
                                if (response.ok) {
                                  // 从本地状态中移除这个入场点
                                  setConfig(prev => ({
                                    ...prev,
                                    entry_points: prev.entry_points.filter((_, i) => i !== index)
                                  }));
                                } else {
                                  alert('删除失败，请重试');
                                }
                              } catch (error) {
                                console.error('删除入场点失败:', error);
                                alert('删除失败，请重试');
                              }
                            } else {
                              // 没有ID的新记录，直接从本地状态移除
                              setConfig(prev => ({
                                ...prev,
                                entry_points: prev.entry_points.filter((_, i) => i !== index)
                              }));
                            }
                          }}
                          className="text-red-600 hover:text-red-800"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-3 text-sm text-gray-600">
                总分配: {(config.entry_points.reduce((sum, entry) => sum + entry.allocation, 0) * 100).toFixed(1)}%
              </div>
            </div>

            {/* 止盈点配置 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">止盈点配置</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={autoDistributeTakeProfit}
                    className="px-3 py-1 text-sm text-green-600 hover:text-green-800"
                  >
                    平均分配
                  </button>
                  <button
                    onClick={addTakeProfitPoint}
                    className="p-1 text-green-600 hover:text-green-800"
                  >
                    <PlusIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="space-y-4">
                {config.take_profit_points.map((tp, index) => {
                  const isTpEditable = !isEditMode || tp.editable !== false
                  return (
                    <div key={index} className={`flex items-center space-x-3 p-3 rounded-lg ${
                      isTpEditable ? 'bg-green-50' : 'bg-red-50 border border-red-200'
                    }`}>
                      {/* 状态指示器 */}
                      {isEditMode && (
                        <div className="flex flex-col items-center">
                          <div className={`w-3 h-3 rounded-full ${
                            tp.status === 'pending' ? 'bg-yellow-400' : 
                            tp.status === 'filled' ? 'bg-green-500' :
                            tp.status === 'cancelled' ? 'bg-gray-400' : 'bg-red-400'
                          }`} title={
                            tp.status === 'pending' ? '待成交' :
                            tp.status === 'filled' ? '已成交' :
                            tp.status === 'cancelled' ? '已取消' : tp.status
                          }></div>
                          <span className="text-xs text-gray-500 mt-1">
                            {tp.status === 'pending' ? '待成交' :
                             tp.status === 'filled' ? '已成交' :
                             tp.status === 'cancelled' ? '已取消' : tp.status}
                          </span>
                        </div>
                      )}
                      
                      <div className="flex-1">
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          价格 {!isTpEditable && (
                            <span className={`${
                              tp.status === 'filled' ? 'text-green-600' :
                              tp.status === 'cancelled' ? 'text-gray-600' : 'text-red-600'
                            }`}>
                              ({tp.status === 'pending' ? '待成交' :
                                tp.status === 'filled' ? '已成交' :
                                tp.status === 'cancelled' ? '已取消' : tp.status})
                            </span>
                          )}
                        </label>
                        <input
                          type="number"
                          step="0.00001"
                          value={tp.price || ''}
                          onChange={(e) => updateTakeProfitPoint(index, 'price', parseFloat(e.target.value) || 0)}
                          disabled={!isTpEditable}
                          className={`w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 ${
                            !isTpEditable ? 'bg-gray-100 cursor-not-allowed' : ''
                          }`}
                        />
                      </div>
                      <div className="w-24">
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          比例 (%)
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          value={(tp.allocation * 100).toFixed(1)}
                          onChange={(e) => updateTakeProfitPoint(index, 'allocation', (parseFloat(e.target.value) || 0) / 100)}
                          disabled={!isTpEditable}
                          className={`w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 ${
                            !isTpEditable ? 'bg-gray-100 cursor-not-allowed' : ''
                          }`}
                        />
                      </div>
                      <div className="flex-1">
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          描述
                        </label>
                        <input
                          type="text"
                          value={tp.description}
                          onChange={(e) => updateTakeProfitPoint(index, 'description', e.target.value)}
                          disabled={!isTpEditable}
                          className={`w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 ${
                            !isTpEditable ? 'bg-gray-100 cursor-not-allowed' : ''
                          }`}
                        />
                      </div>
                      {config.take_profit_points.length > 1 && isTpEditable && (
                        <button
                          onClick={() => removeTakeProfitPoint(index)}
                          className="p-1 text-red-600 hover:text-red-800"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  )
                })}
              </div>

              <div className="mt-3 text-sm text-gray-600">
                总比例: {(config.take_profit_points.reduce((sum, tp) => sum + tp.allocation, 0) * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 