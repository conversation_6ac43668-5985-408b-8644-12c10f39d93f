'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { 
  ArrowLeftIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  TrashIcon,
  EyeIcon,
  CheckIcon,
  PlusIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  PencilIcon,
  Cog6ToothIcon,
  BoltIcon
} from '@heroicons/react/24/outline'
import { TelegramSignal, SignalStats, SignalFilter, signalAPI, SignalSource, TradeDetail, TradeEntryPoint, TradeTakeProfitPoint } from '@/lib/api'

// 状态颜色映射
function getStatusColor(status: string) {
  switch (status) {
    case 'executed':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'failed':
      return 'bg-red-100 text-red-800'
    case 'cancelled':
      return 'bg-gray-100 text-gray-800'
    case 'active':
      return 'bg-blue-100 text-blue-800'
    case 'completed':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 状态文本映射
function getStatusText(status: string) {
  switch (status) {
    case 'executed':
      return '已执行'
    case 'pending':
      return '等待中'
    case 'failed':
      return '失败'
    case 'cancelled':
      return '已取消'
    case 'active':
      return '进行中'
    case 'completed':
      return '已完成'
    default:
      return status
  }
}

// 信号类型颜色映射
function getSignalTypeColor(type: string) {
  switch (type) {
    case 'long':
    case 'buy':
      return 'bg-green-100 text-green-800'
    case 'short':
    case 'sell':
      return 'bg-red-100 text-red-800'
    case 'close':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 信号类型文本映射
function getSignalTypeText(type: string) {
  switch (type) {
    case 'long':
      return '做多'
    case 'short':
      return '做空'
    case 'buy':
      return '买入'
    case 'sell':
      return '卖出'
    case 'close':
      return '平仓'
    default:
      return type.toUpperCase()
  }
}

// 交易详情抽屉组件
function TradeDetailDrawer({ 
  signal, 
  isOpen, 
  onClose, 
  onUpdate 
}: {
  signal: TelegramSignal | null
  isOpen: boolean
  onClose: () => void
  onUpdate: () => void
}) {
  const [tradeDetail, setTradeDetail] = useState<TradeDetail | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTradeDetail = async (signalId: number) => {
    try {
      setLoading(true)
      setError(null)
      const response = await signalAPI.getTradeDetail(signalId)
      if (response.success) {
        setTradeDetail(response.data)
      } else {
        setError('获取交易详情失败')
      }
    } catch (err: any) {
      setError(err.message || '获取交易详情失败')
    } finally {
      setLoading(false)
    }
  }

  const updateEntryPoint = async (entryPointId: number, data: any) => {
    try {
      await signalAPI.updateEntryPoint(entryPointId, data)
      if (signal) {
        await fetchTradeDetail(signal.id)
      }
      onUpdate()
    } catch (err: any) {
      setError(err.message || '更新入场点失败')
    }
  }

  const updateTakeProfitPoint = async (tpPointId: number, data: any) => {
    try {
      await signalAPI.updateTakeProfitPoint(tpPointId, data)
      if (signal) {
        await fetchTradeDetail(signal.id)
      }
      onUpdate()
    } catch (err: any) {
      setError(err.message || '更新止盈点失败')
    }
  }

  useEffect(() => {
    if (signal && isOpen) {
      fetchTradeDetail(signal.id)
    }
  }, [signal, isOpen])

  if (!isOpen || !signal) return null

  return (
    <div className="fixed inset-0 overflow-hidden z-50">
      <div className="absolute inset-0 bg-black/50" onClick={onClose}></div>
      
      <div className="absolute right-0 top-0 h-full w-1/2 bg-white shadow-xl">
        <div className="flex flex-col h-full">
          {/* 标题 */}
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-lg font-semibold text-gray-900">交易详情</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* 内容 */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : error ? (
              <div className="text-red-600 text-center py-8">{error}</div>
            ) : tradeDetail ? (
              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">基本信息</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">交易对:</span>
                      <span className="ml-2 font-medium">{tradeDetail.signal.symbol}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">类型:</span>
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getSignalTypeColor(tradeDetail.signal.signal_type)}`}>
                        {getSignalTypeText(tradeDetail.signal.signal_type)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">杠杆:</span>
                      <span className="ml-2 font-medium">{tradeDetail.signal.leverage}x</span>
                    </div>
                    <div>
                      <span className="text-gray-500">状态:</span>
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(tradeDetail.overall_status)}`}>
                        {getStatusText(tradeDetail.overall_status)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">止损:</span>
                      <span className="ml-2 font-medium">{tradeDetail.signal.stop_loss ? `$${tradeDetail.signal.stop_loss}` : '无'}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">总盈亏:</span>
                      <span className={`ml-2 font-medium ${tradeDetail.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {tradeDetail.total_pnl >= 0 ? '+' : ''}{tradeDetail.total_pnl_percentage ? tradeDetail.total_pnl_percentage.toFixed(2) : '0.00'}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* 入场点 */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">入场点 ({tradeDetail.entry_points.length})</h3>
                  <div className="space-y-3">
                    {tradeDetail.entry_points.map((ep) => (
                      <div key={ep.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <span className="font-medium">${ep.price}</span>
                            <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(ep.status)}`}>
                              {getStatusText(ep.status)}
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">{(ep.allocation * 100).toFixed(1)}%</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          <div>已成交: {ep.filled_quantity}</div>
                          {ep.actual_price && <div>实际价格: ${ep.actual_price}</div>}
                          {ep.description && <div>描述: {ep.description}</div>}
                        </div>
                        {ep.status === 'pending' && (
                          <div className="mt-2 flex space-x-2">
                            <button
                              onClick={() => updateEntryPoint(ep.id, { status: 'cancelled' })}
                              className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                            >
                              取消
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* 止盈点 */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">止盈点 ({tradeDetail.take_profit_points.length})</h3>
                  <div className="space-y-3">
                    {tradeDetail.take_profit_points.map((tp) => (
                      <div key={tp.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <span className="font-medium">${tp.price}</span>
                            <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(tp.status)}`}>
                              {getStatusText(tp.status)}
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">{(tp.allocation * 100).toFixed(1)}%</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          <div>已成交: {tp.filled_quantity}</div>
                          {tp.actual_price && <div>实际价格: ${tp.actual_price}</div>}
                          {tp.pnl && (
                            <div className={tp.pnl >= 0 ? 'text-green-600' : 'text-red-600'}>
                              盈亏: {tp.pnl >= 0 ? '+' : ''}{tp.pnl_percentage?.toFixed(2)}%
                            </div>
                          )}
                          {tp.description && <div>描述: {tp.description}</div>}
                        </div>
                        {tp.status === 'pending' && (
                          <div className="mt-2 flex space-x-2">
                            <button
                              onClick={() => updateTakeProfitPoint(tp.id, { status: 'cancelled' })}
                              className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                            >
                              取消
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* 原始消息 */}
                {tradeDetail.signal.raw_message && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3">原始消息</h3>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <pre className="text-sm text-gray-600 whitespace-pre-wrap">
                        {tradeDetail.signal.raw_message}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function SignalsPage() {
  const [signals, setSignals] = useState<TelegramSignal[]>([])
  const [stats, setStats] = useState<SignalStats | null>(null)
  const [sources, setSources] = useState<SignalSource[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<SignalFilter>({
    page: 1,
    page_size: 20
  })
  const [pagination, setPagination] = useState({
    total: 0,
    total_pages: 0,
    current_page: 1
  })
  const [refreshing, setRefreshing] = useState(false)
  const [selectedSignal, setSelectedSignal] = useState<TelegramSignal | null>(null)
  const [drawerOpen, setDrawerOpen] = useState(false)

  // 获取交易列表
  const fetchSignals = async () => {
    try {
      setLoading(true)
      const response = await signalAPI.getSignals(filter)
      setSignals(response.items || [])
      setPagination({
        total: response.total || 0,
        total_pages: response.pages || 0,
        current_page: response.page || 1
      })
      setError(null)
    } catch (err: any) {
      console.error('获取交易列表失败:', err)
      setError(err.message || '获取交易列表失败')
      setSignals([])
    } finally {
      setLoading(false)
    }
  }

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const response = await signalAPI.getSignalStats(30)
      if (response.success) {
        setStats(response.data)
      }
    } catch (err: any) {
      console.error('获取统计数据失败:', err)
    }
  }

  // 获取信号源列表
  const fetchSources = async () => {
    try {
      const response = await signalAPI.getSignalSources()
      if (response.success) {
        setSources(response.data)
      }
    } catch (err: any) {
      console.error('获取信号源失败:', err)
    }
  }

  // 删除交易
  const handleDeleteSignal = async (signalId: number) => {
    if (!confirm('确定要删除这个交易记录吗？')) {
      return
    }

    try {
      await signalAPI.deleteSignal(signalId)
      await fetchSignals()
    } catch (err: any) {
      setError(err.message || '删除交易失败')
    }
  }

  // 更新交易状态
  const handleUpdateStatus = async (signalId: number, status: string) => {
    try {
      await signalAPI.updateSignalStatus(signalId, status)
      await fetchSignals()
    } catch (err: any) {
      setError(err.message || '更新交易状态失败')
    }
  }

  // 处理筛选变化
  const handleFilterChange = (key: keyof SignalFilter, value: any) => {
    const newFilter = { ...filter, [key]: value, page: 1 }
    setFilter(newFilter)
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setFilter({ ...filter, page })
  }

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true)
    await Promise.all([fetchSignals(), fetchStats()])
    setRefreshing(false)
  }

  // 打开交易详情
  const handleViewDetail = (signal: TelegramSignal) => {
    setSelectedSignal(signal)
    setDrawerOpen(true)
  }

  // 关闭交易详情
  const handleCloseDetail = () => {
    setDrawerOpen(false)
    setSelectedSignal(null)
  }

  // 更新后刷新列表
  const handleUpdateComplete = () => {
    fetchSignals()
  }

  // 初始化数据
  useEffect(() => {
    Promise.all([fetchSignals(), fetchStats(), fetchSources()])
  }, [filter])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* 现代化顶部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200">
                <ArrowLeftIcon className="h-5 w-5" />
                <span className="font-medium">返回仪表板</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg">
                  <BoltIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">交易管理</h1>
                  <p className="text-sm text-gray-500">查看和管理交易记录</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Link
                href="/signals/management"
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <BoltIcon className="h-4 w-4" />
                <span>信号管理</span>
              </Link>
              <Link
                href="/signals/advanced"
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-lg transition-all duration-200"
              >
                <PlusIcon className="h-4 w-4" />
                <span>创建交易</span>
              </Link>
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ArrowPathIcon className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                <span>刷新</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* 错误提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-sm font-medium text-gray-500">总交易数</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.total_signals}</p>
              <p className="text-sm text-gray-600">过去30天</p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-sm font-medium text-gray-500">执行率</h3>
              <p className="text-2xl font-bold text-green-600">{stats.success_rate ? stats.success_rate.toFixed(1) : '0.0'}%</p>
              <p className="text-sm text-gray-600">交易执行率</p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-sm font-medium text-gray-500">平均盈亏</h3>
              <p className={`text-2xl font-bold ${(stats.avg_pnl || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {stats.avg_pnl ? (stats.avg_pnl >= 0 ? '+' : '') + stats.avg_pnl.toFixed(2) : '0.00'}%
              </p>
              <p className="text-sm text-gray-600">平均收益率</p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-sm font-medium text-gray-500">活跃信号源</h3>
              <p className="text-2xl font-bold text-purple-600">{Object.keys(stats.source_distribution).length}</p>
              <p className="text-sm text-gray-600">当前监听</p>
            </div>
          </div>
        )}

        {/* 筛选和搜索 */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  搜索
                </label>
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索交易对..."
                    value={filter.search || ''}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  状态
                </label>
                <select 
                  value={filter.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部状态</option>
                  <option value="executed">已执行</option>
                  <option value="pending">等待中</option>
                  <option value="active">进行中</option>
                  <option value="completed">已完成</option>
                  <option value="failed">失败</option>
                  <option value="cancelled">已取消</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  交易类型
                </label>
                <select 
                  value={filter.signal_type || ''}
                  onChange={(e) => handleFilterChange('signal_type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部类型</option>
                  <option value="long">做多</option>
                  <option value="short">做空</option>
                  <option value="close">平仓</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  交易对
                </label>
                <input
                  type="text"
                  placeholder="如：BTCUSDT"
                  value={filter.symbol || ''}
                  onChange={(e) => handleFilterChange('symbol', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 交易列表 */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : signals.length === 0 ? (
            <div className="p-8 text-center">
              <BoltIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无交易</h3>
              <p className="mt-1 text-sm text-gray-500">当前筛选条件下没有找到交易记录</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      交易对
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      类型
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      入场点/止盈点
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      盈亏
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {signals.map((signal) => (
                    <tr key={signal.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900">{signal.symbol}</div>
                        {signal.leverage && signal.leverage > 1 && (
                          <div className="text-sm text-gray-500">{signal.leverage}x 杠杆</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSignalTypeColor(signal.signal_type)}`}>
                          {getSignalTypeText(signal.signal_type)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center space-x-2">
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                            {signal.entry_points_count}个入场点
                          </span>
                          <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                            {signal.tp_points_count}个止盈点
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(signal.overall_status)}`}>
                          {getStatusText(signal.overall_status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {signal.total_pnl !== 0 ? (
                          <span className={`font-medium ${
                            signal.total_pnl > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {signal.total_pnl > 0 ? '+' : ''}{signal.pnl_percentage ? signal.pnl_percentage.toFixed(2) : '0.00'}%
                          </span>
                        ) : (
                          <span className="text-gray-500">--</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(signal.signal_time).toLocaleString('zh-CN')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewDetail(signal)}
                            className="text-blue-600 hover:text-blue-900"
                            title="查看详情"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <Link
                            href={`/signals/advanced?edit=${signal.id}`}
                            className="text-purple-600 hover:text-purple-900"
                            title="编辑交易"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </Link>
                          {signal.overall_status === 'pending' && (
                            <button
                              onClick={() => handleUpdateStatus(signal.id, 'executed')}
                              className="text-green-600 hover:text-green-900"
                              title="标记为已执行"
                            >
                              <CheckIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteSignal(signal.id)}
                            className="text-red-600 hover:text-red-900"
                            title="删除"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 分页 */}
        {pagination.total > 0 && (
          <div className="mt-6 flex items-center justify-between">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(pagination.current_page - 1)}
                disabled={pagination.current_page <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={() => handlePageChange(pagination.current_page + 1)}
                disabled={pagination.current_page >= pagination.total_pages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                下一页
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示 <span className="font-medium">{(pagination.current_page - 1) * filter.page_size! + 1}</span> 到{' '}
                  <span className="font-medium">{Math.min(pagination.current_page * filter.page_size!, pagination.total)}</span> 项，
                  共 <span className="font-medium">{pagination.total}</span> 项
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handlePageChange(pagination.current_page - 1)}
                    disabled={pagination.current_page <= 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <span className="sr-only">上一页</span>
                    <ChevronLeftIcon className="h-5 w-5" />
                  </button>
                  {[...Array(pagination.total_pages)].map((_, i) => {
                    const pageNumber = i + 1
                    const isCurrentPage = pageNumber === pagination.current_page
                    const showPage = 
                      pageNumber === 1 || 
                      pageNumber === pagination.total_pages || 
                      (pageNumber >= pagination.current_page - 1 && pageNumber <= pagination.current_page + 1)
                    
                    if (!showPage) {
                      if (pageNumber === pagination.current_page - 2 || pageNumber === pagination.current_page + 2) {
                        return (
                          <span key={pageNumber} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                          </span>
                        )
                      }
                      return null
                    }
                    
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          isCurrentPage
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    )
                  })}
                  <button
                    onClick={() => handlePageChange(pagination.current_page + 1)}
                    disabled={pagination.current_page >= pagination.total_pages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <span className="sr-only">下一页</span>
                    <ChevronRightIcon className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* 交易详情抽屉 */}
      <TradeDetailDrawer
        signal={selectedSignal}
        isOpen={drawerOpen}
        onClose={handleCloseDetail}
        onUpdate={handleUpdateComplete}
      />
    </div>
  )
} 