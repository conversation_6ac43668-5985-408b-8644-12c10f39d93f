'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { API_BASE_URL } from '@/lib/config'
import { 
  ArrowLeftIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  BoltIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  TrashIcon,
  EyeIcon,
  CheckIcon,
  PlusIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  PlayIcon,
  PauseIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline'

// 信号管理相关的类型定义
interface SignalManagementData {
  id: number
  symbol: string
  signal_type: string
  entry_price?: number
  stop_loss?: number
  take_profit?: number
  leverage?: number
  status: string
  source_id: number
  signal_time: string
  received_at: string
  raw_message: string
  entry_points_count: number
  tp_points_count: number
  entry_status: string
  tp_status: string
  overall_status: string
  total_pnl: number
  can_execute: boolean
}

interface SignalStatistics {
  total_signals: number
  executed_signals: number
  success_rate: number
  avg_pnl: number
  status_distribution: Record<string, number>
  type_distribution: Record<string, number>
  period_days: number
}

interface SignalSource {
  id: number
  name: string
  telegram_chat_id: string
  description: string
  total_signals: number
  successful_signals: number
  reliability_score: number
  created_at: string
}

interface AutoExecuteStatus {
  enabled: boolean
  description: string
}

// API 函数
const signalManagementAPI = {
  // 获取信号列表
  getSignals: async (params: any = {}) => {
    const searchParams = new URLSearchParams()
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        searchParams.append(key, params[key].toString())
      }
    })
    
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/?${searchParams}`)
    if (!response.ok) throw new Error('获取信号列表失败')
    return response.json()
  },

  // 获取统计信息
  getStatistics: async (days: number = 30) => {
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/statistics?days=${days}`)
    if (!response.ok) throw new Error('获取统计信息失败')
    return response.json()
  },

  // 获取活跃信号源
  getActiveSources: async () => {
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/sources/active`)
    if (!response.ok) throw new Error('获取信号源失败')
    return response.json()
  },

  // 手动执行信号
  executeSignal: async (signalId: number) => {
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/execute`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ signal_id: signalId })
    })
    if (!response.ok) throw new Error('执行信号失败')
    return response.json()
  },

  // 批量执行信号
  batchExecuteSignals: async (signalIds: number[]) => {
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/batch-execute`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(signalIds)
    })
    if (!response.ok) throw new Error('批量执行失败')
    return response.json()
  },

  // 获取自动执行状态
  getAutoExecuteStatus: async () => {
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/auto-execute/status`)
    if (!response.ok) throw new Error('获取自动执行状态失败')
    return response.json()
  },

  // 切换自动执行
  toggleAutoExecute: async (enabled: boolean) => {
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/auto-execute/toggle`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ enabled })
    })
    if (!response.ok) throw new Error('切换自动执行失败')
    return response.json()
  },

  // 删除信号
  deleteSignal: async (signalId: number) => {
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/${signalId}`, {
      method: 'DELETE'
    })
    if (!response.ok) throw new Error('删除信号失败')
    return response.json()
  },

  // 更新信号状态
  updateSignalStatus: async (signalId: number, status: string) => {
    const response = await fetch(`${API_BASE_URL}/api/v1/signal-management/${signalId}/status`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(status)
    })
    if (!response.ok) throw new Error('更新状态失败')
    return response.json()
  }
}

// 状态颜色映射
function getStatusColor(status: string) {
  switch (status) {
    case 'executed':
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'processing':
    case 'active':
      return 'bg-blue-100 text-blue-800'
    case 'failed':
      return 'bg-red-100 text-red-800'
    case 'cancelled':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 状态文本映射
function getStatusText(status: string) {
  switch (status) {
    case 'executed':
      return '已执行'
    case 'pending':
      return '等待中'
    case 'processing':
      return '处理中'
    case 'active':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'failed':
      return '失败'
    case 'cancelled':
      return '已取消'
    default:
      return status
  }
}

// 状态图标映射
function getStatusIcon(status: string) {
  switch (status) {
    case 'executed':
    case 'completed':
      return <CheckCircleIcon className="h-4 w-4" />
    case 'pending':
      return <ClockIcon className="h-4 w-4" />
    case 'processing':
    case 'active':
      return <PlayIcon className="h-4 w-4" />
    case 'failed':
      return <XCircleIcon className="h-4 w-4" />
    case 'cancelled':
      return <ExclamationCircleIcon className="h-4 w-4" />
    default:
      return <ClockIcon className="h-4 w-4" />
  }
}

// 信号类型颜色映射
function getSignalTypeColor(type: string) {
  switch (type) {
    case 'long':
    case 'buy':
      return 'bg-green-100 text-green-800'
    case 'short':
    case 'sell':
      return 'bg-red-100 text-red-800'
    case 'close':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 信号类型文本映射
function getSignalTypeText(type: string) {
  switch (type) {
    case 'long':
      return '做多'
    case 'short':
      return '做空'
    case 'buy':
      return '买入'
    case 'sell':
      return '卖出'
    case 'close':
      return '平仓'
    default:
      return type.toUpperCase()
  }
}

export default function SignalManagementPage() {
  const [signals, setSignals] = useState<SignalManagementData[]>([])
  const [statistics, setStatistics] = useState<SignalStatistics | null>(null)
  const [sources, setSources] = useState<SignalSource[]>([])
  const [autoExecuteStatus, setAutoExecuteStatus] = useState<AutoExecuteStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedSignals, setSelectedSignals] = useState<number[]>([])
  const [executingSignals, setExecutingSignals] = useState<Set<number>>(new Set())
  const [refreshing, setRefreshing] = useState(false)
  
  // 过滤和分页状态
  const [filters, setFilters] = useState({
    page: 1,
    page_size: 20,
    symbol: '',
    signal_type: '',
    status: '',
    source_id: '',
    search: ''
  })
  
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    current_page: 1
  })

  // 获取数据
  const fetchData = async () => {
    try {
      setLoading(true)
      const [signalsRes, statsRes, sourcesRes, autoExecRes] = await Promise.all([
        signalManagementAPI.getSignals(filters),
        signalManagementAPI.getStatistics(30),
        signalManagementAPI.getActiveSources(),
        signalManagementAPI.getAutoExecuteStatus()
      ])

      if (signalsRes.success) {
        setSignals(signalsRes.data.items || [])
        setPagination({
          total: signalsRes.data.total || 0,
          pages: signalsRes.data.pages || 0,
          current_page: signalsRes.data.page || 1
        })
      }

      if (statsRes.success) {
        setStatistics(statsRes.data)
      }

      if (sourcesRes.success) {
        setSources(sourcesRes.data)
      }

      if (autoExecRes.success) {
        setAutoExecuteStatus(autoExecRes.data)
      }

      setError(null)
    } catch (err: any) {
      console.error('获取数据失败:', err)
      setError(err.message || '获取数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchData()
    setRefreshing(false)
  }

  // 执行单个信号
  const handleExecuteSignal = async (signalId: number) => {
    if (executingSignals.has(signalId)) return

    setExecutingSignals(prev => new Set(prev).add(signalId))
    
    try {
      const result = await signalManagementAPI.executeSignal(signalId)
      if (result.success) {
        await fetchData() // 刷新数据
      } else {
        setError(result.message || '执行信号失败')
      }
    } catch (err: any) {
      setError(err.message || '执行信号失败')
    } finally {
      setExecutingSignals(prev => {
        const newSet = new Set(prev)
        newSet.delete(signalId)
        return newSet
      })
    }
  }

  // 批量执行信号
  const handleBatchExecute = async () => {
    if (selectedSignals.length === 0) {
      setError('请选择要执行的信号')
      return
    }

    try {
      const result = await signalManagementAPI.batchExecuteSignals(selectedSignals)
      if (result.success) {
        setSelectedSignals([])
        await fetchData()
      } else {
        setError(result.message || '批量执行失败')
      }
    } catch (err: any) {
      setError(err.message || '批量执行失败')
    }
  }

  // 切换自动执行
  const handleToggleAutoExecute = async () => {
    if (!autoExecuteStatus) return

    try {
      const result = await signalManagementAPI.toggleAutoExecute(!autoExecuteStatus.enabled)
      if (result.success) {
        setAutoExecuteStatus(result.data)
      } else {
        setError(result.message || '切换自动执行失败')
      }
    } catch (err: any) {
      setError(err.message || '切换自动执行失败')
    }
  }

  // 删除信号
  const handleDeleteSignal = async (signalId: number) => {
    if (!confirm('确定要删除这个信号吗？')) return

    try {
      const result = await signalManagementAPI.deleteSignal(signalId)
      if (result.success) {
        await fetchData()
      } else {
        setError(result.message || '删除信号失败')
      }
    } catch (err: any) {
      setError(err.message || '删除信号失败')
    }
  }

  // 处理过滤变化
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }))
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  // 选择信号
  const handleSelectSignal = (signalId: number, checked: boolean) => {
    setSelectedSignals(prev => {
      if (checked) {
        return [...prev, signalId]
      } else {
        return prev.filter(id => id !== signalId)
      }
    })
  }

  // 全选/全不选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const executableSignals = signals.filter(s => s.can_execute).map(s => s.id)
      setSelectedSignals(executableSignals)
    } else {
      setSelectedSignals([])
    }
  }

  // 初始化
  useEffect(() => {
    fetchData()
  }, [filters])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载信号管理数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* 顶部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200">
                <ArrowLeftIcon className="h-5 w-5" />
                <span className="font-medium">返回仪表板</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg">
                  <BoltIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">信号管理</h1>
                  <p className="text-sm text-gray-500">统一管理交易信号的生命周期</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {/* 自动执行开关 */}
              {autoExecuteStatus && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">自动执行:</span>
                  <button
                    onClick={handleToggleAutoExecute}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      autoExecuteStatus.enabled ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        autoExecuteStatus.enabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                  <span className={`text-sm ${autoExecuteStatus.enabled ? 'text-green-600' : 'text-gray-500'}`}>
                    {autoExecuteStatus.enabled ? '已启用' : '已禁用'}
                  </span>
                </div>
              )}
              
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ArrowPathIcon className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                <span>刷新</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* 错误提示 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        {/* 统计卡片 */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <ChartBarIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-500">总信号数</h3>
                  <p className="text-2xl font-bold text-gray-900">{statistics.total_signals}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-500">已执行</h3>
                  <p className="text-2xl font-bold text-green-600">{statistics.executed_signals}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <PlayIcon className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-500">执行率</h3>
                  <p className="text-2xl font-bold text-purple-600">{statistics.success_rate.toFixed(1)}%</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <BoltIcon className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-500">平均盈亏</h3>
                  <p className={`text-2xl font-bold ${statistics.avg_pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {statistics.avg_pnl >= 0 ? '+' : ''}{statistics.avg_pnl.toFixed(2)}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 筛选和批量操作 */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="p-6">
            {/* 筛选条件 */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">搜索</label>
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索交易对..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">状态</label>
                <select 
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部状态</option>
                  <option value="pending">等待中</option>
                  <option value="processing">处理中</option>
                  <option value="executed">已执行</option>
                  <option value="active">进行中</option>
                  <option value="completed">已完成</option>
                  <option value="failed">失败</option>
                  <option value="cancelled">已取消</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">信号类型</label>
                <select 
                  value={filters.signal_type}
                  onChange={(e) => handleFilterChange('signal_type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部类型</option>
                  <option value="long">做多</option>
                  <option value="short">做空</option>
                  <option value="buy">买入</option>
                  <option value="sell">卖出</option>
                  <option value="close">平仓</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">信号源</label>
                <select 
                  value={filters.source_id}
                  onChange={(e) => handleFilterChange('source_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部信号源</option>
                  {sources.map(source => (
                    <option key={source.id} value={source.id}>
                      {source.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">交易对</label>
                <input
                  type="text"
                  placeholder="如：BTCUSDT"
                  value={filters.symbol}
                  onChange={(e) => handleFilterChange('symbol', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* 批量操作 */}
            {selectedSignals.length > 0 && (
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <span className="text-sm text-blue-800">
                  已选择 {selectedSignals.length} 个信号
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={handleBatchExecute}
                    className="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    批量执行
                  </button>
                  <button
                    onClick={() => setSelectedSignals([])}
                    className="px-3 py-1.5 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    取消选择
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 信号列表 */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {signals.length === 0 ? (
            <div className="p-8 text-center">
              <BoltIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无信号</h3>
              <p className="mt-1 text-sm text-gray-500">当前筛选条件下没有找到信号记录</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedSignals.length === signals.filter(s => s.can_execute).length && signals.filter(s => s.can_execute).length > 0}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      信号信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      信号源
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      类型
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      入场/止盈
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      盈亏
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {signals.map((signal) => (
                    <tr key={signal.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedSignals.includes(signal.id)}
                          onChange={(e) => handleSelectSignal(signal.id, e.target.checked)}
                          disabled={!signal.can_execute}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="font-medium text-gray-900">{signal.symbol}</div>
                          {signal.leverage && signal.leverage > 1 && (
                            <div className="text-sm text-gray-500">{signal.leverage}x 杠杆</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900 font-medium">
                          {sources.find(s => s.id === signal.source_id)?.name || '未知'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSignalTypeColor(signal.signal_type)}`}>
                          {getSignalTypeText(signal.signal_type)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center space-x-2">
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                            {signal.entry_points_count}个入场点
                          </span>
                          <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                            {signal.tp_points_count}个止盈点
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(signal.overall_status)}
                          <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(signal.overall_status)}`}>
                            {getStatusText(signal.overall_status)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {signal.total_pnl !== 0 ? (
                          <span className={`font-medium ${signal.total_pnl > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {signal.total_pnl > 0 ? '+' : ''}{signal.total_pnl.toFixed(2)}%
                          </span>
                        ) : (
                          <span className="text-gray-500">--</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(signal.signal_time).toLocaleString('zh-CN')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          {signal.can_execute && (
                            <button
                              onClick={() => handleExecuteSignal(signal.id)}
                              disabled={executingSignals.has(signal.id)}
                              className="text-green-600 hover:text-green-900 disabled:opacity-50"
                              title="执行信号"
                            >
                              {executingSignals.has(signal.id) ? (
                                <div className="animate-spin h-4 w-4 border-2 border-green-600 border-t-transparent rounded-full" />
                              ) : (
                                <PlayIcon className="h-4 w-4" />
                              )}
                            </button>
                          )}
                          <Link
                            href={`/signals/${signal.id}/detail`}
                            className="text-blue-600 hover:text-blue-900"
                            title="查看详情"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => handleDeleteSignal(signal.id)}
                            className="text-red-600 hover:text-red-900"
                            title="删除"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 分页 */}
        {pagination.total > 0 && (
          <div className="mt-6 flex items-center justify-between">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(pagination.current_page - 1)}
                disabled={pagination.current_page <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={() => handlePageChange(pagination.current_page + 1)}
                disabled={pagination.current_page >= pagination.pages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                下一页
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示 <span className="font-medium">{(pagination.current_page - 1) * filters.page_size + 1}</span> 到{' '}
                  <span className="font-medium">{Math.min(pagination.current_page * filters.page_size, pagination.total)}</span> 项，
                  共 <span className="font-medium">{pagination.total}</span> 项
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handlePageChange(pagination.current_page - 1)}
                    disabled={pagination.current_page <= 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <ChevronLeftIcon className="h-5 w-5" />
                  </button>
                  {[...Array(pagination.pages)].map((_, i) => {
                    const pageNumber = i + 1
                    const isCurrentPage = pageNumber === pagination.current_page
                    const showPage = 
                      pageNumber === 1 || 
                      pageNumber === pagination.pages || 
                      (pageNumber >= pagination.current_page - 1 && pageNumber <= pagination.current_page + 1)
                    
                    if (!showPage) {
                      if (pageNumber === pagination.current_page - 2 || pageNumber === pagination.current_page + 2) {
                        return (
                          <span key={pageNumber} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                          </span>
                        )
                      }
                      return null
                    }
                    
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          isCurrentPage
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    )
                  })}
                  <button
                    onClick={() => handlePageChange(pagination.current_page + 1)}
                    disabled={pagination.current_page >= pagination.pages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <ChevronRightIcon className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}