'use client'

import { useState } from 'react'
import api from '@/lib/api'
import { 
  BeakerIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

interface TestResult {
  success: boolean
  parsed?: boolean
  data?: {
    signal_type?: string
    symbol?: string
    entry_price?: number
    entry_prices?: number[]
    stop_loss?: number
    take_profit?: number
    take_profit_targets?: number[]
    leverage?: number
    confidence_score?: number
    format_type?: string
  }
  validation?: {
    is_valid: boolean
    errors: string[]
  }
  error?: string
  message?: string
}

export default function SignalTestPage() {
  const [singleSignal, setSingleSignal] = useState('')
  const [batchSignals, setBatchSignals] = useState('')
  const [singleResult, setSingleResult] = useState<TestResult | null>(null)
  const [batchResults, setBatchResults] = useState<TestResult[]>([])
  const [batchStats, setBatchStats] = useState<any>(null)
  const [batchPerformance, setBatchPerformance] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'single' | 'batch'>('single')
  const [parserType, setParserType] = useState<'default' | 'builtin' | 'llm'>('default')

  const handleSingleTest = async () => {
    if (!singleSignal.trim()) return
    
    setLoading(true)
    try {
      const result = await api.test.testSingleSignal(
        singleSignal, 
        parserType === 'default' ? undefined : parserType
      )
      setSingleResult(result)
    } catch (error) {
      setSingleResult({
        success: false,
        error: '测试失败'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBatchTest = async () => {
    const signals = batchSignals.split('---').map(s => s.trim()).filter(s => s)
    if (signals.length === 0) return
    
    setLoading(true)
    try {
      const response = await api.test.testBatchSignals(
        signals,
        parserType === 'default' ? undefined : parserType
      )
      
      // 处理新的API响应格式
      if (response.success && response.results) {
        setBatchResults(response.results)
        setBatchStats(response.stats)
        setBatchPerformance(response.performance)
      } else {
        setBatchResults([])
        setBatchStats(null)
        setBatchPerformance(null)
      }
    } catch (error) {
      setBatchResults([])
    } finally {
      setLoading(false)
    }
  }

  const renderResult = (result: TestResult) => {
    const isParsed = result.success && result.parsed
    const isValid = isParsed && result.validation?.is_valid
    
    return (
      <div className={`p-4 rounded-lg border ${isParsed ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
        <div className="flex items-center mb-3">
          {isParsed ? (
            <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
          ) : (
            <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
          )}
          <span className={`font-medium ${isParsed ? 'text-green-800' : 'text-red-800'}`}>
            {isParsed ? '解析成功' : '解析失败'}
          </span>
          {result.data?.format_type && (
            <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
              {result.data.format_type}
            </span>
          )}
          {result.data?.confidence_score && (
            <span className="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
              置信度: {(result.data.confidence_score * 100).toFixed(0)}%
            </span>
          )}
        </div>
        
        {isParsed && result.data ? (
          <div className="space-y-2 text-sm">
            <div><span className="font-medium">交易对:</span> {result.data.symbol}</div>
            <div><span className="font-medium">信号类型:</span> {result.data.signal_type}</div>
            {result.data.leverage && <div><span className="font-medium">杠杆:</span> {result.data.leverage}x</div>}
            {result.data.entry_prices && result.data.entry_prices.length > 0 && (
              <div><span className="font-medium">入场价格:</span> {result.data.entry_prices.join(', ')}</div>
            )}
            {result.data.entry_price && (
              <div><span className="font-medium">入场价格:</span> {result.data.entry_price}</div>
            )}
            {result.data.take_profit_targets && result.data.take_profit_targets.length > 0 && (
              <div><span className="font-medium">止盈目标:</span> {result.data.take_profit_targets.join(', ')}</div>
            )}
            {result.data.take_profit && (
              <div><span className="font-medium">止盈:</span> {result.data.take_profit}</div>
            )}
            {result.data.stop_loss && <div><span className="font-medium">止损:</span> {result.data.stop_loss}</div>}
            
            {result.validation && !isValid && (
              <div className="mt-3 p-2 bg-yellow-100 border border-yellow-300 rounded">
                <div className="text-sm text-yellow-800 font-medium">验证警告:</div>
                <ul className="text-sm text-yellow-700 mt-1">
                  {result.validation.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ) : (
          <div className="text-sm text-red-700">
            {result.error || result.message || '未知错误'}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <BeakerIcon className="h-8 w-8 mr-3 text-blue-600" />
                信号测试
              </h1>
              <p className="mt-2 text-gray-600">测试信号解析器对不同格式信号的解析能力</p>
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">解析器:</label>
              <select
                value={parserType}
                onChange={(e) => setParserType(e.target.value as 'default' | 'builtin' | 'llm')}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
              >
                <option value="default">系统默认</option>
                <option value="builtin">内置解析器</option>
                <option value="llm">LLM解析器</option>
              </select>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('single')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'single'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                单个测试
              </button>
              <button
                onClick={() => setActiveTab('batch')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'batch'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                批量测试
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'single' ? (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    信号文本
                  </label>
                  <textarea
                    value={singleSignal}
                    onChange={(e) => setSingleSignal(e.target.value)}
                    rows={6}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="粘贴要测试的信号文本..."
                  />
                </div>
                
                <button
                  onClick={handleSingleTest}
                  disabled={loading || !singleSignal.trim()}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <BeakerIcon className="h-4 w-4 mr-2" />
                  )}
                  {loading ? '测试中...' : '开始测试'}
                </button>

                {singleResult && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">测试结果</h3>
                    {renderResult(singleResult)}
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    批量信号文本 (使用 --- 分隔)
                  </label>
                  <textarea
                    value={batchSignals}
                    onChange={(e) => setBatchSignals(e.target.value)}
                    rows={12}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="粘贴多个信号文本，使用 --- 分隔..."
                  />
                </div>
                
                <button
                  onClick={handleBatchTest}
                  disabled={loading || !batchSignals.trim()}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <DocumentTextIcon className="h-4 w-4 mr-2" />
                  )}
                  {loading ? '测试中...' : '批量测试'}
                </button>

                {batchResults.length > 0 && (
                  <div>
                    <div className="mb-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">测试结果</h3>
                      
                      {/* 性能统计 */}
                      {batchStats && batchPerformance && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                          <h4 className="text-sm font-medium text-blue-900 mb-2">性能统计</h4>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-blue-700">解析成功率:</span>
                              <span className="ml-1 font-semibold text-blue-900">{batchStats.success_rate}%</span>
                            </div>
                            <div>
                              <span className="text-blue-700">总处理时间:</span>
                              <span className="ml-1 font-semibold text-blue-900">{batchPerformance.total_time_seconds.toFixed(2)}s</span>
                            </div>
                            <div>
                              <span className="text-blue-700">平均时间:</span>
                              <span className="ml-1 font-semibold text-blue-900">{batchPerformance.average_time_per_signal.toFixed(3)}s/信号</span>
                            </div>
                            <div>
                              <span className="text-blue-700">解析器:</span>
                              <span className="ml-1 font-semibold text-blue-900">{batchPerformance.parser_type}</span>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      <div className="text-sm text-gray-600 mb-4">
                        共 {batchResults.length} 个信号，
                        成功 {batchResults.filter(r => r.success && r.parsed).length} 个，
                        失败 {batchResults.filter(r => !r.success || !r.parsed).length} 个
                      </div>
                    </div>
                    <div className="space-y-4">
                                              {batchResults.map((result, index) => (
                          <div key={index}>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">
                              信号 #{index + 1}
                              {result.original_message && (
                                <span className="text-xs text-gray-500 ml-2">
                                  {result.original_message}
                                </span>
                              )}
                            </h4>
                            {renderResult(result)}
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}