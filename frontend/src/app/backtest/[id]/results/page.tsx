'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeftIcon, ChartBarIcon } from '@heroicons/react/24/outline'
import api from '@/lib/api'
import { BacktestDetailedResults } from '@/lib/types'

export default function BacktestResultsPage() {
  const params = useParams()
  const backtestId = parseInt(params.id as string)
  const [results, setResults] = useState<BacktestDetailedResults | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadResults = async () => {
      try {
        const data = await api.backtest.getBacktestResults(backtestId)
        setResults(data)
      } catch (error) {
        console.error('加载失败:', error)
      } finally {
        setLoading(false)
      }
    }
    loadResults()
  }, [backtestId])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!results) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">加载失败</h2>
          <Link href="/backtest" className="text-blue-600 hover:underline">返回回测列表</Link>
        </div>
      </div>
    )
  }

  const { backtest_info, summary } = results

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/backtest" className="flex items-center text-gray-600 hover:text-blue-600">
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              返回
            </Link>
            <ChartBarIcon className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-xl font-bold">{backtest_info.name}</h1>
              <p className="text-sm text-gray-500">{backtest_info.symbol}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm text-gray-500">总收益率</h3>
            <p className={`text-2xl font-bold ${summary.total_return >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {summary.total_return >= 0 ? '+' : ''}{(summary.total_return * 100).toFixed(2)}%
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm text-gray-500">胜率</h3>
            <p className="text-2xl font-bold">{summary.win_rate ? (summary.win_rate * 100).toFixed(1) : '0.0'}%</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm text-gray-500">总交易数</h3>
            <p className="text-2xl font-bold">{summary.total_trades}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm text-gray-500">最大回撤</h3>
            <p className="text-2xl font-bold text-red-600">-{summary.max_drawdown ? (summary.max_drawdown * 100).toFixed(2) : '0.00'}%</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-bold mb-4">详细统计</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">初始资金</p>
              <p className="font-medium">${summary.initial_balance.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">最终资金</p>
              <p className="font-medium">${summary.final_balance.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">盈利交易</p>
              <p className="font-medium text-green-600">{summary.winning_trades}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">亏损交易</p>
              <p className="font-medium text-red-600">{summary.losing_trades}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">盈利因子</p>
              <p className="font-medium">{summary.profit_factor ? summary.profit_factor.toFixed(2) : 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">夏普比率</p>
              <p className="font-medium">{summary.sharpe_ratio ? summary.sharpe_ratio.toFixed(2) : 'N/A'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}