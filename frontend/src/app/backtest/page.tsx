'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import Link from 'next/link'
import { 
  ArrowLeftIcon,
  PlayIcon,
  StopIcon,
  ChartBarIcon,
  CalendarIcon,
  PresentationChartLineIcon,
  ArrowPathIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import api from '@/lib/api'
import { BacktestResult, SignalSource, CreateBacktestRequest, BacktestProgress } from '@/lib/types'





function getStatusColor(status: string) {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'running':
      return 'bg-blue-100 text-blue-800'
    case 'failed':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'running':
      return '运行中'
    case 'failed':
      return '失败'
    default:
      return status
  }
}

export default function BacktestPage() {
  const [backtests, setBacktests] = useState<BacktestResult[]>([])


  const [loading, setLoading] = useState(true)


  const [progressMap, setProgressMap] = useState<Record<number, BacktestProgress>>({})


  useEffect(() => {
    loadData()
  }, [])

  // 使用ref来避免闭包问题
  const backtestsRef = useRef(backtests)
  backtestsRef.current = backtests

  const updateProgress = useCallback(async () => {
    const runningBacktests = backtestsRef.current.filter(bt => bt.status === 'running')
    if (runningBacktests.length === 0) return

    try {
      const progressPromises = runningBacktests.map(bt => 
        api.backtest.getBacktestProgress(bt.id)
      )
      const progressResults = await Promise.all(progressPromises)
      
      const newProgressMap: Record<number, BacktestProgress> = {}
      progressResults.forEach(progress => {
        newProgressMap[progress.backtest_id] = progress
      })
      
      setProgressMap(newProgressMap)
      
      // 如果有回测完成，重新加载数据
      const completedBacktests = progressResults.filter(p => 
        p.status === 'completed' || p.status === 'failed'
      )
      if (completedBacktests.length > 0) {
        loadData()
      }
    } catch (error) {
      console.error('更新进度失败:', error)
    }
  }, [])

  // 只在有运行中的回测时启动定时器
  useEffect(() => {
    const runningBacktests = backtests.filter(bt => bt.status === 'running')
    if (runningBacktests.length === 0) return

    const interval = setInterval(updateProgress, 3000)
    return () => clearInterval(interval)
  }, [backtests.length, backtests.some(bt => bt.status === 'running'), updateProgress])

  const loadData = async () => {
    try {
      const backtestsRes = await api.backtest.getBacktests({ size: 50 })
      setBacktests(backtestsRes.items)
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }





  const handleDeleteBacktest = async (id: number) => {
    if (!confirm('确定要删除这个回测吗？')) return

    try {
      await api.backtest.deleteBacktest(id)
      loadData()
    } catch (error: any) {
      alert(`删除失败: ${error.message || '未知错误'}`)
    }
  }

  const handleRestartBacktest = async (id: number) => {
    if (!confirm('确定要重新开始这个回测吗？')) return

    try {
      await api.backtest.restartBacktest(id)
      loadData()
    } catch (error: any) {
      alert(`重启失败: ${error.message || '未知错误'}`)
    }
  }

  const handleStopBacktest = async (id: number) => {
    if (!confirm('确定要停止这个回测吗？')) return

    try {
      const response = await fetch(`/api/v1/backtest/${id}/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      const result = await response.json()
      
      if (result.success) {
        alert('回测已成功停止')
        loadData() // 重新加载数据以更新状态
      } else {
        alert(`停止失败: ${result.message || '未知错误'}`)
      }
    } catch (error: any) {
      alert(`停止失败: ${error.message || '未知错误'}`)
    }
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}秒`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
    return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分钟`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* 现代化顶部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200">
                <ArrowLeftIcon className="h-5 w-5" />
                <span className="font-medium">返回仪表板</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg">
                  <PresentationChartLineIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">回测分析</h1>
                  <p className="text-sm text-gray-500">历史信号回测和性能评估</p>
                </div>
              </div>
            </div>
            <Link 
              href="/backtest/create"
              className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-lg hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 shadow-lg transition-all duration-200"
            >
              <PlayIcon className="h-4 w-4 mr-2" />
              新建回测
            </Link>
          </div>
        </div>
      </div>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            历史信号回测
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            基于历史信号数据进行策略回测和性能评估
          </p>
        </div>

        {/* 总体统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">总回测次数</h3>
            <p className="text-2xl font-bold text-gray-900">{backtests.length}</p>
            <p className="text-sm text-blue-600">运行中: {backtests.filter(r => r.status === 'running').length}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">平均胜率</h3>
            <p className="text-2xl font-bold text-gray-900">
              {backtests.length > 0 ? 
                ((backtests.filter(r => r.win_rate).reduce((sum, r) => sum + (r.win_rate || 0), 0) / backtests.filter(r => r.win_rate).length) * 100).toFixed(1) 
                : '0.0'}%
            </p>
            <p className="text-sm text-gray-600">整体表现</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">最佳收益</h3>
            <p className="text-2xl font-bold text-green-600">
              +{backtests.length > 0 ? 
                (Math.max(...backtests.map(r => r.total_return || 0)) * 100).toFixed(1) 
                : '0.0'}%
            </p>
            <p className="text-sm text-gray-600">单次回测</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">总交易数</h3>
            <p className="text-2xl font-bold text-gray-900">
              {backtests.reduce((sum, r) => sum + (r.total_trades || 0), 0)}
            </p>
            <p className="text-sm text-gray-600">历史交易</p>
          </div>
        </div>

        <div className="grid gap-8 grid-cols-1">
          {/* 回测结果列表 */}
          <div className="col-span-1">
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">回测结果</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        回测名称
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        时间范围
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        交易数
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        胜率
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        总收益
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态/操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {backtests.map((result) => {
                      const progress = progressMap[result.id]
                      return (
                        <tr key={result.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="font-medium text-gray-900">{result.name}</div>
                            <div className="text-sm text-gray-500">
                              {result.status === 'running' && progress ? (
                                <div className="flex items-center space-x-2">
                                  <div className="w-20 bg-gray-200 rounded-full h-1.5">
                                    <div 
                                      className="bg-blue-600 h-1.5 rounded-full transition-all duration-300" 
                                      style={{ width: `${progress.progress_percentage || 0}%` }}
                                    ></div>
                                  </div>
                                  <span>{(progress.progress_percentage || 0).toFixed(1)}%</span>
                                </div>
                              ) : result.started_at && result.completed_at ? (
                                `耗时: ${formatDuration(Math.floor((new Date(result.completed_at).getTime() - new Date(result.started_at).getTime()) / 1000))}`
                              ) : (
                                result.symbol
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div>{new Date(result.start_date).toLocaleDateString()}</div>
                            <div>至 {new Date(result.end_date).toLocaleDateString()}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.total_trades || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {result.win_rate !== null && result.win_rate !== undefined ? (
                              <div className="flex items-center">
                                <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                  <div 
                                    className="bg-green-600 h-2 rounded-full" 
                                    style={{ width: `${result.win_rate * 100}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm text-gray-900">
                                  {(result.win_rate * 100).toFixed(1)}%
                                </span>
                              </div>
                            ) : (
                              <span className="text-sm text-gray-400">-</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {result.total_return !== null && result.total_return !== undefined ? (
                              <span className={`font-medium ${
                                result.total_return >= 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {result.total_return >= 0 ? '+' : ''}{(result.total_return * 100).toFixed(1)}%
                              </span>
                            ) : (
                              <span className="text-sm text-gray-400">-</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(result.status)}`}>
                                {getStatusText(result.status)}
                              </span>
                              {result.status === 'completed' && (
                                <button
                                  onClick={() => window.open(`/backtest/${result.id}/results`, '_blank')}
                                  className="text-blue-600 hover:text-blue-800"
                                  title="查看详细结果"
                                >
                                  <EyeIcon className="h-4 w-4" />
                                </button>
                              )}
                              {result.status === 'running' && (
                                <button
                                  onClick={() => handleStopBacktest(result.id)}
                                  className="text-red-600 hover:text-red-800"
                                  title="停止回测"
                                >
                                  <StopIcon className="h-4 w-4" />
                                </button>
                              )}
                              {result.status !== 'running' && (
                                <>
                                  <button
                                    onClick={() => handleRestartBacktest(result.id)}
                                    className="text-green-600 hover:text-green-800"
                                    title="重新开始"
                                  >
                                    <ArrowPathIcon className="h-4 w-4" />
                                  </button>
                                  <button
                                    onClick={() => handleDeleteBacktest(result.id)}
                                    className="text-red-600 hover:text-red-800"
                                    title="删除"
                                  >
                                    <TrashIcon className="h-4 w-4" />
                                  </button>
                                </>
                              )}
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>


        </div>

        {/* 详细指标 */}
        {backtests.length > 0 && (
          <div className="mt-8 bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">整体统计</h3>
              <p className="text-sm text-gray-500">所有已完成回测的汇总数据</p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {backtests.filter(bt => bt.win_rate).length > 0 ? 
                      ((backtests.filter(bt => bt.win_rate).reduce((sum, bt) => sum + (bt.win_rate || 0), 0) / backtests.filter(bt => bt.win_rate).length) * 100).toFixed(1) 
                      : '0.0'}%
                  </div>
                  <div className="text-sm text-gray-500">平均胜率</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${
                    backtests.length > 0 && Math.max(...backtests.map(bt => bt.total_return || 0)) >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {backtests.length > 0 ? 
                      `${Math.max(...backtests.map(bt => bt.total_return || 0)) >= 0 ? '+' : ''}${(Math.max(...backtests.map(bt => bt.total_return || 0)) * 100).toFixed(1)}%`
                      : '0.0%'}
                  </div>
                  <div className="text-sm text-gray-500">最佳收益</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {backtests.length > 0 ? 
                      `${(Math.max(...backtests.map(bt => Math.abs(bt.max_drawdown || 0))) * 100).toFixed(1)}%`
                      : '0.0%'}
                  </div>
                  <div className="text-sm text-gray-500">最大回撤</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {backtests.filter(bt => bt.sharpe_ratio).length > 0 ? 
                      (backtests.filter(bt => bt.sharpe_ratio).reduce((sum, bt) => sum + (bt.sharpe_ratio || 0), 0) / backtests.filter(bt => bt.sharpe_ratio).length).toFixed(2)
                      : '0.00'}
                  </div>
                  <div className="text-sm text-gray-500">平均夏普比率</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
} 