'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeftIcon,
  PlayIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import api from '@/lib/api'
import { SignalSource, CreateBacktestRequest } from '@/lib/types'

export default function CreateBacktestPage() {
  const router = useRouter()
  const [sources, setSources] = useState<SignalSource[]>([])
  const [symbols, setSymbols] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [formData, setFormData] = useState<CreateBacktestRequest>({
    name: '',
    symbol: undefined,
    start_date: '',
    end_date: '',
    initial_balance: 10000,
    source_ids: []
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      console.log('开始加载数据...')
      const [sourcesRes, symbolsRes] = await Promise.all([
        api.sources.getSources(false), // 获取所有信号源，包括非活跃的
        api.backtest.getAvailableSymbols()
      ])
      
      console.log('信号源数据:', sourcesRes)
      console.log('交易对数据:', symbolsRes)
      
      setSources(Array.isArray(sourcesRes) ? sourcesRes : [])
      setSymbols(symbolsRes?.symbols || ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'])
    } catch (error) {
      console.error('加载数据失败:', error)
      setSources([])
      setSymbols(['BTCUSDT', 'ETHUSDT', 'ADAUSDT'])
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (creating) return

    if (!formData.name || !formData.start_date || !formData.end_date || formData.source_ids.length === 0) {
      alert('请填写所有必填字段')
      return
    }

    setCreating(true)
    try {
      await api.backtest.createBacktest(formData)
      alert('回测任务创建成功！')
      router.push('/backtest')
    } catch (error: any) {
      alert(`创建回测失败: ${error.message || '未知错误'}`)
    } finally {
      setCreating(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/backtest" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200">
                <ArrowLeftIcon className="h-5 w-5" />
                <span className="font-medium">返回回测列表</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg">
                  <ChartBarIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">新建回测</h1>
                  <p className="text-sm text-gray-500">配置回测参数并开始分析</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <main className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow-xl rounded-2xl overflow-hidden">
          <div className="px-8 py-6 bg-gradient-to-r from-purple-600 to-indigo-600">
            <h2 className="text-2xl font-bold text-white">创建新回测</h2>
            <p className="text-purple-100 mt-1">基于历史信号数据进行策略回测和性能评估</p>
          </div>

          <form onSubmit={handleSubmit} className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <ChartBarIcon className="h-5 w-5 mr-2 text-purple-600" />
                    基本信息
                  </h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        回测名称 *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        placeholder="输入回测名称..."
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        交易对 (可选)
                      </label>
                      <select 
                        value={formData.symbol || ''}
                        onChange={(e) => setFormData({...formData, symbol: e.target.value || undefined})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      >
                        <option value="">所有交易对</option>
                        {symbols.map(symbol => (
                          <option key={symbol} value={symbol}>{symbol}</option>
                        ))}
                      </select>
                      <p className="text-xs text-gray-500 mt-1">
                        不选择则回测时间范围内该信号源的所有交易对
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                        初始资金 (USDT)
                      </label>
                      <input
                        type="number"
                        value={formData.initial_balance}
                        onChange={(e) => setFormData({...formData, initial_balance: Number(e.target.value)})}
                        min="100"
                        step="100"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <CalendarIcon className="h-5 w-5 mr-2 text-purple-600" />
                    时间范围
                  </h3>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        开始日期 *
                      </label>
                      <input
                        type="date"
                        value={formData.start_date}
                        onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        结束日期 *
                      </label>
                      <input
                        type="date"
                        value={formData.end_date}
                        onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    选择信号源 * ({formData.source_ids.length} 个已选择)
                  </label>
                  {loading && (
                    <div className="text-sm text-gray-500 mb-2">
                      正在加载信号源...
                    </div>
                  )}
                  <div className="max-h-64 overflow-y-auto border border-gray-300 rounded-lg p-4 bg-gray-50">
                    {sources.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <p>暂无可用的信号源</p>
                        <p className="text-sm mt-1">请先在<a href="/sources" className="text-blue-600 hover:underline">信号源管理</a>中添加信号源</p>
                        <button 
                          type="button"
                          onClick={loadData}
                          className="mt-2 text-sm text-blue-600 hover:underline"
                        >
                          重新加载
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {sources.map(source => (
                          <label key={source.id} className="flex items-start space-x-3 p-3 bg-white rounded-lg border hover:bg-gray-50 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={formData.source_ids.includes(source.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setFormData({...formData, source_ids: [...formData.source_ids, source.id]})
                                } else {
                                  setFormData({...formData, source_ids: formData.source_ids.filter(id => id !== source.id)})
                                }
                              }}
                              className="mt-1 rounded text-purple-600 focus:ring-purple-500"
                            />
                            <div className="flex-1">
                              <div className="font-medium text-gray-900">{source.name || source.telegram_chat_id}</div>
                              <div className="text-sm text-gray-500">
                                总信号: {source.total_signals || 0} • 成功率: {source.total_signals > 0 ? ((source.successful_signals / source.total_signals) * 100).toFixed(1) : '0.0'}%
                              </div>
                            </div>
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="flex justify-end space-x-4">
                <Link
                  href="/backtest"
                  className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors"
                >
                  取消
                </Link>
                <button 
                  type="submit"
                  disabled={creating}
                  className="px-8 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center"
                >
                  {creating ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      创建中...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-5 w-5 mr-2" />
                      开始回测
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </main>
    </div>
  )
}