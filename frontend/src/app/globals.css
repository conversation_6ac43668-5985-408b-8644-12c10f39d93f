@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

@layer base {
  html {
    @apply h-full;
  }
  
  body {
    @apply h-full bg-gray-50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* 自定义组件样式 */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 h-10 py-2 px-4;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 hover:bg-gray-50 hover:text-gray-900 h-10 py-2 px-4;
  }
  
  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900 h-10 py-2 px-4;
  }
  
  .btn-sm {
    @apply h-9 px-3 rounded-md;
  }
  
  .btn-lg {
    @apply h-11 px-8 rounded-md;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .form-input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .form-select {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .form-textarea {
    @apply flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-gray-500;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  .badge-default {
    @apply badge border-transparent bg-blue-600 text-white hover:bg-blue-700;
  }
  
  .badge-secondary {
    @apply badge border-transparent bg-gray-200 text-gray-900 hover:bg-gray-300;
  }
  
  .badge-success {
    @apply badge border-transparent bg-green-600 text-white hover:bg-green-700;
  }
  
  .badge-warning {
    @apply badge border-transparent bg-yellow-600 text-white hover:bg-yellow-700;
  }
  
  .badge-danger {
    @apply badge border-transparent bg-red-600 text-white hover:bg-red-700;
  }
  
  .badge-outline {
    @apply badge text-gray-900 border-gray-300;
  }
  
  /* 数据表格样式 */
  .data-table {
    @apply w-full border-collapse bg-white text-sm;
  }
  
  .data-table th {
    @apply border-b border-gray-200 bg-gray-50 px-4 py-3 text-left font-medium text-gray-700;
  }
  
  .data-table td {
    @apply border-b border-gray-200 px-4 py-3;
  }
  
  .data-table tr:hover {
    @apply bg-gray-50;
  }
  
  /* 状态指示器 */
  .status-indicator {
    @apply inline-flex items-center gap-1.5;
  }
  
  .status-dot {
    @apply h-2 w-2 rounded-full;
  }
  
  .status-pending .status-dot {
    @apply bg-yellow-500;
  }
  
  .status-processing .status-dot {
    @apply bg-blue-500 animate-pulse;
  }
  
  .status-executed .status-dot {
    @apply bg-green-500;
  }
  
  .status-failed .status-dot {
    @apply bg-red-500;
  }
  
  .status-cancelled .status-dot {
    @apply bg-gray-500;
  }
  
  /* 图表容器 */
  .chart-container {
    @apply w-full h-64 p-4;
  }
  
  .chart-container.lg {
    @apply h-96;
  }
  
  /* 导航样式 */
  .nav-link {
    @apply flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-md transition-colors;
  }
  
  .nav-link.active {
    @apply bg-blue-100 text-blue-900;
  }
  
  .nav-link:hover {
    @apply bg-gray-100 text-gray-900;
  }
  
  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
} 