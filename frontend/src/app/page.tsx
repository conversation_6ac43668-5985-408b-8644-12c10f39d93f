'use client'

import Link from 'next/link'
import { useEffect, useState } from 'react'
import { 
  ChartBarIcon, 
  SignalIcon, 
  CogIcon, 
  PlayCircleIcon,
  StopCircleIcon,
  ExclamationTriangleIcon,
  BoltIcon
} from '@heroicons/react/24/outline'
import api, { Signal, SignalStats, TelegramSignal } from '@/lib/api'

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}

function getTimeAgo(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return `${diffInSeconds}秒前`
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`
  return `${Math.floor(diffInSeconds / 86400)}天前`
}

export default function HomePage() {
  const [loading, setLoading] = useState(true)
  const [statistics, setStatistics] = useState<SignalStats | null>(null)
  const [recentSignals, setRecentSignals] = useState<TelegramSignal[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // 调试：打印API URL
        console.log('当前API URL:', process.env.NEXT_PUBLIC_API_URL)
        console.log('Window配置:', (window as any).__RUNTIME_CONFIG__)
        
        // 并行获取统计数据和最近交易
        const [statsResponse, signalsResponse] = await Promise.all([
          api.signals.getSignalStats(30),
          api.signals.getSignals({ page: 1, page_size: 5 })
        ])
        
        setStatistics(statsResponse.data || statsResponse)
        setRecentSignals(signalsResponse.items || [])
      } catch (err) {
        console.error('获取数据失败:', err)
        setError('获取数据失败，请检查后端服务是否运行')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // 构建统计卡片数据
  const stats = statistics ? [
    {
      name: '总交易数',
      value: statistics.total_signals?.toString() || '0',
      change: '+0%',
      changeType: 'positive',
      icon: SignalIcon,
    },
    {
      name: '执行率',
      value: `${statistics.success_rate?.toFixed(1) || '0'}%`,
      change: '+0%',
      changeType: 'positive',
      icon: ChartBarIcon,
    },
    {
      name: '处理中交易',
      value: statistics.status_distribution?.PROCESSING?.toString() || '0',
      change: '+0%',
      changeType: 'positive',
      icon: PlayCircleIcon,
    },
    {
      name: '活跃信号源',
      value: Object.keys(statistics.source_distribution || {}).length.toString(),
      change: '+0%',
      changeType: 'positive',
      icon: ChartBarIcon,
    },
  ] : []

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <img 
                src="/home.png" 
                alt="Husky Logo" 
                className="h-8 w-8 mr-3"
              />
              <h1 className="text-xl font-semibold text-gray-900">
                Husky
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link href="/signals" className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <SignalIcon className="h-4 w-4 mr-2" />
                交易管理
              </Link>
              <Link href="/signals/management" className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <BoltIcon className="h-4 w-4 mr-2" />
                信号管理
              </Link>
              <Link href="/sources" className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <ChartBarIcon className="h-4 w-4 mr-2" />
                信号源
              </Link>
              <Link href="/backtest" className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <PlayCircleIcon className="h-4 w-4 mr-2" />
                回测分析
              </Link>
              <div className="relative group">
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <CogIcon className="h-4 w-4 mr-2" />
                  系统管理
                  <svg className="ml-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="py-1">
                    <Link href="/config" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      配置管理
                    </Link>
                    <Link href="/signal-test" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      信号测试
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* 调试信息 */}
        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="ml-3">
            <p className="text-sm text-blue-800">
              调试信息 - API URL: {process.env.NEXT_PUBLIC_API_URL || 'undefined'}
            </p>
          </div>
        </div>
        
        {/* 系统状态警告 */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}
        
        {!error && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
              <div className="ml-3">
                <p className="text-sm text-yellow-800">
                  这是演示系统，请在生产环境中配置正确的Telegram API认证信息和交易所API密钥。
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          {stats.map((item) => (
            <div key={item.name} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <item.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {item.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {item.value}
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex items-center text-sm">
                  <span
                    className={classNames(
                      item.changeType === 'positive'
                        ? 'text-green-600'
                        : 'text-red-600',
                      'font-medium'
                    )}
                  >
                    {item.change}
                  </span>
                  <span className="text-gray-500 ml-1">较昨日</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 最近交易 */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">最近交易</h3>
              <p className="text-sm text-gray-500 mb-4">
                最新的交易记录
              </p>
              <div className="space-y-4">
                {recentSignals.length > 0 ? (
                  recentSignals.map((signal) => (
                    <div key={signal.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center gap-2">
                          <div className={`h-2 w-2 rounded-full ${
                            signal.status === 'EXECUTED' ? 'bg-green-500' :
                            signal.status === 'PENDING' ? 'bg-yellow-500' : 'bg-red-500'
                          }`}></div>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {signal.signal_type} {signal.symbol}
                          </p>
                          <p className="text-sm text-gray-500">
                            {signal.entry_price ? `价格: $${signal.entry_price.toFixed(4)}` : '价格: --'} | 杠杆: {signal.leverage || 1}x
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          signal.status === 'EXECUTED' ? 'bg-green-100 text-green-800' :
                          signal.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 
                          signal.status === 'PROCESSING' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {signal.status === 'EXECUTED' ? '已执行' :
                           signal.status === 'PENDING' ? '等待中' : 
                           signal.status === 'PROCESSING' ? '处理中' : '失败'}
                        </span>
                        <p className="text-xs text-gray-500 mt-1">{getTimeAgo(signal.signal_time)}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <SignalIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p>暂无最近交易</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 系统状态 */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">系统状态</h3>
              <p className="text-sm text-gray-500 mb-4">
                各组件运行状态
              </p>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">Telegram客户端</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    运行中
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">Freqtrade</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    未连接
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">数据库</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    正常
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">Redis</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    正常
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
              <Link href="/signals" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <SignalIcon className="h-8 w-8 text-blue-600 mb-2" />
                <h4 className="font-medium text-gray-900">交易管理</h4>
                <p className="text-sm text-gray-500">查看和管理交易记录</p>
              </Link>
              <Link href="/signals/management" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <BoltIcon className="h-8 w-8 text-indigo-600 mb-2" />
                <h4 className="font-medium text-gray-900">信号管理</h4>
                <p className="text-sm text-gray-500">统一管理信号生命周期</p>
              </Link>
              <Link href="/sources" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <ChartBarIcon className="h-8 w-8 text-green-600 mb-2" />
                <h4 className="font-medium text-gray-900">信号源</h4>
                <p className="text-sm text-gray-500">管理Telegram信号源</p>
              </Link>
              <Link href="/backtest" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <PlayCircleIcon className="h-8 w-8 text-purple-600 mb-2" />
                <h4 className="font-medium text-gray-900">回测</h4>
                <p className="text-sm text-gray-500">历史交易回测分析</p>
              </Link>
              <Link href="/config" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <CogIcon className="h-8 w-8 text-orange-600 mb-2" />
                <h4 className="font-medium text-gray-900">系统配置</h4>
                <p className="text-sm text-gray-500">系统参数设置</p>
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
} 