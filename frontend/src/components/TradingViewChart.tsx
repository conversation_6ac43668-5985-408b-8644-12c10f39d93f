'use client'

import { useEffect, useRef, memo } from 'react'

interface TradingViewChartProps {
  symbol: string
  entryPrice?: number
  stopLoss?: number
  takeProfit?: number
  signalType?: string
  signalTime?: string
  height?: number
  theme?: 'light' | 'dark'
}

declare global {
  interface Window {
    TradingView: any
  }
}

const TradingViewChart = memo(function TradingViewChart({
  symbol,
  entryPrice,
  stopLoss,
  takeProfit,
  signalType,
  signalTime,
  height = 500,
  theme = 'light'
}: TradingViewChartProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const widgetRef = useRef<any>(null)

  useEffect(() => {
    // 清理之前的图表
    if (widgetRef.current) {
      try {
        widgetRef.current.remove()
      } catch (e) {
        console.warn('清理TradingView图表失败:', e)
      }
      widgetRef.current = null
    }

    if (!containerRef.current) return

    // 加载TradingView脚本
    const loadTradingView = () => {
      if (typeof window.TradingView !== 'undefined') {
        createChart()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://s3.tradingview.com/tv.js'
      script.async = true
      script.onload = createChart
      script.onerror = () => {
        console.error('加载TradingView脚本失败')
      }
      document.head.appendChild(script)
    }

    const createChart = () => {
      if (!containerRef.current || !window.TradingView) return

      try {
        // 保持原始交易对格式，TradingView支持USDT交易对
        const tvSymbol = symbol

        // 创建图表配置
        const chartConfig: any = {
          autosize: true,
          symbol: `BINANCE:${tvSymbol}`,
          interval: '5', // 改为5分钟K线
          timezone: 'Asia/Shanghai',
          theme: theme,
          style: '1',
          locale: 'zh_CN',
          toolbar_bg: '#f1f3f6',
          enable_publishing: false,
          hide_top_toolbar: false,
          hide_legend: false,
          save_image: false,
          container_id: containerRef.current.id,
          height: height,
          studies: [
            'Volume@tv-basicstudies'
          ],
          overrides: {
            'paneProperties.background': theme === 'dark' ? '#1e1e1e' : '#ffffff',
            'paneProperties.vertGridProperties.color': theme === 'dark' ? '#2a2a2a' : '#e1e1e1',
            'paneProperties.horzGridProperties.color': theme === 'dark' ? '#2a2a2a' : '#e1e1e1',
            'symbolWatermarkProperties.transparency': 90,
            'scalesProperties.textColor': theme === 'dark' ? '#d1d4dc' : '#131722',
          }
        }

        // 如果有信号时间，设置时间范围
        if (signalTime) {
          const signalDate = new Date(signalTime)
          const startTime = new Date(signalDate.getTime() - 24 * 60 * 60 * 1000) // 信号前24小时
          const endTime = new Date(signalDate.getTime() + 24 * 60 * 60 * 1000)   // 信号后24小时
          
          chartConfig.time_frames = [
            { text: '1h', resolution: '60' },
            { text: '4h', resolution: '240' },
            { text: '1D', resolution: '1D' }
          ]
        }

        widgetRef.current = new window.TradingView.widget(chartConfig)

        // 图表加载完成后添加标记
        widgetRef.current.onChartReady(() => {
          addSignalMarkers()
        })

      } catch (error) {
        console.error('创建TradingView图表失败:', error)
      }
    }

    const addSignalMarkers = () => {
      if (!widgetRef.current || !signalTime) return

      try {
        const chart = widgetRef.current.chart()
        const signalTimestamp = new Date(signalTime).getTime() / 1000

        console.log('开始添加信号标记...', {
          signalTimestamp,
          entryPrice,
          stopLoss,
          takeProfit,
          signalType
        })

        // 添加入场价格水平线
        if (entryPrice) {
          const isLong = signalType?.toLowerCase() === 'long'

          // 创建入场价格水平线
          chart.createShape({
            time: signalTimestamp,
            price: entryPrice
          }, {
            shape: 'horizontal_line',
            text: `入场 $${entryPrice}`,
            overrides: {
              color: isLong ? '#26a69a' : '#ef5350',
              linewidth: 2,
              linestyle: 0, // 实线
              textColor: isLong ? '#26a69a' : '#ef5350',
              fontsize: 12,
              bold: true,
              showLabel: true,
              horzLabelsAlign: 'right',
              vertLabelsAlign: 'middle'
            }
          })

          // 添加入场点标记
          chart.createShape({
            time: signalTimestamp,
            price: entryPrice
          }, {
            shape: isLong ? 'arrow_up' : 'arrow_down',
            text: `${signalType?.toUpperCase()}`,
            overrides: {
              color: isLong ? '#26a69a' : '#ef5350',
              textColor: '#ffffff',
              fontsize: 10,
              bold: true
            }
          })
        }

        // 添加止损价格水平线
        if (stopLoss) {
          chart.createShape({
            time: signalTimestamp,
            price: stopLoss
          }, {
            shape: 'horizontal_line',
            text: `止损 $${stopLoss}`,
            overrides: {
              color: '#f44336',
              linewidth: 2,
              linestyle: 1, // 虚线
              textColor: '#f44336',
              fontsize: 11,
              bold: true,
              showLabel: true,
              horzLabelsAlign: 'right',
              vertLabelsAlign: 'middle'
            }
          })
        }

        // 添加止盈价格水平线
        if (takeProfit) {
          chart.createShape({
            time: signalTimestamp,
            price: takeProfit
          }, {
            shape: 'horizontal_line',
            text: `止盈 $${takeProfit}`,
            overrides: {
              color: '#4caf50',
              linewidth: 2,
              linestyle: 1, // 虚线
              textColor: '#4caf50',
              fontsize: 11,
              bold: true,
              showLabel: true,
              horzLabelsAlign: 'right',
              vertLabelsAlign: 'middle'
            }
          })
        }

        // 添加信号时间垂直线
        chart.createShape({
          time: signalTimestamp,
          price: entryPrice || stopLoss || takeProfit || 0
        }, {
          shape: 'vertical_line',
          text: '信号时间',
          overrides: {
            color: '#2196f3',
            linewidth: 1,
            linestyle: 2, // 点线
            textColor: '#2196f3',
            fontsize: 10,
            transparency: 30
          }
        })

        console.log('信号标记添加完成')

      } catch (error) {
        console.error('添加信号标记失败:', error)
        // 如果标记失败，尝试简单的文本标记
        addFallbackMarkers()
      }
    }

    const addFallbackMarkers = () => {
      if (!widgetRef.current || !signalTime) return

      try {
        const chart = widgetRef.current.chart()
        const signalTimestamp = new Date(signalTime).getTime() / 1000

        console.log('使用备用标记方案...')

        // 简单的文本标记
        if (entryPrice) {
          chart.createShape({
            time: signalTimestamp,
            price: entryPrice
          }, {
            shape: 'text',
            text: `入场: $${entryPrice}`,
            overrides: {
              color: '#2196f3',
              textColor: '#2196f3',
              fontsize: 12,
              bold: true,
              backgroundColor: '#ffffff',
              borderColor: '#2196f3'
            }
          })
        }

        if (stopLoss) {
          chart.createShape({
            time: signalTimestamp + 1800, // 30分钟后
            price: stopLoss
          }, {
            shape: 'text',
            text: `止损: $${stopLoss}`,
            overrides: {
              color: '#f44336',
              textColor: '#f44336',
              fontsize: 11,
              bold: true,
              backgroundColor: '#ffffff',
              borderColor: '#f44336'
            }
          })
        }

        if (takeProfit) {
          chart.createShape({
            time: signalTimestamp + 3600, // 1小时后
            price: takeProfit
          }, {
            shape: 'text',
            text: `止盈: $${takeProfit}`,
            overrides: {
              color: '#4caf50',
              textColor: '#4caf50',
              fontsize: 11,
              bold: true,
              backgroundColor: '#ffffff',
              borderColor: '#4caf50'
            }
          })
        }

      } catch (error) {
        console.error('备用标记方案也失败:', error)
      }
    }

    loadTradingView()

    return () => {
      if (widgetRef.current) {
        try {
          widgetRef.current.remove()
        } catch (e) {
          console.warn('清理TradingView图表失败:', e)
        }
        widgetRef.current = null
      }
    }
  }, [symbol, entryPrice, stopLoss, takeProfit, signalType, signalTime, height, theme])

  return (
    <div className="w-full">
      <div 
        ref={containerRef}
        id={`tradingview_${Math.random().toString(36).substr(2, 9)}`}
        style={{ height: `${height}px` }}
        className="rounded-lg overflow-hidden border border-gray-200"
      />
      <div className="mt-2 text-xs text-gray-500 text-center">
        <p>数据来源: TradingView • 仅供参考，不构成投资建议</p>
      </div>
    </div>
  )
})

export default TradingViewChart
