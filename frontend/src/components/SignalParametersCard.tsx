'use client'

import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  XCircleIcon,
  CheckCircleIcon,
  CurrencyDollarIcon,
  CalculatorIcon
} from '@heroicons/react/24/outline'

interface SignalParametersCardProps {
  signal: {
    symbol: string
    signal_type: string
    entry_price?: number
    stop_loss?: number
    take_profit?: number
    leverage?: number
  }
}

export default function SignalParametersCard({ signal }: SignalParametersCardProps) {
  // 计算风险收益比
  const calculateRiskReward = () => {
    if (!signal.entry_price || !signal.stop_loss || !signal.take_profit) {
      return null
    }
    
    const risk = Math.abs(signal.entry_price - signal.stop_loss)
    const reward = Math.abs(signal.take_profit - signal.entry_price)
    
    if (risk === 0) return null
    
    return {
      ratio: reward / risk,
      risk: risk,
      reward: reward,
      riskPercent: (risk / signal.entry_price) * 100,
      rewardPercent: (reward / signal.entry_price) * 100
    }
  }

  const riskReward = calculateRiskReward()
  const isLong = signal.signal_type?.toLowerCase() === 'long'

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <CalculatorIcon className="h-5 w-5 mr-2 text-blue-600" />
          交易参数分析
        </h3>
      </div>
      
      <div className="px-6 py-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          {/* 入场价格 */}
          {signal.entry_price && (
            <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-center mb-2">
                {isLong ? (
                  <ArrowUpIcon className="h-8 w-8 text-blue-600" />
                ) : (
                  <ArrowDownIcon className="h-8 w-8 text-blue-600" />
                )}
              </div>
              <p className="text-sm text-blue-600 font-medium">入场价格</p>
              <p className="text-xl font-bold text-blue-700">${signal.entry_price}</p>
              <p className="text-xs text-blue-500 mt-1">
                {isLong ? '做多入场' : '做空入场'}
              </p>
            </div>
          )}

          {/* 止损价格 */}
          {signal.stop_loss && (
            <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-center justify-center mb-2">
                <XCircleIcon className="h-8 w-8 text-red-600" />
              </div>
              <p className="text-sm text-red-600 font-medium">止损价格</p>
              <p className="text-xl font-bold text-red-700">${signal.stop_loss}</p>
              {signal.entry_price && (
                <p className="text-xs text-red-500 mt-1">
                  {((Math.abs(signal.stop_loss - signal.entry_price) / signal.entry_price) * 100).toFixed(2)}% 风险
                </p>
              )}
            </div>
          )}

          {/* 止盈价格 */}
          {signal.take_profit && (
            <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center justify-center mb-2">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-sm text-green-600 font-medium">止盈价格</p>
              <p className="text-xl font-bold text-green-700">${signal.take_profit}</p>
              {signal.entry_price && (
                <p className="text-xs text-green-500 mt-1">
                  {((Math.abs(signal.take_profit - signal.entry_price) / signal.entry_price) * 100).toFixed(2)}% 收益
                </p>
              )}
            </div>
          )}

          {/* 杠杆倍数 */}
          {signal.leverage && (
            <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
              <div className="flex items-center justify-center mb-2">
                <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
              </div>
              <p className="text-sm text-purple-600 font-medium">杠杆倍数</p>
              <p className="text-xl font-bold text-purple-700">{signal.leverage}x</p>
              <p className="text-xs text-purple-500 mt-1">
                资金放大
              </p>
            </div>
          )}
        </div>

        {/* 风险收益分析 */}
        {riskReward && (
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <h4 className="text-md font-semibold text-gray-900 mb-3">风险收益分析</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">风险收益比</p>
                <p className={`text-2xl font-bold ${riskReward.ratio >= 2 ? 'text-green-600' : riskReward.ratio >= 1 ? 'text-yellow-600' : 'text-red-600'}`}>
                  1:{riskReward.ratio.toFixed(2)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {riskReward.ratio >= 2 ? '优秀' : riskReward.ratio >= 1 ? '一般' : '较差'}
                </p>
              </div>
              
              <div className="text-center">
                <p className="text-sm text-gray-600">潜在风险</p>
                <p className="text-lg font-semibold text-red-600">
                  ${riskReward.risk.toFixed(2)}
                </p>
                <p className="text-xs text-red-500 mt-1">
                  {riskReward.riskPercent.toFixed(2)}%
                </p>
              </div>
              
              <div className="text-center">
                <p className="text-sm text-gray-600">潜在收益</p>
                <p className="text-lg font-semibold text-green-600">
                  ${riskReward.reward.toFixed(2)}
                </p>
                <p className="text-xs text-green-500 mt-1">
                  {riskReward.rewardPercent.toFixed(2)}%
                </p>
              </div>
            </div>

            {/* 风险收益比说明 */}
            <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
              <p className="text-sm text-blue-700">
                <strong>风险收益比说明:</strong>
              </p>
              <ul className="text-xs text-blue-600 mt-1 space-y-1">
                <li>• 比例 ≥ 2:1 被认为是优秀的交易机会</li>
                <li>• 比例 1:1 - 2:1 为一般交易机会</li>
                <li>• 比例 &lt; 1:1 风险较高，需谨慎考虑</li>
              </ul>
            </div>
          </div>
        )}

        {/* 价格距离分析 */}
        {signal.entry_price && (signal.stop_loss || signal.take_profit) && (
          <div className="mt-4 bg-gray-50 rounded-lg p-4 border border-gray-200">
            <h4 className="text-md font-semibold text-gray-900 mb-3">价格距离分析</h4>
            
            <div className="space-y-3">
              {signal.stop_loss && (
                <div className="flex items-center justify-between p-2 bg-red-50 rounded border border-red-200">
                  <span className="text-sm text-red-700">距离止损</span>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-red-700">
                      ${Math.abs(signal.entry_price - signal.stop_loss).toFixed(2)}
                    </span>
                    <span className="text-xs text-red-500 ml-2">
                      ({((Math.abs(signal.stop_loss - signal.entry_price) / signal.entry_price) * 100).toFixed(2)}%)
                    </span>
                  </div>
                </div>
              )}
              
              {signal.take_profit && (
                <div className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200">
                  <span className="text-sm text-green-700">距离止盈</span>
                  <div className="text-right">
                    <span className="text-sm font-semibold text-green-700">
                      ${Math.abs(signal.take_profit - signal.entry_price).toFixed(2)}
                    </span>
                    <span className="text-xs text-green-500 ml-2">
                      ({((Math.abs(signal.take_profit - signal.entry_price) / signal.entry_price) * 100).toFixed(2)}%)
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
