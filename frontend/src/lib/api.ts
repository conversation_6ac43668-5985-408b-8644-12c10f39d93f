// API客户端配置
// 动态获取API URL，支持运行时环境变量
function getApiBaseUrl() {
  if (typeof window !== 'undefined') {
    // 客户端：从window对象获取注入的环境变量
    return (window as any).__RUNTIME_CONFIG__?.API_BASE_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  }
  // 服务端：使用环境变量
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
}

// 通用API请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${getApiBaseUrl()}/api/v1${endpoint}`
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  try {
    const response = await fetch(url, config)
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}))
      throw new Error(error.detail || `HTTP Error: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

// 数据类型定义
export interface Signal {
  id: number
  symbol: string
  signal_type: 'BUY' | 'SELL'
  entry_price?: number
  stop_loss?: number
  take_profit?: number
  status: 'PENDING' | 'PROCESSING' | 'EXECUTED' | 'FAILED'
  source_id: number
  signal_time: string
  processed_at?: string
  created_at: string
}

export interface SignalSource {
  id: number
  telegram_chat_id: string
  description?: string
  is_active: boolean
  total_signals: number
  successful_signals: number
  failed_signals: number
  created_at: string
  updated_at: string
}

export interface BacktestResult {
  id: number
  name: string
  description?: string
  symbol: string
  start_date: string
  end_date: string
  initial_balance: number
  final_balance?: number
  total_return?: number
  total_trades?: number
  winning_trades?: number
  losing_trades?: number
  win_rate?: number
  profit_factor?: number
  max_drawdown?: number
  sharpe_ratio?: number
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED'
  created_at: string
  started_at?: string
  completed_at?: string
  error_message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

export interface APIResponse {
  success: boolean
  message: string
}

export interface TelegramUser {
  id: number
  username?: string
  phone?: string
  first_name?: string
  last_name?: string
  is_authenticated: boolean
}

export interface TelegramDialog {
  id: string
  title: string
  type: string
  description?: string
  username?: string
  member_count?: number
  unread_count?: number
  is_muted?: boolean
  date?: string
}

export interface TelegramChat {
  id: string
  title: string
  type: string
  description?: string
  username?: string
  member_count?: number
}

export interface TelegramSignal {
  id: number
  symbol: string
  signal_type: string
  entry_price?: number
  stop_loss?: number
  take_profit?: number
  leverage?: number
  status: string
  source_id: number
  signal_time: string
  received_at: string
  raw_message: string
  result?: string
  pnl?: number
  pnl_percentage?: number
  
  // 新增字段
  entry_points_count: number
  tp_points_count: number
  entry_status: string
  tp_status: string
  overall_status: string
  total_entry_filled: number
  total_tp_filled: number
  total_pnl: number
}

export interface SignalStats {
  total_signals: number
  success_rate: number
  avg_pnl: number
  status_distribution: Record<string, number>
  type_distribution: Record<string, number>
  source_distribution: Record<string, number>
  period_days: number
}

export interface SignalFilter {
  page?: number
  page_size?: number
  symbol?: string
  signal_type?: string
  status?: string
  source_id?: number
  start_date?: string
  end_date?: string
  search?: string
}

// 高级交易配置相关接口
export interface EntryPoint {
  price: number
  allocation: number
  description: string
}

export interface TakeProfitPoint {
  price: number
  allocation: number
  description: string
}

export interface AdvancedTradeConfig {
  symbol: string
  signal_type: string
  entry_points: EntryPoint[]
  auto_distribute_funds: boolean
  take_profit_points: TakeProfitPoint[]
  stop_loss: number | null
  total_quantity: number | null
  leverage: number
  raw_message: string
  description: string
}

// 新增的交易详情接口
export interface TradeEntryPoint {
  id: number
  price: number
  allocation: number
  description: string
  status: string
  filled_quantity: number
  actual_price?: number
  order_id?: string
  created_at: string
  filled_at?: string
}

export interface TradeTakeProfitPoint {
  id: number
  price: number
  allocation: number
  description: string
  status: string
  filled_quantity: number
  actual_price?: number
  order_id?: string
  pnl?: number
  pnl_percentage?: number
  created_at: string
  filled_at?: string
}

export interface TradeDetail {
  signal: TelegramSignal
  entry_points: TradeEntryPoint[]
  take_profit_points: TradeTakeProfitPoint[]
  trades: any[]
  total_entry_filled: number
  total_tp_filled: number
  total_pnl: number
  total_pnl_percentage: number
  overall_status: string
}

export interface CreateTradeRequest {
  symbol: string
  signal_type: string
  entry_points: EntryPoint[]
  take_profit_points: TakeProfitPoint[]
  stop_loss?: number
  leverage: number
  raw_message?: string
  description?: string
}

// 编辑相关的类型定义
export interface EditableTradeEntryPoint extends TradeEntryPoint {
  id?: number
  status?: string
  filled_quantity?: number
  actual_price?: number
  editable: boolean
}

export interface EditableTradeTakeProfitPoint extends TradeTakeProfitPoint {
  id?: number
  status?: string
  filled_quantity?: number
  actual_price?: number
  pnl?: number
  pnl_percentage?: number
  editable: boolean
}

export interface TradeEditData {
  id: number
  symbol: string
  signal_type: string
  stop_loss: number | null
  leverage: number
  raw_message: string
  entry_points: EditableTradeEntryPoint[]
  take_profit_points: EditableTradeTakeProfitPoint[]
  editable_fields: {
    symbol: boolean
    signal_type: boolean
    stop_loss: boolean
    leverage: boolean
    raw_message: boolean
    entry_points: boolean
    take_profit_points: boolean
  }
}

// 信号相关API
export const signalAPI = {
  // 获取信号列表
  getSignals: async (filters: SignalFilter = {}): Promise<PaginatedResponse<TelegramSignal>> => {
    const params = new URLSearchParams()
    
    // 添加过滤参数
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.page_size) params.append('page_size', filters.page_size.toString())
    if (filters.symbol) params.append('symbol', filters.symbol)
    if (filters.signal_type) params.append('signal_type', filters.signal_type)
    if (filters.status) params.append('status', filters.status)
    if (filters.source_id) params.append('source_id', filters.source_id.toString())
    if (filters.start_date) params.append('start_date', filters.start_date)
    if (filters.end_date) params.append('end_date', filters.end_date)
    if (filters.search) params.append('search', filters.search)
    
    return apiRequest<PaginatedResponse<TelegramSignal>>(`/signals?${params}`)
  },

  // 获取信号统计
  getSignalStats: async (days: number = 30): Promise<{
    success: boolean
    data: SignalStats
  }> => {
    return apiRequest<{
      success: boolean
      data: SignalStats
    }>(`/signals/stats/overview?days=${days}`)
  },

  // 获取信号源列表
  getSignalSources: async (): Promise<{
    success: boolean
    data: SignalSource[]
  }> => {
    return apiRequest<{
      success: boolean
      data: SignalSource[]
    }>('/signals/sources/list')
  },

  // 删除信号
  deleteSignal: async (signalId: number): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/signals/${signalId}`, {
      method: 'DELETE'
    })
  },

  // 更新信号状态
  updateSignalStatus: async (signalId: number, status: string): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/signals/${signalId}/status`, {
      method: 'PUT',
      body: JSON.stringify(status)
    })
  },

  // 获取信号详情
  getSignalDetail: async (signalId: number): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/signals/${signalId}`)
  },

  // 解析信号文本
  parseSignalText: async (rawMessage: string): Promise<{
    success: boolean
    message: string
    data?: any
  }> => {
    return apiRequest<{
      success: boolean
      message: string
      data?: any
    }>('/signals/parse', {
      method: 'POST',
      body: JSON.stringify({ raw_message: rawMessage })
    })
  },

  // 创建手动信号
  createManualSignal: async (data: {
    raw_message: string
  }): Promise<APIResponse> => {
    return apiRequest<APIResponse>('/signals/create', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // 创建高级交易
  createAdvancedTrade: async (config: AdvancedTradeConfig): Promise<APIResponse> => {
    return apiRequest<APIResponse>('/signals/advanced-trade', {
      method: 'POST',
      body: JSON.stringify({ config })
    })
  },

  // 获取交易详情
  getTradeDetail: async (signalId: number): Promise<{
    success: boolean
    message: string
    data: TradeDetail
  }> => {
    return apiRequest<{
      success: boolean
      message: string
      data: TradeDetail
    }>(`/signals/${signalId}/detail`)
  },

  // 创建新交易
  createTrade: async (data: CreateTradeRequest): Promise<{
    success: boolean
    message: string
    data: { signal_id: number }
  }> => {
    return apiRequest<{
      success: boolean
      message: string
      data: { signal_id: number }
    }>('/signals/create-trade', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // 更新入场点
  updateEntryPoint: async (entryPointId: number, data: {
    price?: number
    allocation?: number
    description?: string
    status?: string
  }): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/signals/entry-point/${entryPointId}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  },

  // 更新止盈点
  updateTakeProfitPoint: async (tpPointId: number, data: {
    price?: number
    allocation?: number
    description?: string
    status?: string
  }): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/signals/take-profit-point/${tpPointId}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  },

  // 获取高级交易模板
  getAdvancedTradeTemplate: async (): Promise<{
    success: boolean
    message: string
    data: any
  }> => {
    return apiRequest<{
      success: boolean
      message: string
      data: any
    }>('/signals/advanced-trade/template')
  },

  // 获取交易编辑数据
  getTradeEditData: async (signalId: number): Promise<APIResponse<TradeEditData>> => {
    return apiRequest<APIResponse<TradeEditData>>(`/signals/${signalId}/edit`)
  },

  // 更新交易
  updateTrade: async (signalId: number, tradeData: CreateTradeRequest): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/signals/${signalId}`, {
      method: 'PUT',
      body: JSON.stringify(tradeData)
    })
  }
}

// 信号源相关API
export const sourceAPI = {
  // 获取信号源列表
  getSources: async (activeOnly: boolean = true): Promise<SignalSource[]> => {
    return apiRequest<SignalSource[]>(`/sources/?active_only=${activeOnly}`)
  },

  // 获取单个信号源详情
  getSource: async (id: number): Promise<SignalSource> => {
    return apiRequest<SignalSource>(`/sources/${id}`)
  },

  // Telegram用户认证
  authenticateTelegram: async (data: {
    phone_number: string
    code?: string
    password?: string
  }): Promise<{
    success: boolean
    authenticated?: boolean
    code_sent?: boolean
    two_factor_required?: boolean
    user_info?: TelegramUser
    session_string?: string
    message?: string
    error?: string
  }> => {
    return apiRequest<{
      success: boolean
      authenticated?: boolean
      code_sent?: boolean
      two_factor_required?: boolean
      user_info?: TelegramUser
      session_string?: string
      message?: string
      error?: string
    }>('/sources/telegram/auth', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // Telegram用户登出
  logoutTelegram: async (): Promise<{
    success: boolean
    message: string
    error?: string
  }> => {
    return apiRequest<{
      success: boolean
      message: string
      error?: string
    }>('/sources/telegram/logout', {
      method: 'POST'
    })
  },

  // 重置Telegram认证状态
  resetTelegram: async (): Promise<{
    success: boolean
    message: string
    error?: string
  }> => {
    return apiRequest<{
      success: boolean
      message: string
      error?: string
    }>('/sources/telegram/reset', {
      method: 'POST'
    })
  },

  // 获取Telegram用户信息
  getTelegramUser: async (): Promise<{
    success: boolean
    user?: TelegramUser
    error?: string
  }> => {
    return apiRequest<{
      success: boolean
      user?: TelegramUser
      error?: string
    }>('/sources/telegram/user')
  },

  // 获取Telegram对话列表
  getTelegramDialogs: async (search?: string, limit: number = 100): Promise<{
    success: boolean
    dialogs: TelegramDialog[]
    total: number
    message?: string
  }> => {
    const params = new URLSearchParams()
    if (search) {
      params.append('search', search)
    }
    params.append('limit', limit.toString())
    
    return apiRequest<{
      success: boolean
      dialogs: TelegramDialog[]
      total: number
      message?: string
    }>(`/sources/telegram/dialogs?${params.toString()}`)
  },

  // 刷新Telegram对话列表
  refreshTelegramDialogs: async (): Promise<{
    success: boolean
    message: string
    total?: number
  }> => {
    return apiRequest<{
      success: boolean
      message: string
      total?: number
    }>('/sources/telegram/dialogs/refresh', {
      method: 'POST'
    })
  },

  // 选择Telegram对话作为信号源
  selectTelegramDialogs: async (dialogIds: string[]): Promise<{
    success: boolean
    message: string
    created_sources?: Array<{ id: number; name: string; telegram_chat_id: string }>
    updated_sources?: Array<{ id: number; name: string; telegram_chat_id: string }>
  }> => {
    return apiRequest<{
      success: boolean
      message: string
      created_sources?: Array<{ id: number; name: string; telegram_chat_id: string }>
      updated_sources?: Array<{ id: number; name: string; telegram_chat_id: string }>
    }>('/sources/telegram/dialogs/select', {
      method: 'POST',
      body: JSON.stringify({
        selected_dialogs: dialogIds
      })
    })
  },

  // 创建信号源
  createSource: async (data: {
    name: string
    telegram_chat_id: string
    description?: string
    is_active?: boolean
  }): Promise<SignalSource> => {
    return apiRequest<SignalSource>('/sources', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // 更新信号源
  updateSource: async (id: number, data: {
    name?: string
    telegram_chat_id?: string
    description?: string
    is_active?: boolean
  }): Promise<SignalSource> => {
    return apiRequest<SignalSource>(`/sources/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  },

  // 删除信号源
  deleteSource: async (id: number): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/sources/${id}`, {
      method: 'DELETE'
    })
  },

  // 获取信号源统计
  getSourceStatistics: async (id: number, days: number = 30) => {
    return apiRequest(`/sources/${id}/statistics?days=${days}`)
  },

  // 测试信号源连接
  testSourceConnection: async (id: number) => {
    return apiRequest(`/sources/${id}/test-connection`, {
      method: 'POST'
    })
  },

  // 拉取信号源历史消息
  fetchSourceHistory: async (
    id: number, 
    params: {
      limit?: number
      days_back?: number
      skip_replies?: boolean
    } = {}
  ): Promise<{
    success: boolean
    message: string
    data?: {
      total_processed: number
      signals_found: number
      skipped: number
      duplicates: number
      messages: Array<{
        id: string
        text: string
        date: string
        signal_type: string
        symbol: string
      }>
    }
  }> => {
    const searchParams = new URLSearchParams()
    
    if (params.limit) searchParams.append('limit', params.limit.toString())
    if (params.days_back) searchParams.append('days_back', params.days_back.toString())
    if (params.skip_replies !== undefined) searchParams.append('skip_replies', params.skip_replies.toString())
    
    return apiRequest<{
      success: boolean
      message: string
      data?: {
        total_processed: number
        signals_found: number
        skipped: number
        duplicates: number
        messages: Array<{
          id: string
          text: string
          date: string
          signal_type: string
          symbol: string
        }>
      }
    }>(`/sources/${id}/fetch-history?${searchParams}`, {
      method: 'POST'
    })
  }
}

// 回测相关API
export const backtestAPI = {
  // 获取回测列表
  getBacktests: async (params: {
    page?: number
    size?: number
    status?: string
    symbol?: string
  } = {}): Promise<PaginatedResponse<BacktestResult>> => {
    const searchParams = new URLSearchParams()
    
    if (params.page) searchParams.append('page', params.page.toString())
    if (params.size) searchParams.append('size', params.size.toString())
    if (params.status) searchParams.append('status', params.status)
    if (params.symbol) searchParams.append('symbol', params.symbol)
    
    return apiRequest<PaginatedResponse<BacktestResult>>(`/backtest?${searchParams}`)
  },

  // 获取单个回测详情
  getBacktest: async (id: number): Promise<BacktestResult> => {
    return apiRequest<BacktestResult>(`/backtest/${id}`)
  },

  // 创建回测
  createBacktest: async (data: {
    name: string
    description?: string
    symbol: string
    start_date: string
    end_date: string
    initial_balance: number
    source_ids: number[]
    signal_filters?: any
  }): Promise<BacktestResult> => {
    return apiRequest<BacktestResult>('/backtest', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // 删除回测
  deleteBacktest: async (id: number): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/backtest/${id}`, {
      method: 'DELETE'
    })
  },

  // 重新开始回测
  restartBacktest: async (id: number): Promise<APIResponse> => {
    return apiRequest<APIResponse>(`/backtest/${id}/restart`, {
      method: 'POST'
    })
  },

  // 获取回测详细结果
  getBacktestResults: async (id: number) => {
    return apiRequest(`/backtest/${id}/results`)
  },

  // 获取回测统计概览
  getBacktestStatistics: async (days: number = 30) => {
    return apiRequest(`/backtest/statistics/overview?days=${days}`)
  },

  // 获取可用的交易对列表
  getAvailableSymbols: async (): Promise<{
    success: boolean
    symbols: string[]
    error?: string
  }> => {
    return apiRequest<{
      success: boolean
      symbols: string[]
      error?: string
    }>('/backtest/symbols/')
  },

  // 获取回测进度
  getBacktestProgress: async (id: number): Promise<{
    backtest_id: number
    status: string
    started_at?: string
    completed_at?: string
    error_message?: string
    progress_percentage?: number
    elapsed_seconds?: number
    estimated_remaining_seconds?: number
  }> => {
    return apiRequest<{
      backtest_id: number
      status: string
      started_at?: string
      completed_at?: string
      error_message?: string
      progress_percentage?: number
      elapsed_seconds?: number
      estimated_remaining_seconds?: number
    }>(`/backtest/${id}/progress`)
  }
}

// 配置相关API
export const configAPI = {
  // 获取配置分类
  getConfigCategories: async (includeSensitive: boolean = false) => {
    return apiRequest(`/config/categories?include_sensitive=${includeSensitive}`)
  },

  // 验证配置
  validateConfig: async (configs: Record<string, string>) => {
    return apiRequest('/config/validate', {
      method: 'POST',
      body: JSON.stringify(configs)
    })
  },

  // 批量更新配置
  bulkUpdateConfig: async (configs: Record<string, string>, changeReason?: string) => {
    return apiRequest('/config/bulk-update', {
      method: 'POST',
      body: JSON.stringify({
        configs,
        change_reason: changeReason || '通过前端界面更新'
      })
    })
  }
}

// 仪表板相关API
export const dashboardAPI = {
  // 获取仪表板数据
  getDashboardData: async () => {
    return apiRequest('/dashboard/')
  }
}

// 信号测试相关API
export const testAPI = {
  // 单个信号测试
  testSingleSignal: async (signalText: string, parserType?: string) => {
    return apiRequest('/signal-test/parse', {
      method: 'POST',
      body: JSON.stringify({ 
        message: signalText,
        parser_type: parserType 
      })
    })
  },

  // 批量信号测试
  testBatchSignals: async (signalTexts: string[], parserType?: string) => {
    const requests = signalTexts.map(text => ({ 
      message: text,
      parser_type: parserType 
    }))
    return apiRequest('/signal-test/batch', {
      method: 'POST',
      body: JSON.stringify({ signals: requests })
    })
  }
}

// 导出默认API对象
const api = {
  signals: signalAPI,
  sources: sourceAPI,
  backtest: backtestAPI,
  config: configAPI,
  dashboard: dashboardAPI,
  test: testAPI
}

export default api 