// 回测相关类型定义
export interface BacktestResult {
  id: number
  name: string
  description?: string
  symbol: string
  start_date: string
  end_date: string
  initial_balance: number
  source_ids: string
  final_balance?: number
  total_return?: number
  total_trades?: number
  winning_trades?: number
  losing_trades?: number
  win_rate?: number
  profit_factor?: number
  max_drawdown?: number
  sharpe_ratio?: number
  status: 'pending' | 'running' | 'completed' | 'failed'
  started_at?: string
  completed_at?: string
  created_at: string
  result_json?: string
  error_message?: string
}

export interface BacktestProgress {
  backtest_id: number
  status: string
  started_at?: string
  completed_at?: string
  error_message?: string
  progress_percentage?: number
  elapsed_seconds?: number
  estimated_remaining_seconds?: number
}

export interface BacktestDetailedResults {
  backtest_info: BacktestResult
  summary: {
    initial_balance: number
    final_balance: number
    total_return: number
    total_trades: number
    winning_trades: number
    losing_trades: number
    win_rate: number
    profit_factor: number
    max_drawdown: number
    sharpe_ratio: number
  }
  detailed_results: {
    trades: Array<{
      signal_id: number
      type: string
      timestamp: string
      price: number
      quantity: number
      pnl?: number
      pnl_percentage?: number
    }>
    equity_curve: Array<{
      timestamp: string
      equity: number
      balance: number
      position_value: number
    }>
    daily_returns: Array<{
      date: string
      equity: number
      return: number
    }>
    monthly_returns: Array<{
      month: string
      equity: number
      return: number
    }>
    trade_analysis: {
      total_trades: number
      winning_trades: number
      losing_trades: number
      largest_win: number
      largest_loss: number
      average_win: number
      average_loss: number
      win_rate: number
      avg_trade_pnl: number
      std_trade_pnl: number
    }
  }
}

export interface CreateBacktestRequest {
  name: string
  description?: string
  symbol?: string
  start_date: string
  end_date: string
  initial_balance: number
  source_ids: number[]
  signal_filters?: {
    signal_types?: string[]
    min_confidence?: number
    weekdays_only?: boolean
    min_source_signals?: number
  }
}

export interface SignalSource {
  id: number
  name: string
  telegram_chat_id: string
  description?: string
  is_active: boolean
  reliability_score: number
  total_signals: number
  successful_signals: number
  created_at: string
  updated_at?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

export interface APIResponse<T = any> {
  success: boolean
  message: string
  data?: T
}