// 前端配置文件
// 动态获取API URL，支持运行时环境变量
export function getApiBaseUrl() {
  if (typeof window !== 'undefined') {
    // 客户端：从window对象获取注入的环境变量
    return (window as any).__RUNTIME_CONFIG__?.API_BASE_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  }
  // 服务端：使用环境变量
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
}

// 为了兼容性，导出API_BASE_URL常量
export const API_BASE_URL = getApiBaseUrl()