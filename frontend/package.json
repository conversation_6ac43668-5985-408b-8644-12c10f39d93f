{"name": "husky", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "typescript": "^5.2.2", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-query": "^3.39.3", "axios": "^1.6.1", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0"}, "devDependencies": {"eslint": "^8.53.0", "eslint-config-next": "14.0.3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}}