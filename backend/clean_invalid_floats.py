#!/usr/bin/env python3
"""
清理数据库中的无效浮点数值（无穷大和NaN）
"""

import json
import math
from app.database import SessionLocal
from app.models import BacktestResult

def clean_float_value(value):
    """清理浮点数值，将无穷大和NaN转换为None"""
    if value is None:
        return None
    if math.isinf(value) or math.isnan(value):
        return None
    return value

def clean_json_data(data):
    """递归清理JSON数据中的无效浮点数"""
    if isinstance(data, dict):
        return {k: clean_json_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [clean_json_data(item) for item in data]
    elif isinstance(data, float):
        return clean_float_value(data)
    else:
        return data

def main():
    db = SessionLocal()
    try:
        # 获取所有回测记录
        backtests = db.query(BacktestResult).all()
        
        updated_count = 0
        for backtest in backtests:
            updated = False
            
            # 清理数值字段
            if backtest.final_balance is not None:
                cleaned_value = clean_float_value(backtest.final_balance)
                if cleaned_value != backtest.final_balance:
                    backtest.final_balance = cleaned_value
                    updated = True
            
            if backtest.total_return is not None:
                cleaned_value = clean_float_value(backtest.total_return)
                if cleaned_value != backtest.total_return:
                    backtest.total_return = cleaned_value
                    updated = True
            
            if backtest.win_rate is not None:
                cleaned_value = clean_float_value(backtest.win_rate)
                if cleaned_value != backtest.win_rate:
                    backtest.win_rate = cleaned_value
                    updated = True
            
            if backtest.profit_factor is not None:
                cleaned_value = clean_float_value(backtest.profit_factor)
                if cleaned_value != backtest.profit_factor:
                    backtest.profit_factor = cleaned_value
                    updated = True
            
            if backtest.max_drawdown is not None:
                cleaned_value = clean_float_value(backtest.max_drawdown)
                if cleaned_value != backtest.max_drawdown:
                    backtest.max_drawdown = cleaned_value
                    updated = True
            
            if backtest.sharpe_ratio is not None:
                cleaned_value = clean_float_value(backtest.sharpe_ratio)
                if cleaned_value != backtest.sharpe_ratio:
                    backtest.sharpe_ratio = cleaned_value
                    updated = True
            
            # 清理JSON字段
            if backtest.result_json:
                try:
                    result_data = json.loads(backtest.result_json)
                    cleaned_data = clean_json_data(result_data)
                    new_json = json.dumps(cleaned_data, ensure_ascii=False)
                    
                    if new_json != backtest.result_json:
                        backtest.result_json = new_json
                        updated = True
                        
                except json.JSONDecodeError:
                    print(f"警告: 回测 {backtest.id} 的result_json格式无效")
            
            if updated:
                updated_count += 1
                print(f"已清理回测 {backtest.id}: {backtest.name}")
        
        # 提交更改
        db.commit()
        print(f"清理完成，共更新了 {updated_count} 个回测记录")
        
    except Exception as e:
        print(f"清理过程中出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()