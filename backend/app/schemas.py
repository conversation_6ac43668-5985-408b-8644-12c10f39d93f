from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models import SignalStatus, SignalType, BacktestStatus


# 基础模式
class BaseSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)


# 信号源相关模式
class SignalSourceCreate(BaseModel):
    name: Optional[str] = None
    telegram_chat_id: str = Field(..., max_length=50)
    description: Optional[str] = None
    is_active: bool = True


class SignalSourceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    reliability_score: Optional[float] = None


class SignalSourceResponse(BaseSchema):
    id: int
    name: Optional[str] = None
    telegram_chat_id: str
    description: Optional[str]
    is_active: bool
    reliability_score: float
    total_signals: int
    successful_signals: int
    created_at: datetime
    updated_at: Optional[datetime]


# Telegram信号相关模式
class TelegramSignalCreate(BaseModel):
    source_id: int
    message_id: Optional[str] = None
    symbol: str = Field(..., max_length=20)
    signal_type: SignalType
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    quantity: Optional[float] = None
    leverage: int = 1
    raw_message: Optional[str] = None
    signal_time: datetime


class TelegramSignalUpdate(BaseModel):
    status: Optional[SignalStatus] = None
    processed_at: Optional[datetime] = None


class TelegramSignalResponse(BaseSchema):
    id: int
    source_id: int
    message_id: Optional[str]
    symbol: str
    signal_type: SignalType
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    quantity: Optional[float]
    leverage: int
    raw_message: Optional[str]
    status: SignalStatus
    signal_time: datetime
    received_at: datetime
    processed_at: Optional[datetime]
    source: Optional[SignalSourceResponse] = None


# 交易记录相关模式
class SignalTradeCreate(BaseModel):
    signal_id: int
    trade_id: Optional[str] = None
    order_id: Optional[str] = None
    symbol: str
    side: str
    entry_price: Optional[float] = None
    quantity: Optional[float] = None


class SignalTradeUpdate(BaseModel):
    exit_price: Optional[float] = None
    pnl: Optional[float] = None
    pnl_percentage: Optional[float] = None
    fee: Optional[float] = None
    status: Optional[str] = None
    exit_time: Optional[datetime] = None


class SignalTradeResponse(BaseSchema):
    id: int
    signal_id: int
    trade_id: Optional[str]
    order_id: Optional[str]
    symbol: str
    side: str
    entry_price: Optional[float]
    exit_price: Optional[float]
    quantity: Optional[float]
    pnl: Optional[float]
    pnl_percentage: Optional[float]
    fee: Optional[float]
    status: str
    entry_time: Optional[datetime]
    exit_time: Optional[datetime]
    created_at: datetime


# 回测相关模式
class BacktestRequest(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = None
    symbol: Optional[str] = Field(None, max_length=20)
    start_date: datetime
    end_date: datetime
    initial_balance: float = 1000.0
    source_ids: List[int]
    signal_filters: Optional[Dict[str, Any]] = None


class BacktestResponse(BaseSchema):
    id: int
    name: str
    description: Optional[str]
    symbol: str
    start_date: datetime
    end_date: datetime
    initial_balance: float
    source_ids: str
    final_balance: Optional[float]
    total_return: Optional[float]
    total_trades: Optional[int]
    winning_trades: Optional[int]
    losing_trades: Optional[int]
    win_rate: Optional[float]
    profit_factor: Optional[float]
    max_drawdown: Optional[float]
    sharpe_ratio: Optional[float]
    status: BacktestStatus
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    result_json: Optional[str]
    error_message: Optional[str]


# 信号过滤和查询相关模式
class SignalFilter(BaseModel):
    source_ids: Optional[List[int]] = None
    symbols: Optional[List[str]] = None
    signal_types: Optional[List[SignalType]] = None
    status: Optional[List[SignalStatus]] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    min_confidence: Optional[float] = None


class PaginationParams(BaseModel):
    page: int = Field(1, ge=1)
    size: int = Field(10, ge=1, le=100)


class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int


# 统计相关模式
class SignalStatistics(BaseModel):
    total_signals: int
    pending_signals: int
    executed_signals: int
    failed_signals: int
    success_rate: float
    average_confidence: float


class SourcePerformance(BaseModel):
    source_id: int
    source_name: str
    total_signals: int
    successful_signals: int
    success_rate: float
    average_pnl: float
    total_pnl: float


class DashboardData(BaseModel):
    signal_statistics: SignalStatistics
    recent_signals: List[TelegramSignalResponse]
    source_performance: List[SourcePerformance]
    active_trades: int
    total_pnl: float


# WebSocket相关模式
class WebSocketMessage(BaseModel):
    type: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# API响应模式
class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None


class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    detail: Optional[str] = None 


# 配置管理相关模式
class SystemConfigCreate(BaseModel):
    key: str = Field(..., max_length=100)
    value: Optional[str] = None
    description: Optional[str] = None
    category: str
    config_type: str = "string"
    is_required: bool = False
    is_sensitive: bool = False
    validation_rules: Optional[Dict[str, Any]] = None
    sort_order: int = 0


class SystemConfigUpdate(BaseModel):
    value: Optional[str] = None
    description: Optional[str] = None
    is_required: Optional[bool] = None
    validation_rules: Optional[Dict[str, Any]] = None
    sort_order: Optional[int] = None


class SystemConfigResponse(BaseSchema):
    id: int
    key: str
    value: Optional[str]
    default_value: Optional[str]
    description: Optional[str]
    category: str
    config_type: str
    is_required: bool
    is_sensitive: bool
    is_readonly: bool
    validation_rules: Optional[str]
    sort_order: int
    created_at: datetime
    updated_at: Optional[datetime]


class SystemConfigPublic(BaseModel):
    """公开的配置信息（不包含敏感数据）"""
    id: int
    key: str
    value: Optional[str] = None  # 敏感信息将被隐藏
    description: Optional[str]
    category: str
    config_type: str
    is_required: bool
    is_sensitive: bool
    validation_rules: Optional[str]
    sort_order: int


class ConfigCategoryResponse(BaseModel):
    category: str
    configs: List[SystemConfigPublic]


class ConfigHistoryResponse(BaseSchema):
    id: int
    config_key: str
    old_value: Optional[str]
    new_value: Optional[str]
    changed_by: Optional[str]
    change_reason: Optional[str]
    timestamp: datetime


class ConfigUpdateRequest(BaseModel):
    configs: Dict[str, Any]  # key-value pairs of config updates
    change_reason: Optional[str] = None


class ConfigValidationError(BaseModel):
    key: str
    error: str
    current_value: Optional[str]


class ConfigValidationResult(BaseModel):
    is_valid: bool
    errors: List[ConfigValidationError]


# 高级交易配置相关模式
class EntryPoint(BaseModel):
    """入场点配置"""
    price: float = Field(..., gt=0, description="入场价格")
    allocation: float = Field(..., ge=0, le=1, description="资金分配比例 (0-1)")
    description: Optional[str] = None


class TakeProfitPoint(BaseModel):
    """止盈点配置"""
    price: float = Field(..., gt=0, description="止盈价格")
    allocation: float = Field(..., ge=0, le=1, description="平仓比例 (0-1)")
    description: Optional[str] = None


class AdvancedTradeConfig(BaseModel):
    """高级交易配置"""
    # 基本信息
    symbol: str = Field(..., max_length=20)
    signal_type: SignalType
    
    # 多入场配置
    entry_points: List[EntryPoint] = Field(..., min_items=1, description="入场点列表")
    auto_distribute_funds: bool = Field(True, description="是否自动平均分配资金")
    
    # 多止盈配置  
    take_profit_points: List[TakeProfitPoint] = Field(..., min_items=1, description="止盈点列表")
    
    # 风险管理
    stop_loss: Optional[float] = Field(None, gt=0, description="止损价格")
    total_quantity: Optional[float] = Field(None, gt=0, description="总交易数量")
    leverage: int = Field(1, ge=1, le=125, description="杠杆倍数")
    
    # 其他参数
    raw_message: Optional[str] = None
    description: Optional[str] = None


class AdvancedTradeResponse(BaseSchema):
    """高级交易响应"""
    id: int
    config: AdvancedTradeConfig
    created_signals: List[TelegramSignalResponse] = []  # 创建的多个信号
    created_at: datetime
    updated_at: Optional[datetime]


# 用于创建高级交易的请求模式
class CreateAdvancedTradeRequest(BaseModel):
    config: AdvancedTradeConfig


# 新的入场点和止盈点数据结构
class TradeEntryPointCreate(BaseModel):
    """创建入场点"""
    price: float = Field(..., gt=0, description="入场价格")
    allocation: float = Field(..., ge=0, le=1, description="资金分配比例")
    description: Optional[str] = None


class TradeEntryPointUpdate(BaseModel):
    """更新入场点"""
    price: Optional[float] = Field(None, gt=0, description="入场价格")
    allocation: Optional[float] = Field(None, ge=0, le=1, description="资金分配比例")
    description: Optional[str] = None
    status: Optional[str] = None


class TradeEntryPointResponse(BaseSchema):
    """入场点响应"""
    id: int
    signal_id: int
    price: float
    allocation: float
    description: Optional[str]
    status: str
    filled_quantity: float
    actual_price: Optional[float]
    order_id: Optional[str]
    created_at: datetime
    filled_at: Optional[datetime]


class TradeTakeProfitPointCreate(BaseModel):
    """创建止盈点"""
    price: float = Field(..., gt=0, description="止盈价格")
    allocation: float = Field(..., ge=0, le=1, description="平仓比例")
    description: Optional[str] = None


class TradeTakeProfitPointUpdate(BaseModel):
    """更新止盈点"""
    price: Optional[float] = Field(None, gt=0, description="止盈价格")
    allocation: Optional[float] = Field(None, ge=0, le=1, description="平仓比例")
    description: Optional[str] = None
    status: Optional[str] = None


class TradeTakeProfitPointResponse(BaseSchema):
    """止盈点响应"""
    id: int
    signal_id: int
    price: float
    allocation: float
    description: Optional[str]
    status: str
    filled_quantity: float
    actual_price: Optional[float]
    order_id: Optional[str]
    pnl: Optional[float]
    pnl_percentage: Optional[float]
    created_at: datetime
    filled_at: Optional[datetime]


class TradeDetailResponse(BaseSchema):
    """交易详情响应"""
    signal: TelegramSignalResponse
    entry_points: List[TradeEntryPointResponse]
    take_profit_points: List[TradeTakeProfitPointResponse]
    trades: List[SignalTradeResponse]
    
    # 统计信息
    total_entry_filled: float = 0.0
    total_tp_filled: float = 0.0
    total_pnl: float = 0.0
    total_pnl_percentage: float = 0.0
    overall_status: str = "pending"


class CreateTradeRequest(BaseModel):
    """创建交易请求"""
    symbol: str = Field(..., max_length=20)
    signal_type: SignalType
    entry_points: List[TradeEntryPointCreate] = Field(..., min_items=1)
    take_profit_points: List[TradeTakeProfitPointCreate] = Field(..., min_items=1)
    stop_loss: Optional[float] = None
    leverage: int = Field(1, ge=1, le=125)
    raw_message: Optional[str] = None
    description: Optional[str] = None 