import asyncio
import hashlib
import hmac
import time
from typing import Dict, Optional, List, Any
import aiohttp
import logging
from decimal import Decimal

logger = logging.getLogger(__name__)

class BinanceClient:
    """Binance交易所API客户端"""
    
    def __init__(self, api_key: str, api_secret: str, testnet: bool = True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        
        # API端点
        if testnet:
            self.base_url = "https://testnet.binance.vision"
        else:
            self.base_url = "https://api.binance.com"
            
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    def _generate_signature(self, params: str) -> str:
        """生成API签名"""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            params.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        }
        
    async def _request(self, method: str, endpoint: str, params: Dict = None, signed: bool = False) -> Dict:
        """发送API请求"""
        if not self.session:
            self.session = aiohttp.ClientSession()
            
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()
        
        if params is None:
            params = {}
            
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            params['signature'] = self._generate_signature(query_string)
            
        try:
            async with self.session.request(method, url, params=params, headers=headers) as response:
                data = await response.json()
                
                if response.status != 200:
                    logger.error(f"Binance API错误: {data}")
                    raise Exception(f"Binance API错误: {data.get('msg', 'Unknown error')}")
                    
                return data
                
        except Exception as e:
            logger.error(f"请求Binance API失败: {e}")
            raise
            
    # ========== 市场数据接口 ==========
    
    async def get_symbol_info(self, symbol: str) -> Dict:
        """获取交易对信息"""
        data = await self._request('GET', '/api/v3/exchangeInfo')
        
        for symbol_info in data['symbols']:
            if symbol_info['symbol'] == symbol.upper():
                return symbol_info
                
        raise Exception(f"交易对 {symbol} 不存在")
        
    async def get_ticker_price(self, symbol: str) -> Decimal:
        """获取当前价格"""
        data = await self._request('GET', '/api/v3/ticker/price', {'symbol': symbol.upper()})
        return Decimal(data['price'])
        
    async def get_klines(self, symbol: str, interval: str = '5m', limit: int = 500, 
                        start_time: Optional[int] = None, end_time: Optional[int] = None) -> List[Dict]:
        """获取K线数据"""
        params = {
            'symbol': symbol.upper(),
            'interval': interval,
            'limit': limit
        }
        
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
            
        klines_data = await self._request('GET', '/api/v3/klines', params)
        
        # 格式化K线数据
        klines = []
        for kline in klines_data:
            klines.append({
                'open_time': kline[0],
                'open': Decimal(kline[1]),
                'high': Decimal(kline[2]),
                'low': Decimal(kline[3]),
                'close': Decimal(kline[4]),
                'volume': Decimal(kline[5]),
                'close_time': kline[6],
                'quote_volume': Decimal(kline[7]),
                'trades_count': kline[8],
                'taker_buy_base_volume': Decimal(kline[9]),
                'taker_buy_quote_volume': Decimal(kline[10])
            })
            
        return klines
        
    # ========== 账户信息接口 ==========
    
    async def get_account_info(self) -> Dict:
        """获取账户信息"""
        return await self._request('GET', '/api/v3/account', signed=True)
        
    async def get_balance(self, asset: str = None) -> Dict:
        """获取资产余额"""
        account_info = await self.get_account_info()
        
        if asset:
            for balance in account_info['balances']:
                if balance['asset'] == asset.upper():
                    return {
                        'asset': balance['asset'],
                        'free': Decimal(balance['free']),
                        'locked': Decimal(balance['locked'])
                    }
            return None
        else:
            balances = {}
            for balance in account_info['balances']:
                free_amount = Decimal(balance['free'])
                locked_amount = Decimal(balance['locked'])
                if free_amount > 0 or locked_amount > 0:
                    balances[balance['asset']] = {
                        'free': free_amount,
                        'locked': locked_amount
                    }
            return balances
            
    # ========== 交易接口 ==========
    
    async def create_order(self, symbol: str, side: str, order_type: str = 'MARKET', 
                          quantity: Optional[Decimal] = None, quote_quantity: Optional[Decimal] = None,
                          price: Optional[Decimal] = None, time_in_force: str = 'GTC') -> Dict:
        """创建订单"""
        params = {
            'symbol': symbol.upper(),
            'side': side.upper(),
            'type': order_type.upper()
        }
        
        if quantity:
            params['quantity'] = str(quantity)
        elif quote_quantity:
            params['quoteOrderQty'] = str(quote_quantity)
        else:
            raise Exception("必须指定quantity或quoteOrderQty")
            
        if order_type.upper() in ['LIMIT', 'STOP_LOSS_LIMIT', 'TAKE_PROFIT_LIMIT']:
            if not price:
                raise Exception(f"{order_type}订单必须指定价格")
            params['price'] = str(price)
            params['timeInForce'] = time_in_force
            
        return await self._request('POST', '/api/v3/order', params, signed=True)
        
    async def cancel_order(self, symbol: str, order_id: int) -> Dict:
        """取消订单"""
        params = {
            'symbol': symbol.upper(),
            'orderId': order_id
        }
        return await self._request('DELETE', '/api/v3/order', params, signed=True)
        
    async def get_order(self, symbol: str, order_id: int) -> Dict:
        """查询订单"""
        params = {
            'symbol': symbol.upper(),
            'orderId': order_id
        }
        return await self._request('GET', '/api/v3/order', params, signed=True)
        
    async def get_open_orders(self, symbol: str = None) -> List[Dict]:
        """获取未成交订单"""
        params = {}
        if symbol:
            params['symbol'] = symbol.upper()
            
        return await self._request('GET', '/api/v3/openOrders', params, signed=True)
        
    async def get_trades(self, symbol: str, limit: int = 500) -> List[Dict]:
        """获取成交历史"""
        params = {
            'symbol': symbol.upper(),
            'limit': limit
        }
        return await self._request('GET', '/api/v3/myTrades', params, signed=True)
        
    # ========== 便捷交易方法 ==========
    
    async def market_buy(self, symbol: str, quote_quantity: Decimal) -> Dict:
        """市价买入（按金额）"""
        logger.info(f"市价买入 {symbol}: ${quote_quantity}")
        return await self.create_order(
            symbol=symbol,
            side='BUY',
            order_type='MARKET',
            quote_quantity=quote_quantity
        )
        
    async def market_sell(self, symbol: str, quantity: Decimal) -> Dict:
        """市价卖出（按数量）"""
        logger.info(f"市价卖出 {symbol}: {quantity}")
        return await self.create_order(
            symbol=symbol,
            side='SELL',
            order_type='MARKET',
            quantity=quantity
        )
        
    async def limit_buy(self, symbol: str, quantity: Decimal, price: Decimal) -> Dict:
        """限价买入"""
        logger.info(f"限价买入 {symbol}: {quantity} @ ${price}")
        return await self.create_order(
            symbol=symbol,
            side='BUY',
            order_type='LIMIT',
            quantity=quantity,
            price=price
        )
        
    async def limit_sell(self, symbol: str, quantity: Decimal, price: Decimal) -> Dict:
        """限价卖出"""
        logger.info(f"限价卖出 {symbol}: {quantity} @ ${price}")
        return await self.create_order(
            symbol=symbol,
            side='SELL',
            order_type='LIMIT',
            quantity=quantity,
            price=price
        )

# 使用示例
async def example_usage():
    async with BinanceClient(api_key="your_key", api_secret="your_secret", testnet=True) as client:
        # 获取账户信息
        account = await client.get_account_info()
        print(f"账户信息: {account}")
        
        # 获取BTCUSDT价格
        price = await client.get_ticker_price("BTCUSDT")
        print(f"BTCUSDT价格: ${price}")
        
        # 市价买入10 USDT的BTC
        # order = await client.market_buy("BTCUSDT", Decimal("10"))
        # print(f"订单结果: {order}") 