"""
VectorBT可视化模块
生成回测结果的图表和报告
"""

import vectorbt as vbt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
import logging
import base64
import io
from datetime import datetime

logger = logging.getLogger(__name__)


class VectorbtVisualizer:
    """VectorBT可视化工具"""
    
    def __init__(self):
        # 设置plotly默认主题
        self.default_theme = 'plotly_dark'
        self.colors = {
            'profit': '#00ff88',
            'loss': '#ff4444', 
            'neutral': '#888888',
            'primary': '#1f77b4',
            'secondary': '#ff7f0e'
        }
    
    def generate_web_charts(
        self,
        portfolio: vbt.Portfolio,
        backtest_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成适合web页面显示的图表数据"""
        try:
            charts = {}

            # 1. 权益曲线图
            equity_fig = self.create_equity_curve(portfolio, backtest_config.get('title', '回测结果'))
            charts['equity_curve'] = equity_fig.to_html(include_plotlyjs=False, div_id="equity-curve")

            # 2. 回撤分析图
            drawdown_fig = self.create_drawdown_chart(portfolio)
            charts['drawdown'] = drawdown_fig.to_html(include_plotlyjs=False, div_id="drawdown-chart")

            # 3. 收益分布图
            returns_fig = self.create_returns_distribution(portfolio)
            charts['returns_distribution'] = returns_fig.to_html(include_plotlyjs=False, div_id="returns-dist")

            # 4. 交易分析图
            if portfolio.trades.count().sum() > 0:
                trades_fig = self.create_trades_analysis(portfolio)
                charts['trades_analysis'] = trades_fig.to_html(include_plotlyjs=False, div_id="trades-analysis")

            return {
                'success': True,
                'charts': charts,
                'plotly_js_required': True  # 前端需要加载plotly.js
            }

        except Exception as e:
            logger.error(f"生成web图表失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'charts': {}
            }

    def create_equity_curve(self, portfolio: vbt.Portfolio, title: str = "权益曲线") -> go.Figure:
        """创建权益曲线图"""
        try:
            # 获取权益数据
            equity = portfolio.value()

            fig = go.Figure()

            if hasattr(equity, 'columns') and len(equity.columns) > 1:
                # 多资产组合
                for col in equity.columns:
                    fig.add_trace(go.Scatter(
                        x=equity.index,
                        y=equity[col],
                        mode='lines',
                        name=f'{col} 权益',
                        line=dict(width=2)
                    ))

                # 添加总权益
                total_equity = equity.sum(axis=1)
                fig.add_trace(go.Scatter(
                    x=equity.index,
                    y=total_equity,
                    mode='lines',
                    name='总权益',
                    line=dict(width=3, color=self.colors['primary'])
                ))
            else:
                # 单资产
                fig.add_trace(go.Scatter(
                    x=equity.index,
                    y=equity if hasattr(equity, 'index') else equity.iloc[:, 0],
                    mode='lines',
                    name='权益',
                    line=dict(width=2, color=self.colors['primary'])
                ))

            fig.update_layout(
                title=title,
                xaxis_title="时间",
                yaxis_title="权益 ($)",
                template=self.default_theme,
                hovermode='x unified'
            )

            return fig

        except Exception as e:
            logger.error(f"创建权益曲线图失败: {e}")
            # 返回空图表
            fig = go.Figure()
            fig.add_annotation(text=f"图表生成失败: {str(e)}", x=0.5, y=0.5)
            return fig

    def create_drawdown_chart(self, portfolio: vbt.Portfolio) -> go.Figure:
        """创建回撤分析图"""
        try:
            # 获取回撤数据
            drawdown = portfolio.drawdown()

            fig = go.Figure()

            if hasattr(drawdown, 'columns') and len(drawdown.columns) > 1:
                # 多资产
                for col in drawdown.columns:
                    fig.add_trace(go.Scatter(
                        x=drawdown.index,
                        y=drawdown[col] * 100,  # 转换为百分比
                        mode='lines',
                        name=f'{col} 回撤',
                        fill='tonexty' if col != drawdown.columns[0] else 'tozeroy',
                        line=dict(color=self.colors['loss'])
                    ))
            else:
                # 单资产
                dd_data = drawdown if hasattr(drawdown, 'index') else drawdown.iloc[:, 0]
                fig.add_trace(go.Scatter(
                    x=dd_data.index,
                    y=dd_data * 100,
                    mode='lines',
                    name='回撤',
                    fill='tozeroy',
                    line=dict(color=self.colors['loss'])
                ))

            fig.update_layout(
                title="回撤分析",
                xaxis_title="时间",
                yaxis_title="回撤 (%)",
                template=self.default_theme,
                hovermode='x unified'
            )

            return fig

        except Exception as e:
            logger.error(f"创建回撤图失败: {e}")
            fig = go.Figure()
            fig.add_annotation(text=f"图表生成失败: {str(e)}", x=0.5, y=0.5)
            return fig

    def create_returns_distribution(self, portfolio: vbt.Portfolio) -> go.Figure:
        """创建收益分布图"""
        try:
            # 获取收益数据
            returns = portfolio.returns()

            fig = go.Figure()

            if hasattr(returns, 'columns') and len(returns.columns) > 1:
                # 多资产 - 显示总收益分布
                total_returns = returns.sum(axis=1)
                fig.add_trace(go.Histogram(
                    x=total_returns * 100,  # 转换为百分比
                    nbinsx=50,
                    name='总收益分布',
                    marker_color=self.colors['primary'],
                    opacity=0.7
                ))
            else:
                # 单资产
                returns_data = returns if hasattr(returns, 'index') else returns.iloc[:, 0]
                fig.add_trace(go.Histogram(
                    x=returns_data * 100,
                    nbinsx=50,
                    name='收益分布',
                    marker_color=self.colors['primary'],
                    opacity=0.7
                ))

            fig.update_layout(
                title="收益分布",
                xaxis_title="收益率 (%)",
                yaxis_title="频次",
                template=self.default_theme,
                showlegend=False
            )

            return fig

        except Exception as e:
            logger.error(f"创建收益分布图失败: {e}")
            fig = go.Figure()
            fig.add_annotation(text=f"图表生成失败: {str(e)}", x=0.5, y=0.5)
            return fig

    def create_trades_analysis(self, portfolio: vbt.Portfolio) -> go.Figure:
        """创建交易分析图"""
        try:
            trades = portfolio.trades

            # 获取交易PnL数据
            if hasattr(trades, 'pnl'):
                pnl_data = trades.pnl

                if hasattr(pnl_data, 'columns') and len(pnl_data.columns) > 1:
                    # 多资产 - 合并所有交易
                    all_pnl = []
                    for col in pnl_data.columns:
                        col_pnl = pnl_data[col].dropna()
                        all_pnl.extend(col_pnl.tolist())
                    pnl_values = all_pnl
                else:
                    # 单资产
                    pnl_values = pnl_data.dropna().tolist() if hasattr(pnl_data, 'dropna') else [pnl_data]

                if len(pnl_values) > 0:
                    # 创建盈亏分布图
                    colors = [self.colors['profit'] if x > 0 else self.colors['loss'] for x in pnl_values]

                    fig = go.Figure()
                    fig.add_trace(go.Bar(
                        x=list(range(1, len(pnl_values) + 1)),
                        y=pnl_values,
                        marker_color=colors,
                        name='交易盈亏',
                        text=[f'${x:.2f}' for x in pnl_values],
                        textposition='outside'
                    ))

                    fig.update_layout(
                        title="交易盈亏分析",
                        xaxis_title="交易序号",
                        yaxis_title="盈亏 ($)",
                        template=self.default_theme,
                        showlegend=False
                    )

                    return fig

            # 如果没有交易数据，返回空图表
            fig = go.Figure()
            fig.add_annotation(
                text="暂无交易数据",
                x=0.5, y=0.5,
                xref="paper", yref="paper",
                showarrow=False,
                font=dict(size=16)
            )
            return fig

        except Exception as e:
            logger.error(f"创建交易分析图失败: {e}")
            fig = go.Figure()
            fig.add_annotation(text=f"图表生成失败: {str(e)}", x=0.5, y=0.5)
            return fig

    def generate_comprehensive_report(
        self,
        portfolio: vbt.Portfolio,
        backtest_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成综合回测报告"""
        
        logger.info("开始生成综合回测报告...")
        
        try:
            report = {
                'summary': self._generate_summary_stats(portfolio),
                'charts': self._generate_all_charts(portfolio),
                'tables': self._generate_tables(portfolio),
                'analysis': self._generate_analysis(portfolio),
                'config': backtest_config,
                'generated_at': datetime.utcnow().isoformat()
            }
            
            logger.info("综合回测报告生成完成")
            return report
            
        except Exception as e:
            logger.error(f"生成综合报告失败: {e}")
            return {'error': str(e)}
    
    def _generate_summary_stats(self, portfolio: vbt.Portfolio) -> Dict[str, Any]:
        """生成摘要统计"""
        try:
            stats = portfolio.stats()
            
            # 基础指标
            summary = {
                'performance': {
                    'total_return': float(portfolio.total_return()),
                    'annual_return': float(portfolio.annualized_return()),
                    'max_drawdown': float(portfolio.max_drawdown()),
                    'sharpe_ratio': float(portfolio.sharpe_ratio()),
                    'calmar_ratio': float(portfolio.calmar_ratio()),
                    'sortino_ratio': float(portfolio.sortino_ratio()),
                    'omega_ratio': float(portfolio.omega_ratio())
                },
                'risk': {
                    'volatility': float(portfolio.returns().std() * np.sqrt(252)),
                    'var_95': float(portfolio.returns().quantile(0.05)),
                    'cvar_95': float(portfolio.returns()[portfolio.returns() <= portfolio.returns().quantile(0.05)].mean()),
                    'skewness': float(portfolio.returns().skew()),
                    'kurtosis': float(portfolio.returns().kurtosis())
                }
            }
            
            # 交易统计
            if hasattr(portfolio, 'trades') and portfolio.trades.count() > 0:
                trades = portfolio.trades
                summary['trading'] = {
                    'total_trades': int(trades.count()),
                    'win_rate': float(trades.win_rate()),
                    'profit_factor': float(trades.profit_factor()),
                    'avg_trade': float(trades.pnl.mean()),
                    'avg_win': float(trades.winning.pnl.mean()) if trades.winning.count() > 0 else 0,
                    'avg_loss': float(trades.losing.pnl.mean()) if trades.losing.count() > 0 else 0,
                    'max_win': float(trades.pnl.max()),
                    'max_loss': float(trades.pnl.min()),
                    'avg_duration_hours': float(trades.duration.mean().total_seconds() / 3600)
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"生成摘要统计失败: {e}")
            return {}
    
    def _generate_all_charts(self, portfolio: vbt.Portfolio) -> Dict[str, str]:
        """生成所有图表"""
        charts = {}
        
        try:
            # 1. 权益曲线图
            charts['equity_curve'] = self._create_equity_curve_chart(portfolio)
            
            # 2. 回撤图
            charts['drawdown'] = self._create_drawdown_chart(portfolio)
            
            # 3. 收益分布图
            charts['returns_distribution'] = self._create_returns_distribution_chart(portfolio)
            
            # 4. 月度收益热力图
            charts['monthly_returns'] = self._create_monthly_returns_heatmap(portfolio)
            
            # 5. 交易分析图
            if hasattr(portfolio, 'trades') and portfolio.trades.count() > 0:
                charts['trade_analysis'] = self._create_trade_analysis_chart(portfolio)
                charts['trade_duration'] = self._create_trade_duration_chart(portfolio)
            
            # 6. 滚动指标图
            charts['rolling_metrics'] = self._create_rolling_metrics_chart(portfolio)
            
        except Exception as e:
            logger.error(f"生成图表失败: {e}")
            charts['error'] = str(e)
        
        return charts
    
    def _create_equity_curve_chart(self, portfolio: vbt.Portfolio) -> str:
        """创建权益曲线图"""
        try:
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=['Portfolio Value', 'Daily Returns'],
                vertical_spacing=0.1,
                row_heights=[0.7, 0.3]
            )
            
            # 权益曲线
            value = portfolio.value()
            fig.add_trace(
                go.Scatter(
                    x=value.index,
                    y=value.values,
                    name='Portfolio Value',
                    line=dict(color=self.colors['primary'], width=2)
                ),
                row=1, col=1
            )
            
            # 日收益率
            returns = portfolio.returns()
            colors = [self.colors['profit'] if r > 0 else self.colors['loss'] for r in returns]
            
            fig.add_trace(
                go.Bar(
                    x=returns.index,
                    y=returns.values,
                    name='Daily Returns',
                    marker_color=colors,
                    opacity=0.7
                ),
                row=2, col=1
            )
            
            fig.update_layout(
                title='Portfolio Performance',
                template=self.default_theme,
                height=600,
                showlegend=True
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"创建权益曲线图失败: {e}")
            return f"<p>Error creating equity curve: {e}</p>"
    
    def _create_drawdown_chart(self, portfolio: vbt.Portfolio) -> str:
        """创建回撤图"""
        try:
            drawdowns = portfolio.drawdowns
            
            fig = go.Figure()
            
            # 回撤曲线
            dd_series = drawdowns.drawdown
            fig.add_trace(
                go.Scatter(
                    x=dd_series.index,
                    y=dd_series.values,
                    fill='tonexty',
                    name='Drawdown',
                    line=dict(color=self.colors['loss']),
                    fillcolor=f"rgba(255, 68, 68, 0.3)"
                )
            )
            
            fig.update_layout(
                title='Drawdown Analysis',
                xaxis_title='Date',
                yaxis_title='Drawdown (%)',
                template=self.default_theme,
                height=400
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"创建回撤图失败: {e}")
            return f"<p>Error creating drawdown chart: {e}</p>"
    
    def _create_returns_distribution_chart(self, portfolio: vbt.Portfolio) -> str:
        """创建收益分布图"""
        try:
            returns = portfolio.returns().dropna()
            
            fig = make_subplots(
                rows=1, cols=2,
                subplot_titles=['Returns Distribution', 'Q-Q Plot'],
                specs=[[{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # 直方图
            fig.add_trace(
                go.Histogram(
                    x=returns.values,
                    nbinsx=50,
                    name='Returns',
                    marker_color=self.colors['primary'],
                    opacity=0.7
                ),
                row=1, col=1
            )
            
            # Q-Q图（简化版）
            sorted_returns = np.sort(returns.values)
            theoretical_quantiles = np.linspace(0, 1, len(sorted_returns))
            normal_quantiles = np.percentile(np.random.normal(0, returns.std(), 10000), theoretical_quantiles * 100)
            
            fig.add_trace(
                go.Scatter(
                    x=normal_quantiles,
                    y=sorted_returns,
                    mode='markers',
                    name='Q-Q Plot',
                    marker=dict(color=self.colors['secondary'], size=4)
                ),
                row=1, col=2
            )
            
            fig.update_layout(
                title='Returns Analysis',
                template=self.default_theme,
                height=400
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"创建收益分布图失败: {e}")
            return f"<p>Error creating returns distribution: {e}</p>"
    
    def _create_monthly_returns_heatmap(self, portfolio: vbt.Portfolio) -> str:
        """创建月度收益热力图"""
        try:
            returns = portfolio.returns()
            monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
            
            # 创建月度收益矩阵
            monthly_data = []
            years = monthly_returns.index.year.unique()
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            
            for year in years:
                year_data = []
                for month in range(1, 13):
                    try:
                        value = monthly_returns[
                            (monthly_returns.index.year == year) & 
                            (monthly_returns.index.month == month)
                        ].iloc[0] * 100
                        year_data.append(value)
                    except:
                        year_data.append(np.nan)
                monthly_data.append(year_data)
            
            fig = go.Figure(data=go.Heatmap(
                z=monthly_data,
                x=months,
                y=years,
                colorscale='RdYlGn',
                zmid=0,
                text=[[f"{val:.1f}%" if not np.isnan(val) else "" for val in row] for row in monthly_data],
                texttemplate="%{text}",
                textfont={"size": 10},
                hoverongaps=False
            ))
            
            fig.update_layout(
                title='Monthly Returns Heatmap',
                template=self.default_theme,
                height=400
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"创建月度收益热力图失败: {e}")
            return f"<p>Error creating monthly returns heatmap: {e}</p>"
    
    def _create_trade_analysis_chart(self, portfolio: vbt.Portfolio) -> str:
        """创建交易分析图"""
        try:
            trades = portfolio.trades
            
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=['Trade PnL', 'Win/Loss Distribution', 'Trade Duration', 'Cumulative PnL'],
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # 交易盈亏
            pnl_values = trades.pnl.values
            colors = [self.colors['profit'] if p > 0 else self.colors['loss'] for p in pnl_values]
            
            fig.add_trace(
                go.Bar(
                    x=list(range(len(pnl_values))),
                    y=pnl_values,
                    marker_color=colors,
                    name='Trade PnL',
                    opacity=0.7
                ),
                row=1, col=1
            )
            
            # 盈亏分布
            wins = pnl_values[pnl_values > 0]
            losses = pnl_values[pnl_values < 0]
            
            fig.add_trace(
                go.Histogram(
                    x=wins,
                    name='Wins',
                    marker_color=self.colors['profit'],
                    opacity=0.7,
                    nbinsx=20
                ),
                row=1, col=2
            )
            
            fig.add_trace(
                go.Histogram(
                    x=losses,
                    name='Losses',
                    marker_color=self.colors['loss'],
                    opacity=0.7,
                    nbinsx=20
                ),
                row=1, col=2
            )
            
            # 交易持续时间
            durations = [d.total_seconds() / 3600 for d in trades.duration.values]  # 转换为小时
            fig.add_trace(
                go.Histogram(
                    x=durations,
                    name='Duration (hours)',
                    marker_color=self.colors['neutral'],
                    opacity=0.7,
                    nbinsx=20
                ),
                row=2, col=1
            )
            
            # 累积盈亏
            cumulative_pnl = np.cumsum(pnl_values)
            fig.add_trace(
                go.Scatter(
                    x=list(range(len(cumulative_pnl))),
                    y=cumulative_pnl,
                    name='Cumulative PnL',
                    line=dict(color=self.colors['primary'], width=2)
                ),
                row=2, col=2
            )
            
            fig.update_layout(
                title='Trade Analysis',
                template=self.default_theme,
                height=600,
                showlegend=True
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"创建交易分析图失败: {e}")
            return f"<p>Error creating trade analysis: {e}</p>"
    
    def _create_trade_duration_chart(self, portfolio: vbt.Portfolio) -> str:
        """创建交易持续时间分析图"""
        try:
            trades = portfolio.trades
            durations = [d.total_seconds() / 3600 for d in trades.duration.values]  # 小时
            pnl_values = trades.pnl.values
            
            fig = go.Figure()
            
            # 散点图：持续时间 vs 盈亏
            colors = [self.colors['profit'] if p > 0 else self.colors['loss'] for p in pnl_values]
            
            fig.add_trace(
                go.Scatter(
                    x=durations,
                    y=pnl_values,
                    mode='markers',
                    marker=dict(
                        color=colors,
                        size=8,
                        opacity=0.7
                    ),
                    name='Trades',
                    text=[f"Trade {i+1}<br>Duration: {d:.1f}h<br>PnL: {p:.2f}" 
                          for i, (d, p) in enumerate(zip(durations, pnl_values))],
                    hovertemplate='%{text}<extra></extra>'
                )
            )
            
            fig.update_layout(
                title='Trade Duration vs PnL',
                xaxis_title='Duration (hours)',
                yaxis_title='PnL',
                template=self.default_theme,
                height=400
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"创建交易持续时间图失败: {e}")
            return f"<p>Error creating trade duration chart: {e}</p>"
    
    def _create_rolling_metrics_chart(self, portfolio: vbt.Portfolio) -> str:
        """创建滚动指标图"""
        try:
            returns = portfolio.returns()
            
            # 计算滚动指标
            window = min(30, len(returns) // 4)  # 30天或数据长度的1/4
            rolling_sharpe = returns.rolling(window).apply(
                lambda x: x.mean() / x.std() * np.sqrt(252) if x.std() > 0 else 0
            )
            rolling_vol = returns.rolling(window).std() * np.sqrt(252)
            
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=['Rolling Sharpe Ratio', 'Rolling Volatility'],
                vertical_spacing=0.1
            )
            
            # 滚动夏普比率
            fig.add_trace(
                go.Scatter(
                    x=rolling_sharpe.index,
                    y=rolling_sharpe.values,
                    name='Rolling Sharpe',
                    line=dict(color=self.colors['primary'], width=2)
                ),
                row=1, col=1
            )
            
            # 滚动波动率
            fig.add_trace(
                go.Scatter(
                    x=rolling_vol.index,
                    y=rolling_vol.values,
                    name='Rolling Volatility',
                    line=dict(color=self.colors['secondary'], width=2)
                ),
                row=2, col=1
            )
            
            fig.update_layout(
                title='Rolling Performance Metrics',
                template=self.default_theme,
                height=500
            )
            
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"创建滚动指标图失败: {e}")
            return f"<p>Error creating rolling metrics chart: {e}</p>"
    
    def _generate_tables(self, portfolio: vbt.Portfolio) -> Dict[str, Any]:
        """生成数据表格"""
        tables = {}
        
        try:
            # 交易明细表
            if hasattr(portfolio, 'trades') and portfolio.trades.count() > 0:
                trades_df = portfolio.trades.records_readable
                tables['trades'] = trades_df.to_dict('records')
            
            # 订单明细表
            if hasattr(portfolio, 'orders') and portfolio.orders.count() > 0:
                orders_df = portfolio.orders.records_readable
                tables['orders'] = orders_df.to_dict('records')
            
        except Exception as e:
            logger.error(f"生成表格失败: {e}")
            tables['error'] = str(e)
        
        return tables
    
    def _generate_analysis(self, portfolio: vbt.Portfolio) -> Dict[str, Any]:
        """生成分析结论"""
        try:
            analysis = {
                'performance_summary': self._analyze_performance(portfolio),
                'risk_assessment': self._analyze_risk(portfolio),
                'trade_quality': self._analyze_trade_quality(portfolio),
                'recommendations': self._generate_recommendations(portfolio)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"生成分析失败: {e}")
            return {'error': str(e)}
    
    def _analyze_performance(self, portfolio: vbt.Portfolio) -> str:
        """分析性能表现"""
        total_return = portfolio.total_return()
        sharpe = portfolio.sharpe_ratio()
        max_dd = portfolio.max_drawdown()
        
        if total_return > 0.2:
            performance = "优秀"
        elif total_return > 0.1:
            performance = "良好"
        elif total_return > 0:
            performance = "一般"
        else:
            performance = "较差"
        
        return f"策略总收益率为 {total_return:.2%}，表现{performance}。夏普比率为 {sharpe:.2f}，最大回撤为 {max_dd:.2%}。"
    
    def _analyze_risk(self, portfolio: vbt.Portfolio) -> str:
        """分析风险特征"""
        max_dd = portfolio.max_drawdown()
        vol = portfolio.returns().std() * np.sqrt(252)
        
        if max_dd < 0.1:
            risk_level = "低"
        elif max_dd < 0.2:
            risk_level = "中等"
        else:
            risk_level = "高"
        
        return f"策略风险水平为{risk_level}，最大回撤 {max_dd:.2%}，年化波动率 {vol:.2%}。"
    
    def _analyze_trade_quality(self, portfolio: vbt.Portfolio) -> str:
        """分析交易质量"""
        if not hasattr(portfolio, 'trades') or portfolio.trades.count() == 0:
            return "无交易数据可分析。"
        
        trades = portfolio.trades
        win_rate = trades.win_rate()
        profit_factor = trades.profit_factor()
        
        if win_rate > 0.6 and profit_factor > 1.5:
            quality = "优秀"
        elif win_rate > 0.5 and profit_factor > 1.2:
            quality = "良好"
        else:
            quality = "需要改进"
        
        return f"交易质量{quality}，胜率 {win_rate:.2%}，盈亏比 {profit_factor:.2f}。"
    
    def _generate_recommendations(self, portfolio: vbt.Portfolio) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于回撤的建议
        max_dd = portfolio.max_drawdown()
        if max_dd > 0.2:
            recommendations.append("最大回撤较大，建议优化风险管理策略")
        
        # 基于夏普比率的建议
        sharpe = portfolio.sharpe_ratio()
        if sharpe < 1.0:
            recommendations.append("夏普比率偏低，建议提高策略的风险调整收益")
        
        # 基于交易的建议
        if hasattr(portfolio, 'trades') and portfolio.trades.count() > 0:
            win_rate = portfolio.trades.win_rate()
            if win_rate < 0.4:
                recommendations.append("胜率较低，建议优化入场时机")
        
        if not recommendations:
            recommendations.append("策略表现良好，继续保持")
        
        return recommendations
