import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime

from app.models import TelegramSignal, TradeEntryPoint, TradeTakeProfitPoint, SignalTrade
from app.services.exchange_client import exchange_client
from app.config import settings

logger = logging.getLogger(__name__)


class CornixSignalExecutor:
    """Cornix风格信号执行器 - 处理多入场点和多止盈点"""
    
    async def execute_signal(self, signal: TelegramSignal, db: Session) -> bool:
        """执行交易信号"""
        try:
            logger.info(f"[CORNIX_DEBUG] 开始执行信号: {signal.id} - {signal.symbol}")
            
            if not settings.auto_trading_enabled:
                logger.info(f"[CORNIX_DEBUG] 自动交易未启用，跳过执行")
                return False
            
            # 检查是否已经执行过，避免重复执行
            existing_trades = db.query(SignalTrade).filter(
                SignalTrade.signal_id == signal.id
            ).count()
            
            logger.info(f"[CORNIX_DEBUG] 信号 {signal.id} 已有交易记录数: {existing_trades}")
            
            if existing_trades > 0:
                logger.info(f"[CORNIX_DEBUG] 信号 {signal.id} 已有交易记录，跳过执行")
                return True
            
            # 只执行一个主要入场订单，而不是为每个入场点创建订单
            logger.info(f"[CORNIX_DEBUG] 开始执行主入场订单: {signal.id}")
            success = await self._execute_main_entry(signal, db)
            
            logger.info(f"[CORNIX_DEBUG] 信号 {signal.id} 执行结果: {success}")
            return success
            
        except Exception as e:
            logger.error(f"[CORNIX_DEBUG] 执行信号失败: {e}")
            return False
    
    async def _execute_main_entry(self, signal: TelegramSignal, db: Session) -> bool:
        """执行主要入场订单（只创建一个订单）"""
        try:
            # 选择最佳入场价格
            entry_price = self._get_best_entry_price(signal)
            if not entry_price:
                logger.error(f"无法确定入场价格: {signal.symbol}")
                return False
            
            # 计算交易数量
            quantity = settings.default_stake_amount / entry_price
            
            # 创建单一交易订单
            order_id = await exchange_client.create_order(
                symbol=signal.symbol,
                side=signal.signal_type.value.lower(),
                amount=quantity,
                price=entry_price
            )
            
            if order_id:
                # 记录交易
                trade = SignalTrade(
                    signal_id=signal.id,
                    symbol=signal.symbol,
                    side=signal.signal_type.value,
                    entry_price=entry_price,
                    quantity=quantity,
                    status="pending",
                    order_id=order_id
                )
                db.add(trade)
                
                # 更新所有入场点状态为已提交
                for entry_point in signal.entry_points:
                    entry_point.status = "submitted"
                    entry_point.order_id = order_id
                
                db.commit()
                logger.info(f"主入场订单已创建: {signal.symbol} @ {entry_price}")
                return True
            else:
                logger.error(f"创建入场订单失败: {signal.symbol}")
                return False
                
        except Exception as e:
            logger.error(f"执行主入场订单失败: {e}")
            return False
    
    def _get_best_entry_price(self, signal: TelegramSignal) -> Optional[float]:
        """获取最佳入场价格"""
        # 如果有多个入场点，选择第一个（通常是最优价格）
        if signal.entry_points and len(signal.entry_points) > 0:
            # 按价格排序，做多选择最低价，做空选择最高价
            sorted_points = sorted(signal.entry_points, key=lambda x: x.price)
            if signal.signal_type.value.lower() == 'long':
                return sorted_points[0].price  # 做多选择最低价
            else:
                return sorted_points[-1].price  # 做空选择最高价
        
        # 如果没有入场点，使用主入场价格
        return signal.entry_price
    
    async def _setup_take_profit_points(self, signal: TelegramSignal, db: Session):
        """设置止盈点（标记为待执行）"""
        for tp_point in signal.take_profit_points:
            tp_point.status = "waiting_for_entry"
        db.commit()
    
    def _calculate_entry_quantity(self, signal: TelegramSignal, entry_point: TradeEntryPoint) -> float:
        """计算入场点数量"""
        # 基于分配比例计算
        base_amount = settings.default_stake_amount
        return base_amount * entry_point.allocation / 100
    
    async def check_and_execute_take_profits(self, db: Session):
        """检查并执行止盈点（定期任务）"""
        try:
            # 查找有持仓且有待执行止盈点的信号
            pending_tps = db.query(TradeTakeProfitPoint).filter(
                TradeTakeProfitPoint.status == "waiting_for_entry"
            ).all()
            
            for tp_point in pending_tps:
                # 检查对应信号是否有持仓
                has_position = await self._check_position_exists(tp_point.signal.symbol)
                
                if has_position:
                    # 执行止盈单
                    success = await self._execute_take_profit(tp_point)
                    if success:
                        tp_point.status = "submitted"
                        db.commit()
                        
        except Exception as e:
            logger.error(f"检查止盈点失败: {e}")
    
    async def _check_position_exists(self, symbol: str) -> bool:
        """检查是否有持仓"""
        try:
            open_trades = await freqtrade_client.get_open_trades()
            if open_trades:
                return any(trade.get("pair") == symbol for trade in open_trades)
            return False
        except:
            return False
    
    async def _execute_take_profit(self, tp_point: TradeTakeProfitPoint) -> bool:
        """执行止盈单"""
        try:
            # 计算止盈数量
            quantity = self._calculate_tp_quantity(tp_point)
            
            webhook_data = {
                "action": "sell" if tp_point.signal.signal_type.value == "buy" else "buy",
                "pair": tp_point.signal.symbol,
                "price": tp_point.price,
                "amount": quantity,
                "order_type": "limit"
            }
            
            return await freqtrade_client.send_webhook(webhook_data)
            
        except Exception as e:
            logger.error(f"执行止盈失败: {e}")
            return False
    
    def _calculate_tp_quantity(self, tp_point: TradeTakeProfitPoint) -> float:
        """计算止盈数量"""
        # 基于分配比例计算
        # 这里需要根据实际持仓计算
        return tp_point.allocation / 100  # 简化实现
    
    async def activate_take_profit_points(self, signal: TelegramSignal, db: Session):
        """激活止盈点（在入场成交后调用）"""
        try:
            for tp_point in signal.take_profit_points:
                if tp_point.status == "waiting_for_entry":
                    # 发送止盈限价单
                    success = await self._execute_take_profit(tp_point)
                    if success:
                        tp_point.status = "submitted"
                        logger.info(f"止盈单已提交: {signal.symbol} @ {tp_point.price}")
            
            db.commit()
            
        except Exception as e:
            logger.error(f"激活止盈点失败: {e}")


# 全局实例
cornix_executor = CornixSignalExecutor()