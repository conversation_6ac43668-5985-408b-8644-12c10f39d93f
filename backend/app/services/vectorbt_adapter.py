"""
VectorBT数据适配器
将husky的信号数据和K线数据转换为vectorbt所需的格式
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from sqlalchemy.orm import Session

from app.models import TelegramSignal, SignalType, SignalStatus, BacktestResult
from app.services.market_data import MarketDataService

logger = logging.getLogger(__name__)


class SignalDataAdapter:
    """信号数据适配器，将husky数据转换为vectorbt格式"""
    
    def __init__(self, db: Session):
        self.db = db
        self.market_data = MarketDataService()
    
    async def prepare_backtest_data(
        self, 
        backtest: BacktestResult
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        准备回测数据，返回价格数据、入场信号、出场信号
        
        Returns:
            Tuple[price_data, entries, exits]
        """
        logger.info(f"开始准备回测数据: {backtest.symbol} ({backtest.start_date} ~ {backtest.end_date})")
        
        # 1. 获取历史信号
        signals = await self._get_historical_signals(backtest)
        if not signals:
            raise ValueError(f"未找到符合条件的历史信号")
        
        logger.info(f"获取到 {len(signals)} 个历史信号")
        
        # 2. 获取价格数据
        price_data = await self._get_price_data(backtest, signals)
        if price_data.empty:
            raise ValueError("未能获取到价格数据")
        
        logger.info(f"获取到价格数据: {len(price_data)} 条记录")
        
        # 3. 生成信号矩阵
        entries, exits = self._generate_signal_matrices(signals, price_data)
        
        logger.info(f"生成信号矩阵完成: entries={entries.shape}, exits={exits.shape}")
        
        return price_data, entries, exits
    
    async def _get_historical_signals(self, backtest: BacktestResult) -> List[Dict[str, Any]]:
        """获取历史信号数据"""
        query = self.db.query(TelegramSignal).filter(
            TelegramSignal.signal_time >= backtest.start_date,
            TelegramSignal.signal_time <= backtest.end_date,
            TelegramSignal.status == SignalStatus.EXECUTED
        )
        
        # 如果指定了特定交易对
        if backtest.symbol != 'ALL':
            query = query.filter(TelegramSignal.symbol == backtest.symbol)
        
        # 如果指定了信号源
        if backtest.source_ids:
            # 处理source_ids，可能是JSON字符串或列表
            import json
            try:
                if isinstance(backtest.source_ids, str):
                    source_ids_list = json.loads(backtest.source_ids)
                elif isinstance(backtest.source_ids, (list, tuple)):
                    source_ids_list = list(backtest.source_ids)
                else:
                    source_ids_list = [backtest.source_ids]

                if source_ids_list:
                    query = query.filter(TelegramSignal.source_id.in_(source_ids_list))
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"解析source_ids失败: {e}, 使用原值: {backtest.source_ids}")
                # 如果解析失败，尝试直接使用
                if isinstance(backtest.source_ids, (int, str)) and str(backtest.source_ids).isdigit():
                    query = query.filter(TelegramSignal.source_id == int(backtest.source_ids))
        
        signals_db = query.order_by(TelegramSignal.signal_time).all()
        
        # 转换为字典格式
        signals = []
        for signal in signals_db:
            signal_dict = {
                'id': signal.id,
                'symbol': signal.symbol,
                'signal_type': signal.signal_type,
                'signal_time': signal.signal_time,
                'entry_price': signal.entry_price,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'quantity': signal.quantity,
                'leverage': signal.leverage or 1,
                'source_id': signal.source_id
            }
            signals.append(signal_dict)
        
        return signals
    
    async def _get_price_data(
        self, 
        backtest: BacktestResult, 
        signals: List[Dict[str, Any]]
    ) -> pd.DataFrame:
        """获取价格数据并转换为vectorbt格式"""
        
        # 获取所有需要的交易对
        if backtest.symbol == 'ALL':
            symbols = list(set(signal['symbol'] for signal in signals))
        else:
            symbols = [backtest.symbol]
        
        # 扩展时间范围以获取足够的数据
        extended_start = backtest.start_date - timedelta(days=30)
        
        # 获取K线数据
        all_price_data = {}
        for symbol in symbols:
            try:
                klines = await self.market_data.get_klines(
                    symbol=symbol,
                    interval='5m',
                    start_time=extended_start,
                    end_time=backtest.end_date,
                    force_download=False
                )
                
                if not klines.empty:
                    # 使用收盘价作为主要价格数据
                    all_price_data[symbol] = klines['close']
                    logger.info(f"获取 {symbol} 价格数据: {len(klines)} 条记录")
                else:
                    logger.warning(f"未获取到 {symbol} 的价格数据")
                    
            except Exception as e:
                logger.error(f"获取 {symbol} 价格数据失败: {e}")
                continue
        
        if not all_price_data:
            return pd.DataFrame()
        
        # 合并所有价格数据
        price_df = pd.DataFrame(all_price_data)
        
        # 填充缺失值
        price_df = price_df.ffill().bfill()

        # 确保时间范围正确 - 安全的时间比较
        try:
            start_ts = pd.Timestamp(backtest.start_date)
            end_ts = pd.Timestamp(backtest.end_date)

            # 确保索引是DatetimeIndex
            if not isinstance(price_df.index, pd.DatetimeIndex):
                price_df.index = pd.to_datetime(price_df.index)

            mask = (price_df.index >= start_ts) & (price_df.index <= end_ts)
            price_df = price_df.loc[mask]
        except Exception as e:
            logger.warning(f"时间范围过滤失败: {e}, 使用全部数据")
        
        return price_df
    
    def _generate_signal_matrices(
        self, 
        signals: List[Dict[str, Any]], 
        price_data: pd.DataFrame
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """生成入场和出场信号矩阵"""
        
        # 初始化信号矩阵
        entries = pd.DataFrame(
            False, 
            index=price_data.index, 
            columns=price_data.columns
        )
        exits = pd.DataFrame(
            False, 
            index=price_data.index, 
            columns=price_data.columns
        )
        
        # 处理每个信号
        for signal in signals:
            symbol = signal['symbol']
            signal_time = signal['signal_time']
            signal_type = signal['signal_type']
            
            if symbol not in price_data.columns:
                continue
            
            # 找到最接近的时间点
            closest_time = self._find_closest_time(signal_time, price_data.index)
            if closest_time is None:
                continue
            
            # 根据信号类型设置入场信号
            if signal_type in [SignalType.BUY, SignalType.LONG]:
                entries.loc[closest_time, symbol] = True
                
                # 设置出场信号（简化处理：固定持仓时间或价格触发）
                exit_time = self._calculate_exit_time(
                    signal, closest_time, price_data, symbol
                )
                if exit_time is not None:
                    exits.loc[exit_time, symbol] = True
                    
            elif signal_type in [SignalType.SELL, SignalType.SHORT]:
                # 对于做空信号，入场和出场逻辑相反
                entries.loc[closest_time, symbol] = True
                
                exit_time = self._calculate_exit_time(
                    signal, closest_time, price_data, symbol, is_short=True
                )
                if exit_time is not None:
                    exits.loc[exit_time, symbol] = True
        
        return entries, exits
    
    def _find_closest_time(self, target_time: datetime, time_index: pd.DatetimeIndex) -> Optional[pd.Timestamp]:
        """找到最接近的时间点"""
        try:
            # 找到最接近的时间
            closest_idx = time_index.get_indexer([target_time], method='nearest')[0]
            if closest_idx != -1:
                return time_index[closest_idx]
        except Exception as e:
            logger.warning(f"查找最接近时间失败: {e}")
        return None
    
    def _calculate_exit_time(
        self, 
        signal: Dict[str, Any], 
        entry_time: pd.Timestamp, 
        price_data: pd.DataFrame, 
        symbol: str,
        is_short: bool = False
    ) -> Optional[pd.Timestamp]:
        """计算出场时间"""
        
        entry_price = signal.get('entry_price')
        stop_loss = signal.get('stop_loss')
        take_profit = signal.get('take_profit')
        
        if not entry_price:
            # 如果没有入场价格，使用当时的市场价格
            entry_price = price_data.loc[entry_time, symbol]
        
        # 获取入场后的价格数据
        future_prices = price_data.loc[entry_time:, symbol]
        
        for timestamp, price in future_prices.items():
            if timestamp == entry_time:
                continue
            
            # 检查止损条件
            if stop_loss:
                if not is_short and price <= stop_loss:
                    return timestamp
                elif is_short and price >= stop_loss:
                    return timestamp
            
            # 检查止盈条件
            if take_profit:
                if not is_short and price >= take_profit:
                    return timestamp
                elif is_short and price <= take_profit:
                    return timestamp
        
        # 如果没有触发止损止盈，使用默认持仓时间（24小时）
        default_exit = entry_time + timedelta(hours=24)
        if default_exit in price_data.index:
            return default_exit
        
        # 如果默认时间超出数据范围，使用最后一个时间点
        if len(price_data.index) > 0:
            return price_data.index[-1]
        return None
    
    def format_vectorbt_results(self, portfolio, backtest: BacktestResult) -> Dict[str, Any]:
        """将vectorbt结果转换为husky格式"""

        try:
            logger.info("开始格式化VectorBT结果...")

            # 基础统计
            stats = portfolio.stats()

            # 计算基础指标 - 添加更多错误处理
            try:
                total_return = self._safe_extract_value(portfolio.total_return())
                final_value = self._safe_extract_value(portfolio.value().iloc[-1])
                initial_value = self._safe_extract_value(portfolio.value().iloc[0])
            except Exception as e:
                logger.warning(f"计算基础指标失败: {e}")
                total_return = 0.0
                final_value = float(backtest.initial_balance)
                initial_value = float(backtest.initial_balance)

            # 交易统计 - 增强版本
            trades_analysis = self._analyze_trades(portfolio)

            # 风险指标
            risk_metrics = self._calculate_risk_metrics(portfolio, backtest)

            # 性能指标
            performance_metrics = self._calculate_performance_metrics(portfolio, backtest)

            # 时间序列分析
            time_series_analysis = self._analyze_time_series(portfolio)

            # 构建完整结果
            results = {
                'summary': {
                    # 基础指标
                    'initial_balance': float(backtest.initial_balance),
                    'final_balance': float(final_value),
                    'total_return': float(total_return),
                    'total_return_pct': float(total_return * 100),
                    'net_profit': float(final_value - initial_value),

                    # 交易统计
                    **trades_analysis,

                    # 风险指标
                    **risk_metrics,

                    # 性能指标
                    **performance_metrics,

                    # 时间分析
                    **time_series_analysis
                },
                'portfolio': portfolio,  # 保存portfolio对象用于后续分析
                'stats': stats,
                'detailed_trades': self._get_detailed_trades(portfolio),
                'monthly_returns': self._calculate_monthly_returns(portfolio),
                'config': {
                    'backtest_period': f"{backtest.start_date} ~ {backtest.end_date}",
                    'symbol': backtest.symbol,
                    'engine': 'vectorbt'
                }
            }

            logger.info("VectorBT结果格式化完成")
            return results

        except Exception as e:
            logger.error(f"格式化vectorbt结果失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 返回基础结果
            return {
                'summary': {
                    'initial_balance': float(backtest.initial_balance),
                    'final_balance': float(backtest.initial_balance),
                    'total_return': 0.0,
                    'total_return_pct': 0.0,
                    'net_profit': 0.0,
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'max_drawdown': 0.0,
                    'max_drawdown_pct': 0.0,
                    'sharpe_ratio': 0.0,
                    'calmar_ratio': 0.0,
                    'gross_profit': 0.0,
                    'gross_loss': 0.0
                },
                'error': str(e)
            }

    def _safe_extract_value(self, value) -> float:
        """安全提取数值"""
        try:
            if hasattr(value, 'iloc'):
                return float(value.iloc[0])
            elif hasattr(value, 'values'):
                return float(value.values[0])
            else:
                return float(value)
        except:
            return 0.0

    def _analyze_trades(self, portfolio) -> Dict[str, Any]:
        """分析交易统计"""
        try:
            trades = portfolio.trades
            total_trades = len(trades.records_arr) if hasattr(trades, 'records_arr') else 0

            if total_trades == 0:
                return {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'gross_profit': 0.0,
                    'gross_loss': 0.0,
                    'avg_win': 0.0,
                    'avg_loss': 0.0,
                    'largest_win': 0.0,
                    'largest_loss': 0.0
                }

            # 获取交易记录
            pnl_values = trades.records_arr['pnl'] if hasattr(trades, 'records_arr') else []

            winning_trades = len(pnl_values[pnl_values > 0])
            losing_trades = len(pnl_values[pnl_values < 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # 盈亏统计
            profits = pnl_values[pnl_values > 0]
            losses = pnl_values[pnl_values < 0]

            gross_profit = np.sum(profits) if len(profits) > 0 else 0
            gross_loss = abs(np.sum(losses)) if len(losses) > 0 else 0
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

            avg_win = np.mean(profits) if len(profits) > 0 else 0
            avg_loss = abs(np.mean(losses)) if len(losses) > 0 else 0
            largest_win = np.max(profits) if len(profits) > 0 else 0
            largest_loss = abs(np.min(losses)) if len(losses) > 0 else 0

            return {
                'total_trades': int(total_trades),
                'winning_trades': int(winning_trades),
                'losing_trades': int(losing_trades),
                'win_rate': float(win_rate),
                'win_rate_pct': float(win_rate * 100),
                'profit_factor': float(profit_factor),
                'gross_profit': float(gross_profit),
                'gross_loss': float(gross_loss),
                'avg_win': float(avg_win),
                'avg_loss': float(avg_loss),
                'largest_win': float(largest_win),
                'largest_loss': float(largest_loss)
            }

        except Exception as e:
            logger.error(f"交易分析失败: {e}")
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'gross_profit': 0.0,
                'gross_loss': 0.0
            }

    def _calculate_risk_metrics(self, portfolio, backtest: BacktestResult) -> Dict[str, Any]:
        """计算风险指标"""
        try:
            # 最大回撤
            max_drawdown = self._safe_extract_value(portfolio.max_drawdown())
            max_drawdown_pct = max_drawdown * 100

            # 波动率
            returns = portfolio.returns()
            volatility = self._safe_extract_value(returns.std()) * np.sqrt(252)  # 年化波动率

            # VaR (Value at Risk) - 95%置信度
            var_95 = np.percentile(returns.dropna(), 5) if len(returns.dropna()) > 0 else 0

            return {
                'max_drawdown': float(max_drawdown),
                'max_drawdown_pct': float(max_drawdown_pct),
                'volatility': float(volatility),
                'volatility_pct': float(volatility * 100),
                'var_95': float(var_95),
                'var_95_pct': float(var_95 * 100)
            }

        except Exception as e:
            logger.error(f"风险指标计算失败: {e}")
            return {
                'max_drawdown': 0.0,
                'max_drawdown_pct': 0.0,
                'volatility': 0.0,
                'var_95': 0.0
            }

    def _calculate_performance_metrics(self, portfolio, backtest: BacktestResult) -> Dict[str, Any]:
        """计算性能指标"""
        try:
            # 夏普比率
            sharpe_ratio = self._safe_extract_value(portfolio.sharpe_ratio())

            # 卡尔马比率 (Calmar Ratio)
            total_return = self._safe_extract_value(portfolio.total_return())
            max_drawdown = self._safe_extract_value(portfolio.max_drawdown())
            calmar_ratio = total_return / abs(max_drawdown) if max_drawdown != 0 else 0

            # 索提诺比率 (Sortino Ratio)
            returns = portfolio.returns()
            downside_returns = returns[returns < 0]
            downside_std = downside_returns.std() if len(downside_returns) > 0 else 0
            sortino_ratio = returns.mean() / downside_std if downside_std != 0 else 0

            return {
                'sharpe_ratio': float(sharpe_ratio),
                'calmar_ratio': float(calmar_ratio),
                'sortino_ratio': float(sortino_ratio)
            }

        except Exception as e:
            logger.error(f"性能指标计算失败: {e}")
            return {
                'sharpe_ratio': 0.0,
                'calmar_ratio': 0.0,
                'sortino_ratio': 0.0
            }

    def _analyze_time_series(self, portfolio) -> Dict[str, Any]:
        """时间序列分析"""
        try:
            # 获取价值序列
            value_series = portfolio.value()

            # 计算持仓时间统计
            trades = portfolio.trades
            if hasattr(trades, 'records_arr') and len(trades.records_arr) > 0:
                durations = trades.records_arr['exit_idx'] - trades.records_arr['entry_idx']
                avg_holding_period = np.mean(durations) * 5  # 5分钟K线
                max_holding_period = np.max(durations) * 5
                min_holding_period = np.min(durations) * 5
            else:
                avg_holding_period = max_holding_period = min_holding_period = 0

            return {
                'avg_holding_period_minutes': float(avg_holding_period),
                'max_holding_period_minutes': float(max_holding_period),
                'min_holding_period_minutes': float(min_holding_period),
                'total_periods': len(value_series)
            }

        except Exception as e:
            logger.error(f"时间序列分析失败: {e}")
            return {
                'avg_holding_period_minutes': 0.0,
                'max_holding_period_minutes': 0.0,
                'min_holding_period_minutes': 0.0,
                'total_periods': 0
            }

    def _get_detailed_trades(self, portfolio) -> List[Dict[str, Any]]:
        """获取详细交易记录"""
        try:
            trades = portfolio.trades
            if not hasattr(trades, 'records_arr') or len(trades.records_arr) == 0:
                return []

            detailed_trades = []
            for i, trade in enumerate(trades.records_arr):
                detailed_trades.append({
                    'trade_id': int(i + 1),
                    'entry_time': str(trade['entry_idx']),
                    'exit_time': str(trade['exit_idx']),
                    'entry_price': float(trade['entry_price']),
                    'exit_price': float(trade['exit_price']),
                    'size': float(trade['size']),
                    'pnl': float(trade['pnl']),
                    'return_pct': float(trade['return']),
                    'fees': float(trade['fees']),
                    'duration_periods': int(trade['exit_idx'] - trade['entry_idx'])
                })

            return detailed_trades

        except Exception as e:
            logger.error(f"获取详细交易记录失败: {e}")
            return []

    def _calculate_monthly_returns(self, portfolio) -> Dict[str, float]:
        """计算月度收益"""
        try:
            returns = portfolio.returns()
            if returns.empty:
                return {}

            # 按月分组计算收益
            monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)

            result = {}
            for date, ret in monthly_returns.items():
                month_key = date.strftime('%Y-%m')
                result[month_key] = float(ret)

            return result

        except Exception as e:
            logger.error(f"计算月度收益失败: {e}")
            return {}
