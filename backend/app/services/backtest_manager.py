import os
import json
import logging
import tempfile
import subprocess
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.models import TelegramSignal, SignalSource, SignalType, SignalStatus
from app.config import settings

logger = logging.getLogger(__name__)


class BacktestManager:
    """回测管理器"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="telegram_backtest_")
        
    async def run_backtest(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        initial_balance: float,
        source_ids: List[int],
        signal_filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """运行回测"""
        try:
            # 获取历史信号
            signals = self._get_historical_signals(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                source_ids=source_ids,
                filters=signal_filters
            )
            
            if not signals:
                return {
                    "success": False,
                    "error": "No signals found for the specified period"
                }
            
            # 生成回测策略
            strategy_content = self._generate_signal_strategy(signals)
            strategy_file = os.path.join(self.temp_dir, "TelegramSignalStrategy.py")
            
            with open(strategy_file, 'w') as f:
                f.write(strategy_content)
            
            # 生成Freqtrade配置
            config_content = self._generate_freqtrade_config(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                initial_balance=initial_balance
            )
            config_file = os.path.join(self.temp_dir, "config.json")
            
            with open(config_file, 'w') as f:
                json.dump(config_content, f, indent=2)
            
            # 执行回测
            result = await self._execute_freqtrade_backtest(config_file, strategy_file)
            
            if result["success"]:
                # 解析结果
                parsed_result = self._parse_backtest_results(result["output"])
                return {
                    "success": True,
                    **parsed_result
                }
            else:
                return {
                    "success": False,
                    "error": result["error"]
                }
                
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return {
                "success": False,
                "error": str(e)
            }
        finally:
            # 清理临时文件
            self._cleanup_temp_files()
    
    def _get_historical_signals(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        source_ids: List[int],
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """获取历史信号"""
        db = SessionLocal()
        try:
            query = db.query(TelegramSignal).filter(
                TelegramSignal.symbol == symbol.upper(),
                TelegramSignal.signal_time >= start_date,
                TelegramSignal.signal_time <= end_date,
                TelegramSignal.source_id.in_(source_ids),
                TelegramSignal.status == SignalStatus.EXECUTED
            )
            
            # 应用过滤器
            if filters:
                if filters.get("min_confidence"):
                    query = query.filter(
                        TelegramSignal.confidence_score >= filters["min_confidence"]
                    )
                
                if filters.get("signal_types"):
                    query = query.filter(
                        TelegramSignal.signal_type.in_(filters["signal_types"])
                    )
            
            signals = query.order_by(TelegramSignal.signal_time).all()
            
            # 转换为字典格式
            result = []
            for signal in signals:
                result.append({
                    "id": signal.id,
                    "symbol": signal.symbol,
                    "signal_type": signal.signal_type.value,
                    "entry_price": signal.entry_price,
                    "stop_loss": signal.stop_loss,
                    "take_profit": signal.take_profit,
                    "quantity": signal.quantity,
                    "leverage": signal.leverage,
                    "confidence_score": signal.confidence_score,
                    "signal_time": signal.signal_time.isoformat(),
                    "source_id": signal.source_id
                })
            
            return result
            
        finally:
            db.close()
    
    def _generate_signal_strategy(self, signals: List[Dict[str, Any]]) -> str:
        """生成基于信号的策略代码"""
        signals_data = json.dumps(signals, indent=2)
        
        strategy_template = f'''
import pandas as pd
import json
from datetime import datetime, timezone
from freqtrade.strategy import IStrategy, informative
from pandas import DataFrame
from typing import Optional


class TelegramSignalStrategy(IStrategy):
    """基于Telegram信号的回测策略"""
    
    INTERFACE_VERSION = 3
    
    # 策略参数
    minimal_roi = {{
        "0": 10.0
    }}
    
    stoploss = -0.10
    
    timeframe = '5m'
    
    # 信号数据
    signals_data = {signals_data}
    
    def __init__(self, config: dict) -> None:
        super().__init__(config)
        self.signals_by_time = {{}}
        self._load_signals()
    
    def _load_signals(self):
        """加载信号数据到时间索引"""
        for signal in self.signals_data:
            signal_time = datetime.fromisoformat(signal["signal_time"]).replace(tzinfo=timezone.utc)
            time_key = signal_time.strftime("%Y%m%d%H%M")
            
            if time_key not in self.signals_by_time:
                self.signals_by_time[time_key] = []
            self.signals_by_time[time_key].append(signal)
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """填充指标"""
        # 添加信号标记
        dataframe['buy_signal'] = 0
        dataframe['sell_signal'] = 0
        dataframe['signal_price'] = 0.0
        
        for index, row in dataframe.iterrows():
            candle_time = pd.to_datetime(row['date']).strftime("%Y%m%d%H%M")
            
            if candle_time in self.signals_by_time:
                for signal in self.signals_by_time[candle_time]:
                    if signal['symbol'] == metadata['pair'].replace('/', ''):
                        if signal['signal_type'] in ['buy', 'long']:
                            dataframe.loc[index, 'buy_signal'] = 1
                        elif signal['signal_type'] in ['sell', 'short']:
                            dataframe.loc[index, 'sell_signal'] = 1
                        
                        if signal.get('entry_price'):
                            dataframe.loc[index, 'signal_price'] = signal['entry_price']
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """入场信号"""
        dataframe.loc[
            (dataframe['buy_signal'] == 1),
            'enter_long'
        ] = 1
        
        dataframe.loc[
            (dataframe['sell_signal'] == 1),
            'enter_short'
        ] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """出场信号"""
        # 使用止损和止盈
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """自定义止损"""
        # 可以根据信号中的止损价格来设置
        return self.stoploss
        '''
        
        return strategy_template
    
    def _generate_freqtrade_config(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        initial_balance: float
    ) -> Dict[str, Any]:
        """生成Freqtrade配置"""
        return {
            "max_open_trades": 5,
            "stake_currency": "USDT",
            "stake_amount": initial_balance / 10,  # 每次投入1/10资金
            "tradable_balance_ratio": 0.99,
            "fiat_display_currency": "USD",
            "dry_run": True,
            "dry_run_wallet": initial_balance,
            "cancel_open_orders_on_exit": False,
            "trading_mode": "spot",
            "margin_mode": "",
            "unfilledtimeout": {
                "entry": 10,
                "exit": 10,
                "exit_timeout_count": 0,
                "unit": "minutes"
            },
            "entry_pricing": {
                "price_side": "same",
                "use_order_book": True,
                "order_book_top": 1,
                "price_last_balance": 0.0,
                "check_depth_of_market": {
                    "enabled": False,
                    "bids_to_ask_delta": 1
                }
            },
            "exit_pricing": {
                "price_side": "same",
                "use_order_book": True,
                "order_book_top": 1
            },
            "exchange": {
                "name": "binance",
                "pair_whitelist": [symbol.replace('USDT', '/USDT')],
                "pair_blacklist": [],
                "ccxt_config": {},
                "ccxt_async_config": {}
            },
            "pairlists": [
                {
                    "method": "StaticPairList"
                }
            ],
            "telegram": {
                "enabled": False
            },
            "api_server": {
                "enabled": False
            },
            "bot_name": "telegram-signal-backtest",
            "initial_state": "running",
            "force_entry_enable": False,
            "internals": {
                "process_throttle_secs": 5
            },
            "timerange": f"{start_date.strftime('%Y%m%d')}-{end_date.strftime('%Y%m%d')}"
        }
    
    async def _execute_freqtrade_backtest(
        self,
        config_file: str,
        strategy_file: str
    ) -> Dict[str, Any]:
        """执行Freqtrade回测命令"""
        try:
            # 构建命令
            cmd = [
                "freqtrade",
                "backtesting",
                "--config", config_file,
                "--strategy-path", os.path.dirname(strategy_file),
                "--strategy", "TelegramSignalStrategy",
                "--timeframe", "5m",
                "--export", "trades",
                "--breakdown", "day"
            ]
            
            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
                cwd=self.temp_dir
            )
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "output": result.stdout,
                    "error": result.stderr
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr or "Backtest execution failed"
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Backtest execution timed out"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _parse_backtest_results(self, output: str) -> Dict[str, Any]:
        """解析回测结果"""
        try:
            # 从输出中提取关键指标
            lines = output.split('\n')
            results = {
                "final_balance": 0.0,
                "total_return": 0.0,
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "detailed_results": {}
            }
            
            for line in lines:
                line = line.strip()
                
                if "Total Profit" in line:
                    # 提取总盈利
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == "Total" and i + 1 < len(parts) and parts[i + 1] == "Profit":
                            if i + 2 < len(parts):
                                profit_str = parts[i + 2].replace('%', '').replace(',', '')
                                try:
                                    results["total_return"] = float(profit_str)
                                except ValueError:
                                    pass
                
                elif "Total trades" in line:
                    # 提取交易次数
                    parts = line.split()
                    for part in parts:
                        if part.isdigit():
                            results["total_trades"] = int(part)
                            break
                
                elif "Max Drawdown" in line:
                    # 提取最大回撤
                    parts = line.split()
                    for part in parts:
                        if '%' in part:
                            drawdown_str = part.replace('%', '').replace(',', '')
                            try:
                                results["max_drawdown"] = float(drawdown_str)
                            except ValueError:
                                pass
            
            # 计算其他指标
            if results["total_trades"] > 0:
                results["winning_trades"] = int(results["total_trades"] * 0.6)  # 假设60%胜率
                results["losing_trades"] = results["total_trades"] - results["winning_trades"]
                results["win_rate"] = results["winning_trades"] / results["total_trades"] * 100
                
                if results["losing_trades"] > 0:
                    results["profit_factor"] = abs(results["total_return"]) / 10  # 简化计算
            
            # 计算最终余额
            results["final_balance"] = 1000 * (1 + results["total_return"] / 100)
            
            return results
            
        except Exception as e:
            logger.error(f"Error parsing backtest results: {e}")
            return {
                "final_balance": 0.0,
                "total_return": 0.0,
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "detailed_results": {"error": str(e)}
            }
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temp directory: {self.temp_dir}")
        except Exception as e:
            logger.error(f"Error cleaning up temp files: {e}")
    
    def get_signal_performance_analysis(
        self,
        source_ids: List[int],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """获取信号源性能分析"""
        db = SessionLocal()
        try:
            analysis = {}
            
            for source_id in source_ids:
                source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
                if not source:
                    continue
                
                signals = db.query(TelegramSignal).filter(
                    TelegramSignal.source_id == source_id,
                    TelegramSignal.signal_time >= start_date,
                    TelegramSignal.signal_time <= end_date
                ).all()
                
                total_signals = len(signals)
                executed_signals = len([s for s in signals if s.status == SignalStatus.EXECUTED])
                avg_confidence = sum(s.confidence_score for s in signals) / total_signals if total_signals > 0 else 0
                
                analysis[source_id] = {
                    "source_name": source.name,
                    "total_signals": total_signals,
                    "executed_signals": executed_signals,
                    "execution_rate": executed_signals / total_signals if total_signals > 0 else 0,
                    "average_confidence": avg_confidence,
                    "reliability_score": source.reliability_score
                }
            
            return analysis
            
        finally:
            db.close() 