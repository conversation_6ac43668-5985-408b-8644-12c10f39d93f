import logging
from typing import Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime

from app.config import settings
from app.services.exchange_client import exchange_client
from app.services.cornix_signal_executor import cornix_executor
from app.models import TelegramSignal, SignalStatus, SignalTrade

logger = logging.getLogger(__name__)


class FreqtradeManager:
    """Freqtrade统一管理器"""
    
    async def process_signal(self, signal: TelegramSignal, db: Session) -> bool:
        """处理交易信号 - 核心方法"""
        try:
            if not settings.auto_trading_enabled:
                logger.info(f"自动交易未启用，跳过信号: {signal.symbol}")
                signal.status = SignalStatus.CANCELLED
                db.commit()
                return False
            
            # 使用Cornix执行器处理复杂信号
            success = await cornix_executor.execute_signal(signal, db)
            
            # 更新信号状态
            if success:
                signal.status = SignalStatus.EXECUTED
                logger.info(f"信号执行成功: {signal.symbol} {signal.signal_type.value}")
            else:
                signal.status = SignalStatus.FAILED
                logger.error(f"信号执行失败: {signal.symbol}")
            
            signal.processed_at = datetime.utcnow()
            db.commit()
            return success
            
        except Exception as e:
            logger.error(f"处理信号异常: {e}")
            signal.status = SignalStatus.FAILED
            signal.processed_at = datetime.utcnow()
            db.commit()
            return False
    
    async def get_status(self) -> Dict[str, Any]:
        """获取交易状态"""
        try:
            balance = await exchange_client.get_balance()
            positions = await exchange_client.get_positions()
            
            return {
                "connected": True,
                "trading_mode": settings.trading_mode,
                "auto_trading_enabled": settings.auto_trading_enabled,
                "balance": balance,
                "positions": positions,
                "mode_description": "模拟交易" if settings.is_dry_run_mode else "真实交易"
            }
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {"error": str(e), "connected": False}


# 全局实例
freqtrade_manager = FreqtradeManager()