import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.database import SessionLocal
from app.models import (
    TelegramSignal, SignalSource, SignalStatus, SignalType,
    TradeEntryPoint, TradeTakeProfitPoint, SignalTrade
)
from app.services.signal_parser import EnhancedSignalParser
from app.services.llm_signal_parser import LLMSignalParserFactory
from app.services.signal_executor import get_signal_executor
from app.services.config_manager import config_manager

logger = logging.getLogger(__name__)


class SignalManager:
    """信号管理器 - 统一管理信号的生命周期"""
    
    def __init__(self):
        self.builtin_parser = EnhancedSignalParser()
        self.llm_parser = None
        self.auto_execute_enabled = True
        self.processing_signals = set()
        
    async def process_raw_signal(
        self, 
        raw_message: str, 
        source_id: int,
        message_id: Optional[str] = None,
        signal_time: Optional[datetime] = None
    ) -> Optional[Dict[str, Any]]:
        """处理原始信号文本，解析并存储"""
        try:
            logger.info(f"[SIGNAL_DEBUG] 开始处理信号 - message_id: {message_id}, source_id: {source_id}")
            logger.info(f"[SIGNAL_DEBUG] 原始消息: {raw_message[:200]}...")
            
            # 基于Telegram消息ID的严格去重
            if message_id:
                db = SessionLocal()
                try:
                    existing = db.query(TelegramSignal).filter(
                        TelegramSignal.message_id == message_id,
                        TelegramSignal.source_id == source_id
                    ).first()
                    if existing:
                        logger.info(f"[SIGNAL_DEBUG] 消息ID {message_id} 已存在，跳过处理: {existing.id}")
                        return None
                finally:
                    db.close()
            
            # 如果没有message_id，基于消息内容hash去重
            else:
                import hashlib
                message_hash = hashlib.md5(raw_message.encode()).hexdigest()
                db = SessionLocal()
                try:
                    # 检查相同内容的消息是否在近5分钟内处理过
                    from datetime import timedelta
                    recent_time = datetime.utcnow() - timedelta(minutes=5)
                    existing = db.query(TelegramSignal).filter(
                        TelegramSignal.source_id == source_id,
                        TelegramSignal.received_at >= recent_time,
                        TelegramSignal.raw_message == raw_message
                    ).first()
                    if existing:
                        logger.info(f"[SIGNAL_DEBUG] 相同内容消息已处理，跳过: {existing.id}")
                        return None
                finally:
                    db.close()
            
            # 获取当前使用的解析器
            parser = await self._get_active_parser()
            
            # 解析信号
            parsed_signal = await parser.parse_message(raw_message)
            if not parsed_signal:
                logger.debug(f"无法解析信号: {raw_message[:100]}...")
                return None
            
            logger.info(f"[SIGNAL_DEBUG] 信号解析成功: {parsed_signal['symbol']} {parsed_signal['signal_type']}")
            
            # 验证信号
            validation_result = parser.validate_signal(parsed_signal)
            if not validation_result['is_valid']:
                logger.warning(f"信号验证失败: {validation_result['errors']}")
                return None
            
            # 存储信号
            signal_id = await self._store_signal(
                parsed_signal, source_id, raw_message, 
                message_id, signal_time
            )
            
            if signal_id:
                logger.info(f"[SIGNAL_DEBUG] 信号存储成功: {signal_id}")
                
                # 如果启用自动执行，则加入执行队列
                if self.auto_execute_enabled:
                    logger.info(f"[SIGNAL_DEBUG] 创建自动执行任务: {signal_id}")
                    asyncio.create_task(self._auto_execute_signal(signal_id))
                
                return {
                    'signal_id': signal_id,
                    'parsed_data': parsed_signal,
                    'validation': validation_result
                }
            
            return None
            
        except Exception as e:
            logger.error(f"处理信号失败: {e}")
            return None
    
    async def _store_signal(
        self,
        parsed_signal: Dict[str, Any],
        source_id: int,
        raw_message: str,
        message_id: Optional[str] = None,
        signal_time: Optional[datetime] = None
    ) -> Optional[int]:
        """存储解析后的信号到数据库"""
        db = SessionLocal()
        try:
            # 创建主信号记录
            signal = TelegramSignal(
                source_id=source_id,
                message_id=message_id,
                symbol=parsed_signal['symbol'],
                signal_type=parsed_signal['signal_type'],
                entry_price=parsed_signal.get('entry_price'),
                stop_loss=parsed_signal.get('stop_loss'),
                take_profit=parsed_signal.get('take_profit'),
                quantity=parsed_signal.get('quantity'),
                leverage=parsed_signal.get('leverage', 1),
                raw_message=raw_message,
                status=SignalStatus.PENDING,
                signal_time=signal_time or datetime.utcnow(),
                received_at=datetime.utcnow()
            )
            
            db.add(signal)
            db.flush()  # 获取signal.id
            
            # 创建入场点记录
            entry_prices = parsed_signal.get('entry_prices', [])
            entry_price_range = parsed_signal.get('entry_price_range', [])
            
            # 优先使用entry_prices，然后是entry_price_range，最后是单一entry_price
            if entry_prices and len(entry_prices) > 0:
                allocation_per_entry = 1.0 / len(entry_prices)
                for i, price in enumerate(entry_prices):
                    entry_point = TradeEntryPoint(
                        signal_id=signal.id,
                        price=price,
                        allocation=allocation_per_entry,
                        description=f"入场点 {i+1}",
                        status="pending"
                    )
                    db.add(entry_point)
            elif entry_price_range and len(entry_price_range) > 0:
                allocation_per_entry = 1.0 / len(entry_price_range)
                for i, price in enumerate(entry_price_range):
                    entry_point = TradeEntryPoint(
                        signal_id=signal.id,
                        price=price,
                        allocation=allocation_per_entry,
                        description=f"入场点 {i+1}",
                        status="pending"
                    )
                    db.add(entry_point)
            elif parsed_signal.get('entry_price'):
                # 单一入场价格
                entry_point = TradeEntryPoint(
                    signal_id=signal.id,
                    price=parsed_signal['entry_price'],
                    allocation=1.0,
                    description="主入场点",
                    status="pending"
                )
                db.add(entry_point)
            
            # 创建止盈点记录
            take_profit_targets = parsed_signal.get('take_profit_targets', [])
            
            if take_profit_targets and len(take_profit_targets) > 0:
                allocation_per_tp = 1.0 / len(take_profit_targets)
                for i, price in enumerate(take_profit_targets):
                    tp_point = TradeTakeProfitPoint(
                        signal_id=signal.id,
                        price=price,
                        allocation=allocation_per_tp,
                        description=f"止盈点 {i+1}",
                        status="pending"
                    )
                    db.add(tp_point)
            elif parsed_signal.get('take_profit'):
                # 单一止盈价格
                tp_point = TradeTakeProfitPoint(
                    signal_id=signal.id,
                    price=parsed_signal['take_profit'],
                    allocation=1.0,
                    description="主止盈点",
                    status="pending"
                )
                db.add(tp_point)
            
            db.commit()
            
            # 更新信号源统计
            await self._update_source_stats(source_id, db)
            
            logger.info(f"信号存储成功: {signal.id} - {signal.symbol} {signal.signal_type}")
            return signal.id
            
        except Exception as e:
            db.rollback()
            logger.error(f"存储信号失败: {e}")
            return None
        finally:
            db.close()
    
    async def _update_source_stats(self, source_id: int, db: Session):
        """更新信号源统计信息"""
        try:
            source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
            if source:
                source.total_signals += 1
                db.commit()
        except Exception as e:
            logger.error(f"更新信号源统计失败: {e}")
    
    async def _auto_execute_signal(self, signal_id: int):
        """自动执行信号"""
        logger.info(f"[EXECUTE_DEBUG] 开始自动执行信号: {signal_id}")
        
        if signal_id in self.processing_signals:
            logger.warning(f"[EXECUTE_DEBUG] 信号 {signal_id} 已在处理中，跳过")
            return
        
        self.processing_signals.add(signal_id)
        logger.info(f"[EXECUTE_DEBUG] 信号 {signal_id} 加入处理队列")
        
        try:
            # 等待一小段时间避免重复处理
            await asyncio.sleep(0.1)
            
            db = SessionLocal()
            try:
                signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
                if not signal:
                    logger.warning(f"[EXECUTE_DEBUG] 信号 {signal_id} 不存在")
                    return
                
                logger.info(f"[EXECUTE_DEBUG] 信号 {signal_id} 当前状态: {signal.status}")
                
                if signal.status != SignalStatus.PENDING:
                    logger.warning(f"[EXECUTE_DEBUG] 信号 {signal_id} 状态不是PENDING: {signal.status}，跳过执行")
                    return
                
                # 使用Freqtrade管理器执行信号
                from app.services.freqtrade_manager import freqtrade_manager
                
                # 标记为处理中
                signal.status = SignalStatus.PROCESSING
                db.commit()
                logger.info(f"[EXECUTE_DEBUG] 信号 {signal_id} 状态更新为PROCESSING")
                
                # 执行信号
                logger.info(f"[EXECUTE_DEBUG] 开始执行信号 {signal_id}")
                success = await freqtrade_manager.process_signal(signal, db)
                result = {
                    'success': success,
                    'message': '信号处理成功' if success else '信号处理失败'
                }
                
                if result['success']:
                    logger.info(f"[EXECUTE_DEBUG] 信号自动执行成功: {signal_id}")
                else:
                    logger.warning(f"[EXECUTE_DEBUG] 信号自动执行失败: {signal_id} - {result['message']}")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"[EXECUTE_DEBUG] 自动执行信号异常: {signal_id} - {e}")
        finally:
            self.processing_signals.discard(signal_id)
            logger.info(f"[EXECUTE_DEBUG] 信号 {signal_id} 从处理队列移除")
    
    async def manual_execute_signal(self, signal_id: int) -> Dict[str, Any]:
        """手动执行信号"""
        try:
            db = SessionLocal()
            try:
                signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
                if not signal:
                    return {'success': False, 'message': '信号不存在'}
                
                if signal.status != SignalStatus.PENDING:
                    return {'success': False, 'message': f'信号状态不允许执行: {signal.status}'}
                
                # 获取信号执行器
                executor = await get_signal_executor()
                if not executor:
                    return {'success': False, 'message': '信号执行器不可用'}
                
                # 执行信号
                result = await executor.execute_signal(signal, db)
                return result
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"手动执行信号失败: {signal_id} - {e}")
            return {'success': False, 'message': f'执行失败: {str(e)}'}
    
    async def get_signal_list(
        self,
        filters: Optional[Dict[str, Any]] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """获取信号列表"""
        try:
            db = SessionLocal()
            try:
                query = db.query(TelegramSignal).join(SignalSource)
                
                # 应用过滤条件
                if filters:
                    if filters.get('symbol'):
                        query = query.filter(TelegramSignal.symbol.ilike(f"%{filters['symbol']}%"))
                    
                    if filters.get('signal_type'):
                        query = query.filter(TelegramSignal.signal_type == filters['signal_type'])
                    
                    if filters.get('status'):
                        query = query.filter(TelegramSignal.status == filters['status'])
                    
                    if filters.get('source_id'):
                        query = query.filter(TelegramSignal.source_id == filters['source_id'])
                    
                    if filters.get('start_date'):
                        query = query.filter(TelegramSignal.signal_time >= filters['start_date'])
                    
                    if filters.get('end_date'):
                        query = query.filter(TelegramSignal.signal_time <= filters['end_date'])
                    
                    if filters.get('search'):
                        search_term = f"%{filters['search']}%"
                        query = query.filter(
                            or_(
                                TelegramSignal.symbol.ilike(search_term),
                                TelegramSignal.raw_message.ilike(search_term)
                            )
                        )
                
                # 获取总数
                total = query.count()
                
                # 分页
                offset = (page - 1) * page_size
                signals = query.order_by(TelegramSignal.signal_time.desc()).offset(offset).limit(page_size).all()
                
                # 格式化结果
                signal_list = []
                for signal in signals:
                    # 获取入场点和止盈点信息
                    entry_points = db.query(TradeEntryPoint).filter(
                        TradeEntryPoint.signal_id == signal.id
                    ).all()
                    
                    tp_points = db.query(TradeTakeProfitPoint).filter(
                        TradeTakeProfitPoint.signal_id == signal.id
                    ).all()
                    
                    # 计算状态和盈亏
                    entry_status = self._calculate_entry_status(entry_points)
                    tp_status = self._calculate_tp_status(tp_points)
                    overall_status = self._calculate_overall_status(entry_status, tp_status, signal.status)
                    total_pnl = sum(tp.pnl or 0 for tp in tp_points)
                    
                    signal_data = {
                        'id': signal.id,
                        'symbol': signal.symbol,
                        'signal_type': signal.signal_type.value,
                        'entry_price': signal.entry_price,
                        'stop_loss': signal.stop_loss,
                        'take_profit': signal.take_profit,
                        'leverage': signal.leverage,
                        'status': signal.status.value,
                        'source_id': signal.source_id,
                        'signal_time': signal.signal_time.isoformat(),
                        'received_at': signal.received_at.isoformat(),
                        'raw_message': signal.raw_message,
                        'entry_points_count': len(entry_points),
                        'tp_points_count': len(tp_points),
                        'entry_status': entry_status,
                        'tp_status': tp_status,
                        'overall_status': overall_status,
                        'total_pnl': total_pnl,
                        'can_execute': signal.status == SignalStatus.PENDING
                    }
                    signal_list.append(signal_data)
                
                return {
                    'success': True,
                    'data': {
                        'items': signal_list,
                        'total': total,
                        'page': page,
                        'page_size': page_size,
                        'pages': (total + page_size - 1) // page_size
                    }
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取信号列表失败: {e}")
            return {'success': False, 'message': f'获取信号列表失败: {str(e)}'}
    
    def _calculate_entry_status(self, entry_points: List[TradeEntryPoint]) -> str:
        """计算入场点状态"""
        if not entry_points:
            return "none"
        
        filled_count = sum(1 for ep in entry_points if ep.status == "filled")
        partial_count = sum(1 for ep in entry_points if ep.status == "partial")
        
        if filled_count == len(entry_points):
            return "filled"
        elif filled_count > 0 or partial_count > 0:
            return "partial"
        else:
            return "pending"
    
    def _calculate_tp_status(self, tp_points: List[TradeTakeProfitPoint]) -> str:
        """计算止盈点状态"""
        if not tp_points:
            return "none"
        
        filled_count = sum(1 for tp in tp_points if tp.status == "filled")
        partial_count = sum(1 for tp in tp_points if tp.status == "partial")
        
        if filled_count == len(tp_points):
            return "filled"
        elif filled_count > 0 or partial_count > 0:
            return "partial"
        else:
            return "pending"
    
    def _calculate_overall_status(self, entry_status: str, tp_status: str, signal_status: SignalStatus) -> str:
        """计算整体状态"""
        if signal_status == SignalStatus.FAILED:
            return "failed"
        elif signal_status == SignalStatus.CANCELLED:
            return "cancelled"
        elif entry_status == "filled" and tp_status == "filled":
            return "completed"
        elif entry_status in ["partial", "filled"] or tp_status in ["partial", "filled"]:
            return "active"
        else:
            return "pending"
    
    async def get_signal_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取信号统计信息"""
        try:
            db = SessionLocal()
            try:
                from datetime import timedelta
                start_date = datetime.utcnow() - timedelta(days=days)
                
                # 基础统计
                total_signals = db.query(TelegramSignal).filter(
                    TelegramSignal.signal_time >= start_date
                ).count()
                
                executed_signals = db.query(TelegramSignal).filter(
                    and_(
                        TelegramSignal.signal_time >= start_date,
                        TelegramSignal.status == SignalStatus.EXECUTED
                    )
                ).count()
                
                # 计算成功率
                success_rate = (executed_signals / total_signals * 100) if total_signals > 0 else 0
                
                # 计算平均盈亏
                from sqlalchemy import func
                avg_pnl = db.query(func.avg(TradeTakeProfitPoint.pnl)).join(TelegramSignal).filter(
                    and_(
                        TelegramSignal.signal_time >= start_date,
                        TradeTakeProfitPoint.pnl.isnot(None)
                    )
                ).scalar() or 0
                
                # 按状态分布
                status_stats = db.query(
                    TelegramSignal.status,
                    func.count(TelegramSignal.id).label('count')
                ).filter(
                    TelegramSignal.signal_time >= start_date
                ).group_by(TelegramSignal.status).all()
                
                # 按类型分布
                type_stats = db.query(
                    TelegramSignal.signal_type,
                    func.count(TelegramSignal.id).label('count')
                ).filter(
                    TelegramSignal.signal_time >= start_date
                ).group_by(TelegramSignal.signal_type).all()
                
                return {
                    'success': True,
                    'data': {
                        'total_signals': total_signals,
                        'executed_signals': executed_signals,
                        'success_rate': round(success_rate, 2),
                        'avg_pnl': round(avg_pnl, 2),
                        'status_distribution': {
                            status.value: count for status, count in status_stats
                        },
                        'type_distribution': {
                            type_.value: count for type_, count in type_stats
                        },
                        'period_days': days
                    }
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取信号统计失败: {e}")
            return {'success': False, 'message': f'获取统计失败: {str(e)}'}
    
    def set_auto_execute(self, enabled: bool):
        """设置自动执行开关"""
        self.auto_execute_enabled = enabled
        logger.info(f"信号自动执行已{'启用' if enabled else '禁用'}")
    
    def get_auto_execute_status(self) -> bool:
        """获取自动执行状态"""
        return self.auto_execute_enabled
    
    async def _get_active_parser(self):
        """获取当前活跃的信号解析器"""
        try:
            db = SessionLocal()
            try:
                # 从配置中获取解析器类型
                parser_type = config_manager.get_config("signal_parser_type", db, "builtin")
                
                if parser_type == "llm":
                    # 使用LLM解析器
                    if not self.llm_parser:
                        self.llm_parser = LLMSignalParserFactory.create_parser(db)
                    return self.llm_parser
                else:
                    # 使用内置解析器（默认）
                    return self.builtin_parser
            finally:
                db.close()
        except Exception as e:
            logger.error(f"获取解析器失败，使用默认内置解析器: {e}")
            return self.builtin_parser


# 全局信号管理器实例
signal_manager = SignalManager()