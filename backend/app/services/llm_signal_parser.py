import json
import logging
import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime
import httpx
from sqlalchemy.orm import Session

from app.models import SignalType
from app.services.config_manager import config_manager

logger = logging.getLogger(__name__)


class LLMSignalParser:
    """基于LLM的信号解析器"""
    
    def __init__(self, db: Session):
        self.db = db
        self.client = httpx.AsyncClient(timeout=30.0)
        
        # 从配置获取LLM设置
        self.api_key = config_manager.get_config("deepseek_api_key", db, "")
        self.base_url = config_manager.get_config("deepseek_base_url", db, "https://api.deepseek.com")
        self.model = config_manager.get_config("deepseek_model", db, "deepseek-chat")
        self.temperature = config_manager.get_config("llm_temperature", db, 0.1)
        self.max_tokens = config_manager.get_config("llm_max_tokens", db, 1000)
        
        # 检查配置
        if not self.api_key:
            logger.warning("LLM API密钥未配置，LLM信号解析器将无法正常工作")
    
    async def parse_message(self, message: str) -> Optional[Dict[str, Any]]:
        """使用LLM解析单个交易信号"""
        results = await self.parse_messages([message])
        return results[0] if results else None
    
    async def parse_messages(self, messages: List[str]) -> List[Optional[Dict[str, Any]]]:
        """批量解析交易信号"""
        try:
            if not self.api_key:
                logger.error("LLM API密钥未配置")
                return [None] * len(messages)
            
            if not messages:
                return []
            
            logger.info(f"[LLM_PARSER] 开始批量解析 {len(messages)} 个信号...")
            
            # 对于大批量，分批处理以避免API限制
            batch_size = 5  # 每批处理5个信号
            all_results = []
            
            for i in range(0, len(messages), batch_size):
                batch = messages[i:i + batch_size]
                logger.info(f"[LLM_PARSER] 处理批次 {i//batch_size + 1}, 信号数: {len(batch)}")
                
                # 构建批量提示词
                batch_prompt = self._build_batch_prompt(batch)
                
                # 调用LLM API
                response = await self._call_llm_api(batch_prompt)
                if not response:
                    logger.error(f"[LLM_PARSER] 批次 {i//batch_size + 1} API调用失败")
                    all_results.extend([None] * len(batch))
                    continue
                
                # 解析批量响应
                batch_results = self._parse_batch_llm_response(response, batch)
                all_results.extend(batch_results)
                
                # 简短延迟以避免API限制
                if i + batch_size < len(messages):
                    await asyncio.sleep(0.5)
            
            success_count = sum(1 for r in all_results if r is not None)
            logger.info(f"[LLM_PARSER] 批量解析完成: {success_count}/{len(messages)} 成功")
            
            return all_results
                
        except Exception as e:
            logger.error(f"[LLM_PARSER] 批量解析失败: {e}")
            return [None] * len(messages)
    
    def _build_prompt(self, message: str) -> str:
        """构建LLM提示词"""
        prompt = f"""请解析以下交易信号文本，提取关键交易信息。

交易信号文本：
{message}

请按照以下JSON格式返回解析结果，如果无法解析出有效的交易信号，请返回null：

{{
    "signal_type": "LONG|SHORT|CLOSE",
    "symbol": "交易对符号（例如：BTCUSDT）",
    "entry_price": 入场价格（数字，可选）,
    "entry_prices": [入场价格列表（数字数组，可选）],
    "stop_loss": 止损价格（数字，可选）,
    "take_profit": 主要止盈价格（数字，可选）,
    "take_profit_targets": [止盈目标列表（数字数组，可选）],
    "leverage": 杠杆倍数（数字，可选）,
    "risk_percentage": 风险百分比（数字，可选）,
    "confidence_score": 0.0-1.0（解析置信度）,
    "format_type": "llm"
}}

解析规则：
1. signal_type：识别信号类型，LONG/BUY表示做多，SHORT/SELL表示做空，CLOSE/EXIT表示平仓
2. symbol：提取交易对，统一转换为USDT格式（如BTC→BTCUSDT）
3. entry_price：单个入场价格
4. entry_prices：多个入场价格区间
5. stop_loss：止损价格
6. take_profit：主要止盈价格
7. take_profit_targets：多个止盈目标
8. leverage：杠杆倍数，支持范围（取中间值）
9. confidence_score：根据信息完整度评分

只返回JSON格式，不要包含其他文字说明。"""
        
        return prompt
    
    def _build_batch_prompt(self, messages: List[str]) -> str:
        """构建批量LLM提示词"""
        # 构建批量信号文本
        signals_text = ""
        for i, message in enumerate(messages, 1):
            signals_text += f"\n=== 信号 {i} ===\n{message}\n"
        
        prompt = f"""请解析以下多个交易信号文本，为每个信号提取关键交易信息。

{signals_text}

请按照以下JSON格式返回解析结果数组，每个信号对应一个结果对象。如果某个信号无法解析出有效的交易信号，请为该信号返回null：

[
  {{
    "signal_type": "LONG|SHORT|CLOSE",
    "symbol": "交易对符号（例如：BTCUSDT）",
    "entry_price": 入场价格（数字，可选）,
    "entry_prices": [入场价格列表（数字数组，可选）],
    "stop_loss": 止损价格（数字，可选）,
    "take_profit": 主要止盈价格（数字，可选）,
    "take_profit_targets": [止盈目标列表（数字数组，可选）],
    "leverage": 杠杆倍数（数字，可选）,
    "risk_percentage": 风险百分比（数字，可选）,
    "confidence_score": 0.0-1.0（解析置信度）,
    "format_type": "llm"
  }},
  // ... 为每个信号返回一个对象，共{len(messages)}个
]

解析规则：
1. signal_type：识别信号类型，LONG/BUY表示做多，SHORT/SELL表示做空，CLOSE/EXIT表示平仓
2. symbol：提取交易对，统一转换为USDT格式（如BTC→BTCUSDT）
3. entry_price：单个入场价格
4. entry_prices：多个入场价格区间
5. stop_loss：止损价格
6. take_profit：主要止盈价格
7. take_profit_targets：多个止盈目标
8. leverage：杠杆倍数，支持范围（取中间值）
9. confidence_score：根据信息完整度评分

返回结果数组必须包含{len(messages)}个元素，按信号顺序排列。

重要提示：请直接返回JSON数组，不要包装在其他对象中，也不要包含任何文字说明。格式示例：
[{{"signal_type":"LONG","symbol":"BTCUSDT",...}}, null, {{"signal_type":"SHORT",...}}]"""
        
        return prompt
    
    async def _call_llm_api(self, prompt: str) -> Optional[str]:
        """调用LLM API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "response_format": {"type": "json_object"}
            }
            
            url = f"{self.base_url.rstrip('/')}/v1/chat/completions"
            
            logger.debug(f"[LLM_PARSER] 调用API: {url}")
            
            response = await self.client.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                logger.debug(f"[LLM_PARSER] API响应: {content}")
                return content
            else:
                logger.error(f"[LLM_PARSER] API调用失败: {response.status_code}, {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"[LLM_PARSER] API调用异常: {e}")
            return None
    
    def _parse_llm_response(self, response: str, original_message: str) -> Optional[Dict[str, Any]]:
        """解析LLM响应"""
        try:
            # 尝试解析JSON
            data = json.loads(response.strip())
            
            # 检查是否为null（无法解析）
            if data is None:
                return None
            
            # 验证必需字段
            if not data.get("signal_type") or not data.get("symbol"):
                logger.warning(f"[LLM_PARSER] 缺少必需字段: {data}")
                return None
            
            # 转换信号类型
            signal_type_str = data.get("signal_type", "").upper()
            if signal_type_str in ["LONG", "BUY"]:
                signal_type = SignalType.LONG
            elif signal_type_str in ["SHORT", "SELL"]:
                signal_type = SignalType.SHORT
            elif signal_type_str in ["CLOSE", "EXIT"]:
                signal_type = SignalType.CLOSE
            else:
                logger.warning(f"[LLM_PARSER] 无效的信号类型: {signal_type_str}")
                return None
            
            # 标准化符号
            symbol = data.get("symbol", "").upper()
            if symbol and not symbol.endswith("USDT") and not symbol.endswith("USD"):
                symbol += "USDT"
            
            # 构建结果
            result = {
                "signal_type": signal_type,
                "symbol": symbol,
                "entry_price": data.get("entry_price"),
                "entry_prices": data.get("entry_prices", []),
                "stop_loss": data.get("stop_loss"),
                "take_profit": data.get("take_profit"),
                "take_profit_targets": data.get("take_profit_targets", []),
                "leverage": data.get("leverage"),
                "risk_percentage": data.get("risk_percentage"),
                "confidence_score": data.get("confidence_score", 0.5),
                "format_type": "llm",
                "raw_text": original_message
            }
            
            # 数据类型验证和转换
            for key in ["entry_price", "stop_loss", "take_profit", "leverage", "risk_percentage", "confidence_score"]:
                if result[key] is not None:
                    try:
                        result[key] = float(result[key])
                    except (ValueError, TypeError):
                        result[key] = None
            
            for key in ["entry_prices", "take_profit_targets"]:
                if result[key]:
                    try:
                        result[key] = [float(x) for x in result[key] if x is not None]
                    except (ValueError, TypeError):
                        result[key] = []
            
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"[LLM_PARSER] JSON解析失败: {e}, 响应: {response}")
            return None
        except Exception as e:
            logger.error(f"[LLM_PARSER] 响应解析异常: {e}")
            return None
    
    def _parse_batch_llm_response(self, response: str, original_messages: List[str]) -> List[Optional[Dict[str, Any]]]:
        """解析批量LLM响应"""
        try:
            # 尝试解析JSON
            data = json.loads(response.strip())
            
            # 检查是否是包装在对象字段中的数组
            if isinstance(data, dict):
                # 尝试多种可能的字段名
                if "results" in data:
                    data_list = data["results"]
                elif "response" in data:
                    data_list = data["response"]
                elif "data" in data:
                    data_list = data["data"]
                elif "signals" in data:
                    data_list = data["signals"]
                else:
                    # 如果是对象但没有预期字段，尝试取第一个数组值
                    array_values = [v for v in data.values() if isinstance(v, list)]
                    if array_values:
                        data_list = array_values[0]
                        logger.warning(f"[LLM_PARSER] 使用对象中的数组字段: {response[:200]}...")
                    else:
                        logger.error(f"[LLM_PARSER] 批量响应格式无法识别: {response}")
                        return [None] * len(original_messages)
            elif isinstance(data, list):
                data_list = data
            else:
                logger.error(f"[LLM_PARSER] 批量响应格式无法识别: {response}")
                return [None] * len(original_messages)
            
            if not isinstance(data_list, list):
                logger.error(f"[LLM_PARSER] 批量响应不是数组格式: {response}")
                return [None] * len(original_messages)
            
            # 确保数组长度匹配
            if len(data_list) != len(original_messages):
                logger.warning(f"[LLM_PARSER] 响应数组长度({len(data_list)})与输入({len(original_messages)})不匹配")
                # 填充或截取到正确长度
                while len(data_list) < len(original_messages):
                    data_list.append(None)
                data_list = data_list[:len(original_messages)]
            
            results = []
            for i, (data, original_message) in enumerate(zip(data_list, original_messages)):
                if data is None:
                    results.append(None)
                    continue
                
                # 使用单个解析方法处理每个结果
                try:
                    # 模拟单个响应格式
                    single_response = json.dumps(data)
                    parsed_result = self._parse_llm_response(single_response, original_message)
                    results.append(parsed_result)
                except Exception as e:
                    logger.error(f"[LLM_PARSER] 解析批量结果项{i+1}失败: {e}")
                    results.append(None)
            
            return results
            
        except json.JSONDecodeError as e:
            logger.error(f"[LLM_PARSER] 批量JSON解析失败: {e}, 响应: {response}")
            return [None] * len(original_messages)
        except Exception as e:
            logger.error(f"[LLM_PARSER] 批量响应解析异常: {e}")
            return [None] * len(original_messages)
    
    def validate_signal(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """验证信号"""
        is_valid = True
        errors = []
        
        # 检查必需字段
        if not signal.get("symbol"):
            is_valid = False
            errors.append("缺少交易对符号")
        
        if not signal.get("signal_type"):
            is_valid = False
            errors.append("缺少信号类型")
        
        # 检查价格合理性
        entry_price = signal.get("entry_price")
        stop_loss = signal.get("stop_loss")
        
        if entry_price and entry_price <= 0:
            is_valid = False
            errors.append("入场价格必须大于0")
        
        if stop_loss and stop_loss <= 0:
            is_valid = False
            errors.append("止损价格必须大于0")
        
        # 检查杠杆合理性
        leverage = signal.get("leverage")
        if leverage and (leverage < 1 or leverage > 100):
            is_valid = False
            errors.append("杠杆倍数必须在1-100之间")
        
        return {
            "is_valid": is_valid,
            "errors": errors
        }
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的信号格式"""
        return [
            "LLM-based Signal Parser",
            "Natural Language Trading Signals",
            "Multi-format Signal Recognition",
            "AI-powered Signal Analysis"
        ]


class LLMSignalParserFactory:
    """LLM信号解析器工厂"""
    
    @staticmethod
    def create_parser(db: Session) -> LLMSignalParser:
        """创建LLM信号解析器实例"""
        return LLMSignalParser(db) 