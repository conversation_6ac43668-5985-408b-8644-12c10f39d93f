import asyncio
import logging
from typing import Dict, Any

from app.services.price_monitor import price_monitor
from app.services.order_monitor import order_monitor
from app.config import settings

logger = logging.getLogger(__name__)


class TradingMonitor:
    """交易监控总控制器"""
    
    def __init__(self):
        self.running = False
        self.monitors = {
            'price_monitor': price_monitor,
            'order_monitor': order_monitor
        }
    
    async def start_all_monitors(self):
        """启动所有监控服务"""
        if not settings.auto_trading_enabled:
            logger.info("自动交易未启用，跳过监控启动")
            return
        
        self.running = True
        logger.info("🚀 启动交易监控系统")
        
        # 启动所有监控任务
        tasks = []
        
        # 价格监控
        tasks.append(asyncio.create_task(
            price_monitor.start_monitoring(),
            name="price_monitor"
        ))
        
        # 订单监控
        tasks.append(asyncio.create_task(
            order_monitor.start_monitoring(),
            name="order_monitor"
        ))
        
        # 状态报告任务
        tasks.append(asyncio.create_task(
            self._status_reporter(),
            name="status_reporter"
        ))
        
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"监控系统异常: {e}")
        finally:
            self.running = False
            logger.info("交易监控系统已停止")
    
    def stop_all_monitors(self):
        """停止所有监控服务"""
        self.running = False
        price_monitor.stop_monitoring()
        order_monitor.stop_monitoring()
        logger.info("🛑 停止交易监控系统")
    
    async def _status_reporter(self):
        """状态报告器"""
        while self.running:
            try:
                await asyncio.sleep(60)  # 每分钟报告一次状态
                
                status = await self.get_system_status()
                logger.info(f"📊 系统状态: 监控交易对 {status['monitored_symbols']} 个, "
                          f"活跃信号 {status['active_signals']} 个")
                
            except Exception as e:
                logger.error(f"状态报告异常: {e}")
                await asyncio.sleep(30)
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            from app.database import SessionLocal
            from app.models import TelegramSignal, SignalStatus
            
            db = SessionLocal()
            try:
                # 统计活跃信号
                active_signals = db.query(TelegramSignal).filter(
                    TelegramSignal.status.in_([
                        SignalStatus.PENDING,
                        SignalStatus.PROCESSING,
                        SignalStatus.EXECUTED
                    ])
                ).count()
                
                # 获取监控的交易对数量
                monitored_symbols = len(price_monitor.monitored_symbols)
                
                # 获取价格缓存状态
                price_cache_size = len(price_monitor.price_cache)
                
                return {
                    'running': self.running,
                    'auto_trading_enabled': settings.auto_trading_enabled,
                    'dry_run_mode': settings.is_dry_run_mode,
                    'active_signals': active_signals,
                    'monitored_symbols': monitored_symbols,
                    'price_cache_size': price_cache_size,
                    'monitors_status': {
                        name: monitor.running if hasattr(monitor, 'running') else True
                        for name, monitor in self.monitors.items()
                    }
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e)}
    
    def is_running(self) -> bool:
        """检查监控系统是否运行中"""
        return self.running


# 全局实例
trading_monitor = TradingMonitor()