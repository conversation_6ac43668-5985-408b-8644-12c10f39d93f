import re
import logging
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
from app.models import SignalType

logger = logging.getLogger(__name__)


class CornixSignalParser:
    """Cornix格式信号解析器"""
    
    def __init__(self):
        # Cornix特有的格式模式
        self.cornix_patterns = {
            # 典型的Cornix格式：#BTCUSDT 📈 LONG, 🚨 MOODENG/USDT Long
            'header_pattern': r'(?:#([A-Z]+(?:USDT?)?)\s*([📈📉🟢🔴]|\b(?:LONG|SHORT|BUY|SELL)\b)|🚨\s*([A-Z]{2,15})/USDT?\s+(Long|Short|Buy|Sell))',
            
            # Entry价格模式：Entry: 42000 - 42500, Optimal Entry: 0.19000
            'entry_range': r'(?:Entry|Enter|Optimal\s*Entry)[:：]?\s*([0-9,.]+)\s*[–\-]\s*([0-9,.]+)',
            'entry_single': r'(?:Entry|Enter|Optimal\s*Entry)[:：]?\s*([0-9,.]+)',
            
            # 止损模式：Stop Loss: 40000, Stop-Loss: 0.17840
            'stop_loss': r'(?:Stop[\s\-]*Loss|SL)[:：]?\s*([0-9,.]+)',
            
            # 止盈目标模式：Take Profit 1: 45000, ℹ 0.19459
            'take_profit_targets': r'(?:Take\s*Profit|TP)\s*(\d+)[:：]?\s*([0-9,.]+)',
            'take_profit_simple': r'(?:Take\s*Profit|TP)[:：]?\s*([0-9,.]+)',
            'profit_targets_list': r'ℹ\s*([0-9,.]+)',
            'profit_targets_header': r'(?:Profit\s*Targets|Take\s*Profit)',
            
            # 杠杆模式：Leverage: 10x, Leverage: 25x–75x
            'leverage': r'(?:Leverage|Lev)[:：]?\s*(\d+)(?:x?[–\-]?\d*x?)?',
            
            # 风险模式：Risk: 2%
            'risk': r'(?:Risk|R)[:：]?\s*(\d+(?:\.\d+)?)%',
            
            # 时间框架：Timeframe: 4H
            'timeframe': r'(?:Timeframe|TF)[:：]?\s*(\w+)',
        }
        
        # 编译正则表达式
        self.compiled_cornix = {}
        for key, pattern in self.cornix_patterns.items():
            self.compiled_cornix[key] = re.compile(pattern, re.IGNORECASE | re.MULTILINE)


class EnhancedSignalParser:
    """增强版信号解析器，支持多种格式"""
    
    def __init__(self):
        self.cornix_parser = CornixSignalParser()
        
        # 扩展的信号模式
        self.signal_patterns = {
            # 交易类型模式 - 增加更多识别模式
            'buy_patterns': [
                r'\b(buy|long|enter\s+long|go\s+long|买入|做多|多单|多)\b',
                r'🟢|📈|⬆️|🚀|💚|✅|🔼',
                r'LONG',
                r'BUY\s+SIGNAL',
                r'CALL:\s*BUY',
                r'Long\s+Setup',
                r'ABOVE\s+ABOVE\s+ABOVE',
            ],
            'sell_patterns': [
                r'\b(sell|short|enter\s+short|go\s+short|卖出|做空|空单|空)\b',
                r'🔴|📉|⬇️|💥|❤️|🔻|🏓',
                r'SHORT',
                r'SELL\s+SIGNAL',
                r'CALL:\s*SELL',
                r'BELOW\s+BELOW\s+BELOW',
            ],
            'close_patterns': [
                r'\b(close|exit|平仓|止盈|止损|close\s+position)\b',
                r'❌|🚫|⏹️',
                r'EXIT',
                r'CLOSE\s+POSITION',
            ],
            
            # 增强的价格模式
            'price_patterns': [
                # 标准格式
                r'(?:entry|enter|entry\s+price|entry\s+point|optimal\s*entry|价格|价位)[:：\-]?\s*\$?([0-9,]+\.?[0-9]*)',
                r'(?:price|价格)[:：]?\s*\$?([0-9,]+\.?[0-9]*)',
                r'@\s*\$?([0-9,]+\.?[0-9]*)',
                r'(\d+\.?\d*)\s*(?:usdt|usd)',
                # 区间格式
                r'([0-9,]+\.?[0-9]*)\$?\s*[–\-]\s*([0-9,]+\.?[0-9]*)\$?',
                # 目标格式
                r'Target[:：]?\s*\$?([0-9,]+\.?[0-9]*)',
                # BUY格式：1) 0.1876 - 50%
                r'\d+\)\s*([0-9.]+)\s*-\s*\d+%',
                # ENTRY格式：ENTRY :- 486.65$ - 480.5$
                r'ENTRY\s*[:：\-]+\s*([0-9,]+\.?[0-9]*)\$?\s*[–\-]\s*([0-9,]+\.?[0-9]*)\$?',
                # 括号格式：(0.4600)(0.4550)
                r'\(([0-9,]+\.?[0-9]*)\)\(([0-9,]+\.?[0-9]*)\)',
            ],
            
            # 增强的止损模式
            'stop_loss_patterns': [
                r'(?:stop[\s\-]*loss|sl|止损|stoploss)[:：\-]?\s*\$?([0-9,]+\.?[0-9]*)',
                r'(?:stop|止损)\s*@?\s*\$?([0-9,]+\.?[0-9]*)',
                r'SL[:：]?\s*([0-9,]+\.?[0-9]*)',
                r'Stop[:：]?\s*([0-9,]+\.?[0-9]*)',
                r'StopLoss[:：]?\s*([0-9,]+\.?[0-9]*)',
                r'Stop\s*loss\s*[:：\-]+\s*([0-9,]+\.?[0-9]*)\$?',
                r'STOPLOSS\s*[:：]?\s*([0-9,]+\.?[0-9]*)',
                r'Protect\s*your\s*capital.*?([0-9,]+\.?[0-9]*)',
            ],
            
            # 增强的止盈模式
            'take_profit_patterns': [
                r'(?:take\s*profit|tp|止盈|目标|target|targets)[:：\-]?\s*\$?([0-9,]+\.?[0-9]*)',
                r'(?:target|目标)\s*@?\s*\$?([0-9,]+\.?[0-9]*)',
                r'TP\s*\d*[:：]?\s*([0-9,]+\.?[0-9]*)',
                r'Take\s*Profit\s*\d*[:：]?\s*([0-9,]+\.?[0-9]*)',
                # 简化后的模式，不依赖emoji
                r'([0-9,]+\.?[0-9]*)\s*First\s*Breakout',
                r'([0-9,]+\.?[0-9]*)\s*Momentum\s*Builds',
                r'([0-9,]+\.?[0-9]*)\s*Bullish\s*Surge',
                r'([0-9,]+\.?[0-9]*)\s*Moon\s*Zone',
                r'TP\s*[:：\-]+\s*([0-9,]+\.?[0-9]*(?:\$?\s*[–\-]\s*[0-9,]+\.?[0-9]*\$?)*)',
            ],
            
            # 增强的交易对模式
            'symbol_patterns': [
                # 标准格式
                r'#([A-Z]{1,10}(?:USDT?)?)\b',
                r'\b([A-Z]{2,10}/?USDT?)\b',
                r'\b([A-Z]{1,10})/[A-Z]{2,4}\b',
                r'\b([A-Z]{2,10})\s*[：:]?\s*(?:usdt|usd)\b',
                # Binance格式
                r'([A-Z]{1,10})USDT',
                r'([A-Z]{1,10})/USDT',
                # 其他格式
                r'Pair[:：]?\s*([A-Z]{1,10}(?:USDT?)?)',
                r'Symbol[:：]?\s*([A-Z]{1,10}(?:USDT?)?)',
                r'([A-Z]{2,15})/USDT?\s+(?:Long|Short|Buy|Sell)',
                r'#([A-Z]{1,15})/USDT?',
                # 简化后的特殊格式
                r'([A-Z]{1,15})/USDT',
                r'SHORT\s+([A-Z]{1,10})\s*/\s*USDT',
                r'Coin\s+#([A-Z]{1,10})/USDT',
            ],
            
            # 杠杆模式
            'leverage_patterns': [
                r'(?:leverage|杠杆|lev)[:：]?\s*(\d+)(?:x?[–\-]?(\d+)x?)?',
                r'(\d+)x?\s*[–\-]?\s*(\d+)?x?\s*(?:leverage|杠杆)?',
                r'Leverage[:：]?\s*(\d+)',
                # LEV : 10X - 20X 格式
                r'LEV\s*[:：]?\s*(\d+)X?\s*-\s*(\d+)X?',
            ],
            
            # 数量模式
            'quantity_patterns': [
                r'(?:quantity|amount|size|数量|仓位)[:：]?\s*([0-9,]+\.?[0-9]*)',
                r'(\d+\.?\d*)\s*(?:coins?|个|张|%)',
                r'Position\s*Size[:：]?\s*([0-9,]+\.?[0-9]*)',
            ],
            
            # 风险管理模式
            'risk_patterns': [
                r'(?:risk|风险|r)[:：]?\s*(\d+(?:\.\d+)?)%',
                r'Risk\s*Management[:：]?\s*(\d+(?:\.\d+)?)%',
            ]
        }
        
        # 编译正则表达式
        self.compiled_patterns = {}
        for category, patterns in self.signal_patterns.items():
            self.compiled_patterns[category] = [
                re.compile(pattern, re.IGNORECASE | re.MULTILINE)
                for pattern in patterns
            ]
        
        # 信号状态更新消息模式 - 用于过滤汇报信号状态的消息
        self.status_update_patterns = [
            # Take-Profit 相关状态
            r'Take\s*[–\-]?\s*Profit\s+(?:target\s+)?\d+\s+(?:done|achieved|hit|reached)',
            r'TP\s*\d+\s+(?:done|achieved|hit|reached)',
            r'Target\s+\d+\s+(?:done|achieved|hit|reached)',
            r'Target\s+(?:done|achieved|hit|reached)',
            
            # Profit Secured 类型
            r'Profit\s+Secured[:：]?\s*[+\-]?\d+(?:\.\d+)?%',
            r'Profit\s+Locked\s+in',
            r'Gains?\s+(?:secured|locked)',
            
            # Entry 完成状态
            r'Entry\s+(?:completed|filled|done)',
            r'Position\s+opened',
            r'Trade\s+(?:opened|started|activated)',
            
            # 整体交易状态
            r'(?:Trade|Signal)\s+(?:completed|finished|closed)',
            r'All\s+targets?\s+(?:achieved|hit|reached)',
            r'(?:100%|Full)\s+profit\s+taken',
            
            # 止损触发
            r'Stop\s*[–\-]?\s*Loss\s+(?:hit|triggered|reached)',
            r'SL\s+(?:hit|triggered|reached)',
            
            # 常见的汇报表达式
            r'Locked\s+in\s+solid\s+gains',
            r'Patience\s*[+&]*\s*Precision\s*=\s*Victory',
            r'Take\s*[–\-]?\s*Profit\s+.*?achieved',
            r'Profit\s+target\s+\d+\s+(?:done|achieved)',
            
            # 数字盈利汇报格式
            r'[+]\d+(?:\.\d+)?%.*?(?:profit|gain|secured)',
            r'\d+(?:\.\d+)?%\s+profit\s+(?:secured|locked|taken)',
            
            # 中文状态消息
            r'止盈\s*\d*\s*(?:完成|达成|触发)',
            r'目标\s*\d*\s*(?:完成|达成)',
            r'盈利\s*(?:锁定|确保)',
            
            # 特定格式的状态更新
            r'#\w+Explodes?',
            r'#\w+Wins?',
            r'#FuturesMastery',
            r'#CryptoWins',
        ]
        
        # 编译状态更新模式
        self.compiled_status_patterns = [
            re.compile(pattern, re.IGNORECASE | re.MULTILINE)
            for pattern in self.status_update_patterns
        ]
    
    def is_status_update_message(self, message: str) -> bool:
        """检测是否为信号状态更新消息（汇报类消息）"""
        try:
            # 先检查是否包含新的交易信号指令
            has_signal_setup = bool(re.search(r'(?:setup|signal|alert|call)\s+(?:alert|signal).*?(?:entry|leverage|target|stop)', message, re.IGNORECASE | re.DOTALL))
            has_new_trade_info = bool(re.search(r'entry[:：]?\s*[0-9]|leverage[:：]?\s*[0-9]|stop\s*loss[:：]?\s*[0-9]', message, re.IGNORECASE))
            
            # 检查是否包含明确的历史/结果关键词（表示这是状态汇报）
            has_result_keywords = bool(re.search(r'(?:result|previous|achieved|done|secured|locked|completed)', message, re.IGNORECASE))
            
            # 如果包含新的交易设置信息，且不是历史结果汇报，不应该被过滤
            if has_new_trade_info and not has_result_keywords:
                logger.debug(f"消息包含新的交易信号设置，不应过滤")
                return False
            
            # 检查消息是否匹配状态更新模式
            for pattern in self.compiled_status_patterns:
                if pattern.search(message):
                    logger.debug(f"检测到状态更新消息，匹配模式: {pattern.pattern}")
                    return True
            
            # 额外检查：如果消息包含多个状态关键词但没有新的交易信息，也认为是状态更新
            status_keywords = [
                'secured', 'locked', 'achieved', 'done', 'hit', 'reached', 
                'completed', 'finished', 'opened', 'activated', 'triggered',
                'profit', 'gain'
            ]
            
            # 纯状态关键词（明确的状态更新指标）
            pure_status_keywords = [
                'profit secured', 'take-profit.*done', 'target.*achieved', 
                'locked in', 'gains secured', 'position opened'
            ]
            
            message_lower = message.lower()
            status_count = sum(1 for keyword in status_keywords if keyword in message_lower)
            pure_status_count = sum(1 for keyword in pure_status_keywords if re.search(keyword, message_lower))
            
            # 如果有明确的纯状态更新关键词，且没有新的交易信息，认为是状态更新
            if pure_status_count >= 1:
                logger.debug(f"检测到纯状态更新关键词")
                return True
            
            # 如果状态关键词很多，且没有明确的价格信息，可能是状态更新
            if status_count >= 3:
                # 检查是否有新的入场价格或交易指令
                has_price_info = bool(re.search(r'entry[:：]?\s*[0-9]|price[:：]?\s*[0-9]|@\s*[0-9]', message_lower))
                if not has_price_info:
                    logger.debug(f"检测到可能的状态更新消息：状态词{status_count}个")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检测状态更新消息时出错: {e}")
            return False
    
    async def parse_message(self, message: str) -> Optional[Dict[str, Any]]:
        """解析单个消息，提取交易信号"""
        results = await self.parse_messages([message])
        return results[0] if results else None
    
    async def parse_messages(self, messages: List[str]) -> List[Optional[Dict[str, Any]]]:
        """批量解析消息，提取交易信号"""
        results = []
        for message in messages:
            try:
                # 首先检查是否为信号状态更新消息，如果是则直接返回None
                if self.is_status_update_message(message):
                    logger.info(f"跳过信号状态更新消息: {message[:100]}...")
                    results.append(None)
                    continue
                
                # 先尝试用原始消息解析（保留emoji）
                general_result = await self._parse_general_format(message)
                if general_result:
                    logger.info(f"Parsed general signal: {general_result['symbol']} {general_result['signal_type']}")
                    results.append(general_result)
                    continue
                
                # 清理消息文本后再尝试
                cleaned_message = self._clean_message(message)
                
                # 尝试Cornix格式解析
                cornix_result = await self._parse_cornix_format(cleaned_message)
                if cornix_result:
                    logger.info(f"Parsed Cornix signal: {cornix_result['symbol']} {cornix_result['signal_type']}")
                    results.append(cornix_result)
                    continue
                
                # 通用格式解析（清理后的消息）
                general_result_cleaned = await self._parse_general_format(cleaned_message)
                if general_result_cleaned:
                    logger.info(f"Parsed general signal: {general_result_cleaned['symbol']} {general_result_cleaned['signal_type']}")
                    results.append(general_result_cleaned)
                    continue
                
                # 无法解析
                results.append(None)
                
            except Exception as e:
                logger.error(f"Error parsing message: {e}")
                results.append(None)
        
        return results
    
    async def _parse_cornix_format(self, message: str) -> Optional[Dict[str, Any]]:
        """解析Cornix格式信号"""
        try:
            # 检查是否为Cornix格式
            header_match = self.cornix_parser.compiled_cornix['header_pattern'].search(message)
            if not header_match:
                return None
            
            # 处理两种格式：标准格式和🚨格式
            if header_match.group(1):  # 标准格式：#BTCUSDT LONG
                symbol = header_match.group(1)
                direction_indicator = header_match.group(2)
            else:  # 🚨格式：🚨 MOODENG/USDT Long
                symbol = header_match.group(3)
                direction_indicator = header_match.group(4)
            
            # 标准化符号
            if not symbol.endswith('USDT') and not symbol.endswith('USD'):
                symbol += 'USDT'
            
            # 确定信号类型
            if any(word in direction_indicator.upper() for word in ['LONG', 'BUY']) or direction_indicator in ['📈', '🟢']:
                signal_type = SignalType.LONG
            elif any(word in direction_indicator.upper() for word in ['SHORT', 'SELL']) or direction_indicator in ['📉', '🔴']:
                signal_type = SignalType.SHORT
            else:
                return None
            
            # 提取入场价格
            entry_prices = self._extract_cornix_entry_prices(message)
            
            # 提取止损
            stop_loss = self._extract_cornix_stop_loss(message)
            
            # 提取止盈目标
            take_profit_targets = self._extract_cornix_take_profits(message)
            
            # 提取杠杆
            leverage = self._extract_cornix_leverage(message)
            
            # 提取风险
            risk_percentage = self._extract_cornix_risk(message)
            
            result = {
                'signal_type': signal_type,
                'symbol': symbol,
                'entry_prices': entry_prices,
                'stop_loss': stop_loss,
                'take_profit_targets': take_profit_targets,
                'leverage': leverage,
                'risk_percentage': risk_percentage,
                'raw_text': message,
                'format_type': 'cornix'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing Cornix format: {e}")
            return None
    
    async def _parse_general_format(self, message: str) -> Optional[Dict[str, Any]]:
        """解析通用格式信号"""
        try:
            # 先分离多个信号（如果存在）
            signals = self._split_multiple_signals(message)
            if len(signals) > 1:
                # 只解析第一个信号
                message = signals[0]
            
            # 先提取交易对（避免被LONG/SHORT干扰）
            symbol = self._extract_symbol(message)
            if not symbol:
                return None
            
            # 提取信号类型
            signal_type = self._extract_signal_type(message)
            if not signal_type:
                return None
            
            # 提取价格信息
            entry_prices = self._extract_entry_prices(message)
            stop_loss = self._extract_stop_loss(message)
            take_profit_targets = self._extract_multiple_take_profits(message)
            
            # 提取其他信息
            leverage = self._extract_leverage(message)
            quantity = self._extract_quantity(message)
            risk_percentage = self._extract_risk(message)
            
            # 统一字段名称
            main_entry_price = entry_prices[0] if entry_prices else None
            main_take_profit = take_profit_targets[0] if take_profit_targets else None
            
            result = {
                'signal_type': signal_type,
                'symbol': symbol,
                'entry_price': main_entry_price,
                'entry_prices': entry_prices,
                'stop_loss': stop_loss,
                'take_profit': main_take_profit,
                'take_profit_targets': take_profit_targets,
                'leverage': leverage,
                'quantity': quantity,
                'risk_percentage': risk_percentage,
                'raw_text': message,
                'format_type': 'general'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing general format: {e}")
            return None
    
    def _split_multiple_signals(self, message: str) -> List[str]:
        """分离多个信号"""
        # 检查是否包含多个信号的标识
        if '--' in message:
            # 按--分割，只取第一个信号
            parts = message.split('--')
            if len(parts) > 1:
                first_signal = parts[0].strip()
                # 确保第一个信号包含完整的交易信息
                if len(first_signal) > 50:
                    return [first_signal]
        
        # 按其他分隔符分离信号
        separators = ['\n\n---\n\n', '---']
        signals = [message]
        
        for sep in separators:
            new_signals = []
            for signal in signals:
                parts = signal.split(sep)
                new_signals.extend(parts)
            signals = new_signals
        
        # 过滤太短的片段
        return [s.strip() for s in signals if len(s.strip()) > 30]
    
    def _extract_cornix_entry_prices(self, message: str) -> List[float]:
        """提取Cornix格式的入场价格"""
        prices = []
        
        # 尝试区间格式
        range_match = self.cornix_parser.compiled_cornix['entry_range'].search(message)
        if range_match:
            low = float(range_match.group(1).replace(',', ''))
            high = float(range_match.group(2).replace(',', ''))
            prices.extend([low, high])
        
        # 尝试单一价格格式
        single_match = self.cornix_parser.compiled_cornix['entry_single'].search(message)
        if single_match and not prices:
            prices.append(float(single_match.group(1).replace(',', '')))
        
        return sorted(list(set(prices)))
    
    def _extract_cornix_stop_loss(self, message: str) -> Optional[float]:
        """提取Cornix格式的止损价格"""
        match = self.cornix_parser.compiled_cornix['stop_loss'].search(message)
        if match:
            return float(match.group(1).replace(',', ''))
        return None
    
    def _extract_cornix_take_profits(self, message: str) -> List[float]:
        """提取Cornix格式的多个止盈目标"""
        targets = []
        
        # 提取编号的目标：TP1: 45000, TP2: 46000
        for match in self.cornix_parser.compiled_cornix['take_profit_targets'].finditer(message):
            try:
                price = float(match.group(2).replace(',', ''))
                targets.append(price)
            except ValueError:
                continue
        
        # 提取 ℹ 0.19459 格式的目标
        if not targets:
            for match in self.cornix_parser.compiled_cornix['profit_targets_list'].finditer(message):
                try:
                    price = float(match.group(1).replace(',', ''))
                    targets.append(price)
                except ValueError:
                    continue
        
        # 如果没有列表格式目标，尝试简单格式
        if not targets:
            match = self.cornix_parser.compiled_cornix['take_profit_simple'].search(message)
            if match:
                try:
                    price = float(match.group(1).replace(',', ''))
                    targets.append(price)
                except ValueError:
                    pass
        
        return sorted(targets)
    
    def _extract_cornix_leverage(self, message: str) -> int:
        """提取Cornix格式的杠杆"""
        match = self.cornix_parser.compiled_cornix['leverage'].search(message)
        if match:
            return int(match.group(1))
        return 1
    
    def _extract_cornix_risk(self, message: str) -> Optional[float]:
        """提取Cornix格式的风险百分比"""
        match = self.cornix_parser.compiled_cornix['risk'].search(message)
        if match:
            return float(match.group(1))
        return None
    
    def _calculate_cornix_confidence(
        self, message: str, symbol: str, entry_price: Optional[float], 
        stop_loss: Optional[float], take_profit_targets: List[float]
    ) -> float:
        """计算Cornix格式信号的置信度"""
        confidence = 0.7  # Cornix格式基础置信度较高
        
        # 有完整价格信息
        if entry_price and stop_loss and take_profit_targets:
            confidence += 0.2
        elif entry_price and (stop_loss or take_profit_targets):
            confidence += 0.15
        elif entry_price:
            confidence += 0.1
        
        # 多个止盈目标
        if len(take_profit_targets) > 1:
            confidence += 0.05
        
        # 包含杠杆信息
        if 'leverage' in message.lower() or 'lev' in message.lower():
            confidence += 0.03
        
        # 包含风险管理
        if 'risk' in message.lower():
            confidence += 0.02
        
        return min(confidence, 1.0)
    
    # 保持原有的通用解析方法
    def _clean_message(self, message: str) -> str:
        """清理消息文本，移除emoji表情"""
        import re
        
        # 移除所有emoji表情，保留文本和数字信息
        # Unicode emoji 范围
        emoji_pattern = re.compile(
            r'[\U0001F600-\U0001F64F]|'  # 表情
            r'[\U0001F300-\U0001F5FF]|'  # 符号和象形文字
            r'[\U0001F680-\U0001F6FF]|'  # 交通和地图
            r'[\U0001F1E0-\U0001F1FF]|'  # 国旗
            r'[\U00002600-\U000026FF]|'  # 杂项符号
            r'[\U00002700-\U000027BF]|'  # 装饰符号
            r'[\U0001F900-\U0001F9FF]|'  # 补充符号
            r'[\U0001FA70-\U0001FAFF]',  # 扩展A
            flags=re.UNICODE
        )
        
        # 移除emoji
        cleaned = emoji_pattern.sub(' ', message)
        
        # 保留基本字符、数字、标点符号
        cleaned = re.sub(r'[^\w\s\.\$@#/:：，,\+\-\*\(\)–]', ' ', cleaned)
        
        # 合并多个空格
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        return cleaned.strip()
    
    def _extract_signal_type(self, message: str) -> Optional[SignalType]:
        """提取信号类型"""
        # 优先检查明确的文本标识（避免emoji干扰）
        if re.search(r'\b(?:SHORT|SELL)\b', message, re.IGNORECASE):
            return SignalType.SHORT
        if re.search(r'\b(?:LONG|BUY)\b', message, re.IGNORECASE):
            return SignalType.LONG
        if re.search(r'Position:\s*SHORT', message, re.IGNORECASE):
            return SignalType.SHORT
        if re.search(r'Position:\s*LONG', message, re.IGNORECASE):
            return SignalType.LONG
        
        # 检查emoji和其他模式
        for pattern in self.compiled_patterns['sell_patterns']:
            if pattern.search(message):
                return SignalType.SHORT
        
        for pattern in self.compiled_patterns['buy_patterns']:
            if pattern.search(message):
                return SignalType.LONG
        
        # 检查平仓信号
        for pattern in self.compiled_patterns['close_patterns']:
            if pattern.search(message):
                return SignalType.CLOSE
        
        return None
    
    def _extract_symbol(self, message: str) -> Optional[str]:
        """提取交易对符号"""
        import re
        
        # 优先匹配 #ALGO/USDT 格式
        algo_pattern = re.compile(r'#([A-Z]{2,10})/USDT', re.IGNORECASE)
        match = algo_pattern.search(message)
        if match:
            symbol = match.group(1).upper()
            if symbol not in ['LONG', 'SHORT', 'BUY', 'SELL']:
                return symbol + 'USDT'
        
        # 其他模式
        for pattern in self.compiled_patterns['symbol_patterns']:
            match = pattern.search(message)
            if match:
                symbol = match.group(1).upper()
                if symbol in ['LONG', 'SHORT', 'BUY', 'SELL']:
                    continue
                if '/' in symbol:
                    symbol = symbol.replace('/', '')
                if not symbol.endswith('USDT') and not symbol.endswith('USD'):
                    symbol += 'USDT'
                return symbol
        
        return None
    

    
    def _extract_entry_prices(self, message: str) -> List[float]:
        """提取所有入场价格"""
        prices = []
        
        # 1. 处理ABOVE/BELOW特殊关键词
        above_pattern = re.compile(r'Entry\s+Point\s+ABOVE\s+ABOVE\s+ABOVE\s*[-:]?\s*([0-9.]+)', re.IGNORECASE)
        match = above_pattern.search(message)
        if match:
            try:
                price = float(match.group(1))
                prices.append(price)
                return prices
            except ValueError:
                pass
        
        below_pattern = re.compile(r'Entry\s+Point\s+BELOW\s+BELOW\s+BELOW\s*[-:]?\s*([0-9.]+)', re.IGNORECASE)
        match = below_pattern.search(message)
        if match:
            try:
                price = float(match.group(1))
                prices.append(price)
                return prices
            except ValueError:
                pass
        
        # 2. 处理括号格式：(0.4600)(0.4550)
        bracket_pattern = re.compile(r'\(([0-9.]+)\)\(([0-9.]+)\)')
        match = bracket_pattern.search(message)
        if match:
            try:
                price1 = float(match.group(1))
                price2 = float(match.group(2))
                prices.extend([price1, price2])
                return sorted(prices)
            except ValueError:
                pass
        
        # 3. 处理ENTRY范围格式：ENTRY :- 486.65$ - 480.5$
        entry_range_pattern = re.compile(r'ENTRY\s*[:：\-]+\s*([0-9.]+)\$?\s*[-–]\s*([0-9.]+)\$?', re.IGNORECASE)
        match = entry_range_pattern.search(message)
        if match:
            try:
                price1 = float(match.group(1))
                price2 = float(match.group(2))
                prices.extend([price1, price2])
                return sorted(prices)
            except ValueError:
                pass
        
        # 4. 处理Entries范围格式：Entries: 0.0139 - 0.0142
        entries_range_pattern = re.compile(r'Entries?\s*[:：]?\s*([0-9.]+)\s*[-–]\s*([0-9.]+)', re.IGNORECASE)
        match = entries_range_pattern.search(message)
        if match:
            try:
                price1 = float(match.group(1))
                price2 = float(match.group(2))
                prices.extend([price1, price2])
                return sorted(prices)
            except ValueError:
                pass
        
        # 5. 处理Buy范围格式：Buy: 0.003957 - 0.004070
        buy_range_pattern = re.compile(r'Buy\s*[:：]?\s*([0-9.]+)\s*[-–]\s*([0-9.]+)', re.IGNORECASE)
        match = buy_range_pattern.search(message)
        if match:
            try:
                price1 = float(match.group(1))
                price2 = float(match.group(2))
                prices.extend([price1, price2])
                return sorted(prices)
            except ValueError:
                pass
        
        # 6. 处理中文范围格式：1.9055 - 1.8755 多
        chinese_range_pattern = re.compile(r'([0-9.]+)\s*[-–]\s*([0-9.]+)\s*多', re.IGNORECASE)
        match = chinese_range_pattern.search(message)
        if match:
            try:
                price1 = float(match.group(1))
                price2 = float(match.group(2))
                prices.extend([price1, price2])
                return sorted(prices)
            except ValueError:
                pass
        
        # 7. 处理单一Entry Point格式
        entry_single_pattern = re.compile(r'Entry\s*Point\s*[:：]?\s*([0-9.]+)', re.IGNORECASE)
        match = entry_single_pattern.search(message)
        if match:
            try:
                price = float(match.group(1))
                prices.append(price)
                return prices
            except ValueError:
                pass
        
        # 8. 原有的通用模式
        for pattern in self.compiled_patterns['price_patterns']:
            match = pattern.search(message)
            if match:
                try:
                    if len(match.groups()) > 1 and match.group(2):
                        low = float(match.group(1).replace(',', ''))
                        high = float(match.group(2).replace(',', ''))
                        prices.extend([low, high])
                    else:
                        price_str = match.group(1).replace(',', '')
                        prices.append(float(price_str))
                except (ValueError, IndexError):
                    continue
        
        return sorted(list(set(prices)))
    
    def _extract_entry_price_range(self, message: str) -> List[float]:
        """提取入场价格区间（返回多个价格）"""
        prices = []
        
        # 首先尝试CRYPTO MONK格式的BUY部分：1) 0.1876 - 50%
        import re
        buy_pattern = re.compile(r'BUY\s*:.*?(?=Take\s*Profit|🟥|$)', re.IGNORECASE | re.DOTALL)
        buy_section = buy_pattern.search(message)
        if buy_section:
            buy_text = buy_section.group(0)
            # 提取所有的价格：1) 0.1876 - 50%
            entry_pattern = re.compile(r'\d+\)\s*([0-9.]+)\s*-\s*\d+%')
            for match in entry_pattern.finditer(buy_text):
                try:
                    price = float(match.group(1))
                    prices.append(price)
                except ValueError:
                    continue
        
        # 如果没有找到CRYPTO MONK格式，查找区间格式：42000 - 42500
        if not prices:
            for pattern in self.compiled_patterns['price_patterns']:
                match = pattern.search(message)
                if match and len(match.groups()) > 1 and match.group(2):
                    try:
                        low = float(match.group(1).replace(',', ''))
                        high = float(match.group(2).replace(',', ''))
                        # 只返回原始的两个价格点
                        prices.extend([low, high])
                        break
                    except (ValueError, IndexError):
                        continue
        
        return sorted(list(set(prices)))  # 去重并排序
    
    def _extract_multiple_take_profits(self, message: str) -> List[float]:
        """提取多个止盈目标"""
        targets = []
        
        # 先分离信号，避免混合
        if '--' in message:
            first_signal = message.split('--')[0]
            message = first_signal
        
        # 1. TP编号格式：TP1: 1.9458, TP2: 1.9725
        tp_pattern = re.compile(r'TP\d+:\s*([0-9.]+)', re.IGNORECASE)
        for match in tp_pattern.finditer(message):
            try:
                price = float(match.group(1))
                targets.append(price)
            except ValueError:
                continue
        
        # 2. Target列表格式：Target: 0.003922 - 0.003883 - 0.003845 - 0.003807 - 0.003769 - 0.003732
        if not targets:
            target_pattern = re.compile(r'Target\s*[:：]\s*([0-9.\s\-]+)', re.IGNORECASE)
            match = target_pattern.search(message)
            if match:
                price_str = match.group(1)
                # 提取所有数字
                for price_part in re.findall(r'([0-9.]+)', price_str):
                    try:
                        price = float(price_part)
                        targets.append(price)
                    except ValueError:
                        continue
        
        # 3. TP范围格式：TP :- 474$ - 469.5$ - 461$ - 453$ - 444$
        if not targets:
            tp_range_pattern = re.compile(r'TP\s*[:：\-]+\s*([0-9.$\s\-]+)', re.IGNORECASE)
            match = tp_range_pattern.search(message)
            if match:
                price_str = match.group(1)
                for price_part in re.findall(r'([0-9.]+)', price_str):
                    try:
                        price = float(price_part)
                        targets.append(price)
                    except ValueError:
                        continue
        
        # 4. Targets范围格式：Targets: 3130 - 3145 - 3160 - 3175
        if not targets:
            targets_range_pattern = re.compile(r'Targets?\s*[:：]?\s*([0-9\s\-]+)', re.IGNORECASE)
            match = targets_range_pattern.search(message)
            if match:
                price_str = match.group(1)
                for price_part in re.findall(r'([0-9]+)', price_str):
                    try:
                        price = float(price_part)
                        targets.append(price)
                    except ValueError:
                        continue
        
        # 4.5. 特殊处理：如果前面的模式都没有找到，尝试奖牌格式（作为备用）
        if not targets:
            medal_pattern = re.compile(r'[🥇🥈🥉]\s*([0-9.]+)', re.IGNORECASE)
            for match in medal_pattern.finditer(message):
                try:
                    price = float(match.group(1))
                    targets.append(price)
                except ValueError:
                    continue
        
        # 5. Targets逗号分隔格式：Targets: 🎯 0.0137, 0.0135, 0.0132, 0.0129, 0.0126
        if not targets:
            # 特殊处理ALPHA信号：Targets: 🎯 0.0137, 0.0135, 0.0132, 0.0129, 0.0126
            alpha_targets_pattern = re.compile(r'Targets?\s*[:：]?\s*🎯\s*([0-9.,\s]+)', re.IGNORECASE)
            match = alpha_targets_pattern.search(message)
            if match:
                price_str = match.group(1)
                # 提取所有数字，包括逗号分隔的
                for price_part in re.findall(r'([0-9.]+)', price_str):
                    try:
                        price = float(price_part)
                        targets.append(price)
                    except ValueError:
                        continue
            
            # 如果没有emoji格式，尝试普通格式
            if not targets:
                comma_targets_pattern = re.compile(r'Targets?\s*[:：]?[^0-9]*([0-9.,\s]+)', re.IGNORECASE)
                match = comma_targets_pattern.search(message)
                if match:
                    price_str = match.group(1)
                    for price_part in re.findall(r'([0-9.]+)', price_str):
                        try:
                            price = float(price_part)
                            targets.append(price)
                        except ValueError:
                            continue
        
        # 6. 奖牌emoji格式和TARGETS关键词处理
        if not targets:
            # 先尝试奖牌格式（更直接的方法）
            medal_pattern = re.compile(r'[🥇🥈🥉]\s*([0-9.]+)', re.IGNORECASE)
            for match in medal_pattern.finditer(message):
                try:
                    price = float(match.group(1))
                    targets.append(price)
                except ValueError:
                    continue
            
            # 如果没有奖牌格式，检查TARGETS关键词
            if not targets and 'TARGETS' in message.upper():
                # 提取TARGETS后面的所有数字
                targets_section = re.search(r'TARGETS.*?(?=STOPLOSS|$)', message, re.IGNORECASE | re.DOTALL)
                if targets_section:
                    targets_text = targets_section.group(0)
                    # 提取所有数字，但要过滤不合理的值
                    for price_match in re.finditer(r'([0-9.]+)', targets_text):
                        try:
                            price = float(price_match.group(1))
                            # 过滤明显不是价格的数字（如年份、百分比等）
                            if 0.001 <= price <= 100000:  # 合理的价格范围
                                targets.append(price)
                        except ValueError:
                            continue
        
        # 7. 特殊描述格式：0.04500– First Breakout
        if not targets:
            special_desc_patterns = [
                r'([0-9.]+)\s*–?\s*First\s*Breakout',
                r'([0-9.]+)\s*–?\s*Momentum\s*Builds',
                r'([0-9.]+)\s*–?\s*Bullish\s*Surge',
                r'([0-9.]+)\s*–?\s*Moon\s*Zone'
            ]
            for pattern_str in special_desc_patterns:
                pattern = re.compile(pattern_str, re.IGNORECASE)
                match = pattern.search(message)
                if match:
                    try:
                        price = float(match.group(1))
                        targets.append(price)
                    except ValueError:
                        continue
        
        return sorted(list(set(targets)))  # 去重并排序
    
    def _extract_stop_loss(self, message: str) -> Optional[float]:
        """提取止损价格"""
        # 先检查是否有多个信号混合，只处理第一个信号的止损
        if '--' in message:
            first_signal = message.split('--')[0]
            message = first_signal
        
        # 特殊处理SL格式：SL: 1.8400
        sl_pattern = re.compile(r'SL\s*[:：]?\s*([0-9.]+)', re.IGNORECASE)
        match = sl_pattern.search(message)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                pass
        
        for pattern in self.compiled_patterns['stop_loss_patterns']:
            match = pattern.search(message)
            if match:
                try:
                    price_str = match.group(1).replace(',', '')
                    return float(price_str)
                except (ValueError, IndexError):
                    continue
        
        return None
    
    def _extract_take_profit(self, message: str) -> Optional[float]:
        """提取止盈价格（返回第一个找到的目标）"""
        # 先检查是否有多个 ℹ 格式的目标
        import re
        info_pattern = re.compile(r'ℹ\s*([0-9,]+\.?[0-9]*)', re.IGNORECASE)
        info_matches = info_pattern.findall(message)
        if info_matches:
            try:
                # 返回第一个目标
                return float(info_matches[0].replace(',', ''))
            except ValueError:
                pass
        
        # 尝试其他模式
        for pattern in self.compiled_patterns['take_profit_patterns']:
            match = pattern.search(message)
            if match:
                try:
                    price_str = match.group(1).replace(',', '')
                    return float(price_str)
                except (ValueError, IndexError):
                    continue
        
        return None
    
    def _extract_leverage(self, message: str) -> int:
        """提取杠杆倍数"""
        # 先分离信号，避免混合
        if '--' in message:
            first_signal = message.split('--')[0]
            message = first_signal
        
        # 优先处理明确的"Leverage - 10x"格式
        leverage_dash_pattern = re.compile(r'Leverage\s*-\s*(\d+)x?', re.IGNORECASE)
        match = leverage_dash_pattern.search(message)
        if match:
            try:
                leverage_val = int(match.group(1))
                if 1 <= leverage_val <= 200:
                    return leverage_val
            except ValueError:
                pass
        
        # 处理范围格式：25x – 75x, 20X TO 75X, 5X - 15X
        range_patterns = [
            r'(?:leverage|杠杆)[:：]?\s*(\d+)x?\s*[–\-到TO]\s*(\d+)x?',
            r'(\d+)x?\s*[–\-到TO]\s*(\d+)x?\s*(?:leverage|杠杆)',
            r'with\s+(\d+)X\s*-\s*(\d+)X\s*leverage',
            r'LEVERAGE\s*[:：]?\s*(\d+)X\s+TO\s+(\d+)X'
        ]
        
        for pattern_str in range_patterns:
            pattern = re.compile(pattern_str, re.IGNORECASE)
            match = pattern.search(message)
            if match:
                try:
                    low = int(match.group(1))
                    high = int(match.group(2))
                    return (low + high) // 2  # 取中间值
                except (ValueError, IndexError):
                    continue
        
        # 处理其他单一杠杆格式
        single_patterns = [
            r'Cross(\d+)X',  # Cross25X
            r'Leverage[:：]?\s*(\d+)x?(?!\s*[–\-到TO])',  # Leverage: 20x
        ]
        
        for pattern_str in single_patterns:
            pattern = re.compile(pattern_str, re.IGNORECASE)
            match = pattern.search(message)
            if match:
                try:
                    leverage_val = int(match.group(1))
                    # 避免提取到价格数字（通常杠杆在1-200之间）
                    if 1 <= leverage_val <= 200:
                        return leverage_val
                except (ValueError, IndexError):
                    continue
        
        # 特殊处理：直接在行尾的x格式（如"10x"）
        line_end_x_pattern = re.compile(r'\b(\d+)x\s*$', re.IGNORECASE | re.MULTILINE)
        matches = line_end_x_pattern.findall(message)
        for match in matches:
            try:
                leverage_val = int(match)
                if 1 <= leverage_val <= 200:
                    return leverage_val
            except ValueError:
                continue
        
        # 最后尝试原有模式（但要过滤价格数字）
        for pattern in self.compiled_patterns['leverage_patterns']:
            match = pattern.search(message)
            if match:
                try:
                    leverage_val = int(match.group(1))
                    if 1 <= leverage_val <= 200:
                        return leverage_val
                except (ValueError, IndexError):
                    continue
        
        return 1
    
    def _extract_quantity(self, message: str) -> Optional[float]:
        """提取交易数量"""
        for pattern in self.compiled_patterns['quantity_patterns']:
            match = pattern.search(message)
            if match:
                try:
                    quantity_str = match.group(1).replace(',', '')
                    return float(quantity_str)
                except (ValueError, IndexError):
                    continue
        
        return None
    
    def _extract_risk(self, message: str) -> Optional[float]:
        """提取风险百分比"""
        for pattern in self.compiled_patterns['risk_patterns']:
            match = pattern.search(message)
            if match:
                try:
                    return float(match.group(1))
                except (ValueError, IndexError):
                    continue
        
        return None
    
    def _calculate_confidence(
        self,
        message: str,
        signal_type: SignalType,
        symbol: str,
        entry_price: Optional[float]
    ) -> float:
        """计算信号置信度"""
        confidence = 0.5  # 基础置信度
        
        # 有完整的价格信息
        if entry_price:
            confidence += 0.2
        
        # 包含止损信息
        if any(pattern.search(message) for pattern in self.compiled_patterns['stop_loss_patterns']):
            confidence += 0.15
        
        # 包含止盈信息
        if any(pattern.search(message) for pattern in self.compiled_patterns['take_profit_patterns']):
            confidence += 0.15
        
        # 交易对格式标准
        if symbol and (symbol.endswith('USDT') or symbol.endswith('USD')):
            confidence += 0.05
        
        # 包含杠杆信息
        if any(pattern.search(message) for pattern in self.compiled_patterns['leverage_patterns']):
            confidence += 0.03
        
        # 包含数量信息
        if any(pattern.search(message) for pattern in self.compiled_patterns['quantity_patterns']):
            confidence += 0.02
        
        return min(confidence, 1.0)
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的信号格式"""
        return [
            "Cornix Format",
            "Standard Trading Signals",
            "Custom Telegram Signals",
            "Multi-language Signals (EN/CN)"
        ]
    
    def validate_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证解析的信号数据"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 必需字段检查
        required_fields = ['signal_type', 'symbol']
        for field in required_fields:
            if not signal_data.get(field):
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"Missing required field: {field}")
        
        # 价格合理性检查
        entry_price = signal_data.get('entry_price')
        stop_loss = signal_data.get('stop_loss')
        take_profit = signal_data.get('take_profit')
        
        if entry_price and entry_price <= 0:
            validation_result['is_valid'] = False
            validation_result['errors'].append("Entry price must be positive")
        
        if stop_loss and stop_loss <= 0:
            validation_result['is_valid'] = False
            validation_result['errors'].append("Stop loss must be positive")
        
        if take_profit and take_profit <= 0:
            validation_result['is_valid'] = False
            validation_result['errors'].append("Take profit must be positive")
        
        # 逻辑性检查
        if entry_price and stop_loss and take_profit:
            signal_type = signal_data.get('signal_type')
            if signal_type == SignalType.LONG:
                if stop_loss >= entry_price:
                    validation_result['warnings'].append("Stop loss should be below entry price for LONG")
                if take_profit <= entry_price:
                    validation_result['warnings'].append("Take profit should be above entry price for LONG")
            elif signal_type == SignalType.SHORT:
                if stop_loss <= entry_price:
                    validation_result['warnings'].append("Stop loss should be above entry price for SHORT")
                if take_profit >= entry_price:
                    validation_result['warnings'].append("Take profit should be below entry price for SHORT")
        
        # 置信度检查
        confidence = signal_data.get('confidence_score', 0)
        if confidence < 0.3:
            validation_result['warnings'].append("Low confidence signal")
        
        return validation_result


# 向后兼容
SignalParser = EnhancedSignalParser 