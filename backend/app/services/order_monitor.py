import asyncio
import logging
from typing import List
from sqlalchemy.orm import Session
from datetime import datetime

from app.database import SessionLocal
from app.models import TradeEntryPoint, TradeTakeProfitPoint, SignalTrade
from app.services.exchange_client import exchange_client
from app.services.cornix_signal_executor import cornix_executor

logger = logging.getLogger(__name__)


class OrderMonitor:
    """订单状态监控器"""
    
    def __init__(self):
        self.running = False
    
    async def start_monitoring(self):
        """启动订单监控"""
        self.running = True
        logger.info("订单监控已启动")
        
        while self.running:
            try:
                await self._check_pending_orders()
                await asyncio.sleep(30)  # 每30秒检查一次（降低频率）
            except Exception as e:
                logger.error(f"订单监控异常: {e}")
                await asyncio.sleep(60)
    
    def stop_monitoring(self):
        """停止订单监控"""
        self.running = False
        logger.info("订单监控已停止")
    
    async def _check_pending_orders(self):
        """检查待处理订单（只检查submitted状态的订单）"""
        db = SessionLocal()
        try:
            # 只检查已提交的订单状态
            await self._check_entry_orders(db)
            await self._check_take_profit_orders(db)
            
        finally:
            db.close()
    
    async def _check_entry_orders(self, db: Session):
        """检查入场订单状态"""
        pending_entries = db.query(TradeEntryPoint).filter(
            TradeEntryPoint.status == "submitted"
        ).all()
        
        for entry in pending_entries:
            if not entry.order_id:
                continue
                
            # 查询订单状态
            order_status = await exchange_client.get_order_status(
                entry.order_id, 
                entry.signal.symbol
            )
            
            if order_status.get('status') == 'filled':
                # 订单成交
                entry.status = "filled"
                entry.filled_quantity = order_status.get('filled', 0)
                entry.actual_price = order_status.get('price', entry.price)
                entry.filled_at = datetime.utcnow()
                
                logger.info(f"入场订单成交: {entry.signal.symbol} @ {entry.actual_price}")
                
                # 激活对应的止盈点
                await self._activate_take_profits(entry.signal, db)
                
            elif order_status.get('status') == 'cancelled':
                entry.status = "cancelled"
                logger.info(f"入场订单取消: {entry.signal.symbol}")
        
        db.commit()
    
    async def _check_take_profit_orders(self, db: Session):
        """检查止盈订单状态"""
        pending_tps = db.query(TradeTakeProfitPoint).filter(
            TradeTakeProfitPoint.status == "submitted"
        ).all()
        
        for tp in pending_tps:
            if not tp.order_id:
                continue
                
            order_status = await exchange_client.get_order_status(
                tp.order_id,
                tp.signal.symbol
            )
            
            if order_status.get('status') == 'filled':
                # 止盈成交
                tp.status = "filled"
                tp.filled_quantity = order_status.get('filled', 0)
                tp.actual_price = order_status.get('price', tp.price)
                tp.filled_at = datetime.utcnow()
                
                # 计算盈亏
                entry_price = tp.signal.entry_price
                if tp.signal.signal_type.value == 'buy':
                    tp.pnl = (tp.actual_price - entry_price) * tp.filled_quantity
                else:
                    tp.pnl = (entry_price - tp.actual_price) * tp.filled_quantity
                
                tp.pnl_percentage = (tp.pnl / (entry_price * tp.filled_quantity)) * 100
                
                logger.info(f"止盈成交: {tp.signal.symbol} @ {tp.actual_price}, 盈亏: {tp.pnl:.2f}")
                
            elif order_status.get('status') == 'cancelled':
                tp.status = "cancelled"
        
        db.commit()
    
    async def _activate_take_profits(self, signal, db: Session):
        """激活止盈点"""
        try:
            for tp_point in signal.take_profit_points:
                if tp_point.status == "waiting_for_entry":
                    # 计算止盈数量
                    quantity = self._calculate_tp_quantity(tp_point)
                    
                    # 创建止盈订单
                    opposite_side = 'sell' if signal.signal_type.value == 'buy' else 'buy'
                    order_id = await exchange_client.create_order(
                        symbol=signal.symbol,
                        side=opposite_side,
                        amount=quantity,
                        price=tp_point.price
                    )
                    
                    if order_id:
                        tp_point.status = "submitted"
                        tp_point.order_id = order_id
                        logger.info(f"止盈单已提交: {signal.symbol} @ {tp_point.price}")
            
            db.commit()
            
        except Exception as e:
            logger.error(f"激活止盈点失败: {e}")
    
    def _calculate_tp_quantity(self, tp_point: TradeTakeProfitPoint) -> float:
        """计算止盈数量"""
        # 简化实现：基于分配比例
        base_quantity = 100  # 基础数量
        return base_quantity * tp_point.allocation / 100


# 全局实例
order_monitor = OrderMonitor()