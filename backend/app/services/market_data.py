import asyncio
import aiohttp
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import ccxt.async_support as ccxt
from sqlalchemy.orm import Session
from sqlalchemy import text
import json

from app.config import get_settings
from app.database import SessionLocal

settings = get_settings()
logger = logging.getLogger(__name__)


class MarketDataService:
    """增强的市场数据服务 - 支持TimescaleDB K线数据存储"""
    
    def __init__(self):
        self.exchange = None
        self._init_exchange()
    
    def _init_exchange(self):
        """初始化交易所连接"""
        try:
            # 使用币安作为数据源
            self.exchange = ccxt.binance({
                'apiKey': getattr(settings, 'BINANCE_API_KEY', ''),
                'secret': getattr(settings, 'BINANCE_SECRET_KEY', ''),
                'sandbox': False,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot'  # 现货交易
                }
            })
            logger.info("交易所连接初始化成功")
        except Exception as e:
            logger.warning(f"初始化交易所失败: {e}")
            self.exchange = None

    async def get_klines(
        self, 
        symbol: str, 
        interval: str = '5m',  # 改为5分钟默认
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 1000,
        force_download: bool = False,
        exchange: str = 'binance'
    ) -> pd.DataFrame:
        """获取K线数据 - 优先从TimescaleDB获取，缺失数据自动下载"""
        try:
            logger.info(f"获取K线数据: {symbol} {interval} {start_time} ~ {end_time} ({exchange})")
            
            # 如果没有指定时间范围，获取最近数据
            if not start_time:
                start_time = datetime.utcnow() - timedelta(days=30)
            if not end_time:
                end_time = datetime.utcnow()
            
            # 检查TimescaleDB中是否有数据
            db_data = await self._get_klines_from_db(symbol, interval, start_time, end_time, exchange)
            
            if not db_data.empty and not force_download:
                # 检查数据完整性
                missing_ranges = await self._check_data_completeness(symbol, interval, start_time, end_time, db_data)
                
                if missing_ranges:
                    logger.info(f"检测到缺失数据，需要下载 {len(missing_ranges)} 个时间段")
                    # 下载缺失的数据
                    for start_missing, end_missing in missing_ranges:
                        missing_data = await self._download_klines_from_exchange(
                            symbol, interval, start_missing, end_missing
                        )
                        if not missing_data.empty:
                            await self._save_klines_to_db(missing_data, symbol, interval, exchange)
                    
                    # 重新从数据库获取完整数据
                    db_data = await self._get_klines_from_db(symbol, interval, start_time, end_time, exchange)
                
                if not db_data.empty:
                    logger.info(f"从TimescaleDB获取到 {len(db_data)} 条K线数据 ({exchange})")
                    return db_data
            
            # 如果数据库中没有数据或强制下载，从交易所获取
            logger.info(f"从交易所下载K线数据: {symbol} {interval} ({exchange})")
            exchange_data = await self._download_klines_from_exchange(symbol, interval, start_time, end_time, limit)
            
            if not exchange_data.empty:
                # 保存到数据库
                await self._save_klines_to_db(exchange_data, symbol, interval, exchange)
                logger.info(f"成功下载并保存 {len(exchange_data)} 条K线数据 ({exchange})")
                return exchange_data
            else:
                logger.warning(f"无法获取K线数据: {symbol} ({exchange})")
                return await self._get_mock_klines(symbol, interval, start_time, end_time)
            
        except Exception as e:
            logger.error(f"获取K线数据失败: {e}")
            return await self._get_mock_klines(symbol, interval, start_time, end_time)

    async def _get_klines_from_db(
        self, 
        symbol: str, 
        interval: str, 
        start_time: datetime, 
        end_time: datetime,
        exchange: str = 'binance'
    ) -> pd.DataFrame:
        """从TimescaleDB获取K线数据"""
        db = SessionLocal()
        try:
            # 首先检查exchange字段是否存在
            check_column_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'kline_data' AND column_name = 'exchange'
            """)
            
            column_result = db.execute(check_column_query)
            has_exchange_column = len(column_result.fetchall()) > 0
            
            if has_exchange_column:
                # 如果有exchange字段，包含在查询中
                query = text("""
                    SELECT 
                        open_time as timestamp,
                        open_price as open,
                        high_price as high,
                        low_price as low,
                        close_price as close,
                        volume,
                        exchange
                    FROM kline_data 
                    WHERE symbol = :symbol 
                    AND interval = :interval
                    AND open_time >= :start_time 
                    AND open_time <= :end_time
                    AND (exchange = :exchange OR exchange IS NULL)
                    ORDER BY open_time
                """)
                
                result = db.execute(query, {
                    'symbol': symbol,
                    'interval': interval,
                    'start_time': start_time,
                    'end_time': end_time,
                    'exchange': exchange
                })
                
                rows = result.fetchall()
                if rows:
                    df = pd.DataFrame(rows, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'exchange'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    return df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]  # 只返回需要的列
            else:
                # 如果没有exchange字段，使用原始查询
                query = text("""
                    SELECT 
                        open_time as timestamp,
                        open_price as open,
                        high_price as high,
                        low_price as low,
                        close_price as close,
                        volume
                    FROM kline_data 
                    WHERE symbol = :symbol 
                    AND interval = :interval
                    AND open_time >= :start_time 
                    AND open_time <= :end_time
                    ORDER BY open_time
                """)
                
                result = db.execute(query, {
                    'symbol': symbol,
                    'interval': interval,
                    'start_time': start_time,
                    'end_time': end_time
                })
                
                rows = result.fetchall()
                if rows:
                    df = pd.DataFrame(rows, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    return df
            
            return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"从数据库获取K线数据失败: {e}")
            return pd.DataFrame()
        finally:
            db.close()

    async def _download_klines_from_exchange(
        self, 
        symbol: str, 
        interval: str, 
        start_time: datetime, 
        end_time: datetime,
        limit: int = 1000
    ) -> pd.DataFrame:
        """从交易所下载K线数据"""
        try:
            if not self.exchange:
                logger.warning("交易所连接不可用，无法下载K线数据")
                return pd.DataFrame()
            
            # 转换时间格式
            since = int(start_time.timestamp() * 1000)
            until = int(end_time.timestamp() * 1000)
            
            all_klines = []
            current_since = since
            request_count = 0
            max_requests = 50  # 限制最大请求次数，避免无限循环
            
            logger.info(f"开始下载 {symbol} 的K线数据，时间范围: {start_time} ~ {end_time}")
            
            # 分批下载数据（每次最多1000条）
            while current_since < until and request_count < max_requests:
                try:
                    # 添加超时控制
                    request_start = datetime.utcnow()
                    
                    ohlcv = await asyncio.wait_for(
                        self.exchange.fetch_ohlcv(
                            symbol=symbol,
                            timeframe=interval,
                            since=current_since,
                            limit=limit
                        ),
                        timeout=30.0  # 30秒超时
                    )
                    
                    request_duration = (datetime.utcnow() - request_start).total_seconds()
                    logger.debug(f"请求 {symbol} 数据耗时: {request_duration:.2f}s")
                    
                    if not ohlcv:
                        logger.info(f"没有更多 {symbol} 的K线数据")
                        break
                    
                    all_klines.extend(ohlcv)
                    
                    # 更新起始时间为最后一条数据的时间
                    current_since = ohlcv[-1][0] + 1
                    request_count += 1
                    
                    # 避免请求过于频繁
                    await asyncio.sleep(0.2)
                    
                except asyncio.TimeoutError:
                    logger.error(f"下载 {symbol} K线数据超时")
                    break
                except Exception as e:
                    error_msg = str(e).lower()
                    if 'invalid symbol' in error_msg or 'symbol not found' in error_msg:
                        logger.warning(f"交易对 {symbol} 在交易所不存在")
                        break
                    elif 'rate limit' in error_msg:
                        logger.warning(f"请求频率限制，等待后重试: {symbol}")
                        await asyncio.sleep(2)
                        continue
                    else:
                        logger.error(f"下载K线数据批次失败: {symbol} - {e}")
                        break
            
            if all_klines:
                # 转换为DataFrame
                df = pd.DataFrame(all_klines, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                
                # 过滤时间范围
                df = df[(df['timestamp'] >= start_time) & (df['timestamp'] <= end_time)]
                
                # 去重并排序
                df = df.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
                
                logger.info(f"成功下载 {symbol} 的 {len(df)} 条K线数据")
                return df
            else:
                logger.warning(f"未获取到 {symbol} 的K线数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"从交易所下载K线数据失败: {symbol} - {e}")
            return pd.DataFrame()
        finally:
            # 不在这里关闭exchange，因为可能会被重复使用
            pass

    async def _save_klines_to_db(self, df: pd.DataFrame, symbol: str, interval: str, exchange: str = 'binance') -> bool:
        """批量保存K线数据到TimescaleDB"""
        if df.empty:
            return False
            
        logger.info(f"开始保存 {len(df)} 条K线数据到数据库: {symbol} {interval} ({exchange})")
        
        # 首先检查exchange字段是否存在
        db = SessionLocal()
        try:
            check_column_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'kline_data' AND column_name = 'exchange'
            """)
            
            column_result = db.execute(check_column_query)
            has_exchange_column = len(column_result.fetchall()) > 0
        except Exception as e:
            logger.warning(f"检查exchange字段失败: {e}")
            has_exchange_column = False
        finally:
            db.close()
        
        # 根据是否有exchange字段使用不同的SQL
        if has_exchange_column:
            insert_sql = text("""
                INSERT INTO kline_data (
                    symbol, interval, open_time, close_time,
                    open_price, high_price, low_price, close_price,
                    volume, quote_volume, trades_count, exchange
                ) VALUES (
                    :symbol, :interval, :open_time, :close_time,
                    :open_price, :high_price, :low_price, :close_price,
                    :volume, :quote_volume, :trades_count, :exchange
                ) ON CONFLICT (symbol, interval, open_time) DO UPDATE SET
                    close_time = EXCLUDED.close_time,
                    open_price = EXCLUDED.open_price,
                    high_price = EXCLUDED.high_price,
                    low_price = EXCLUDED.low_price,
                    close_price = EXCLUDED.close_price,
                    volume = EXCLUDED.volume,
                    quote_volume = EXCLUDED.quote_volume,
                    trades_count = EXCLUDED.trades_count,
                    exchange = EXCLUDED.exchange
            """)
        else:
            insert_sql = text("""
                INSERT INTO kline_data (
                    symbol, interval, open_time, close_time,
                    open_price, high_price, low_price, close_price,
                    volume, quote_volume, trades_count
                ) VALUES (
                    :symbol, :interval, :open_time, :close_time,
                    :open_price, :high_price, :low_price, :close_price,
                    :volume, :quote_volume, :trades_count
                ) ON CONFLICT (symbol, interval, open_time) DO UPDATE SET
                    close_time = EXCLUDED.close_time,
                    open_price = EXCLUDED.open_price,
                    high_price = EXCLUDED.high_price,
                    low_price = EXCLUDED.low_price,
                    close_price = EXCLUDED.close_price,
                    volume = EXCLUDED.volume,
                    quote_volume = EXCLUDED.quote_volume,
                    trades_count = EXCLUDED.trades_count
            """)
        
        insert_count = 0
        for index, row in df.iterrows():
            db = SessionLocal()  # 为每条记录创建新的数据库连接
            try:
                # 计算close_time
                close_time = self._calculate_close_time(row['timestamp'], interval)
                
                # 准备参数
                params = {
                    'symbol': symbol,
                    'interval': interval,
                    'open_time': row['timestamp'],
                    'close_time': close_time,
                    'open_price': float(row['open']),
                    'high_price': float(row['high']),
                    'low_price': float(row['low']),
                    'close_price': float(row['close']),
                    'volume': float(row['volume']),
                    'quote_volume': 0.0,  # 默认值
                    'trades_count': 0     # 默认值
                }
                
                # 如果有exchange字段，添加到参数中
                if has_exchange_column:
                    params['exchange'] = exchange
                
                # 执行插入
                db.execute(insert_sql, params)
                db.commit()
                insert_count += 1
                
            except Exception as e:
                db.rollback()
                logger.debug(f"插入单条K线数据失败，跳过: {row['timestamp']} - {e}")
                continue
            finally:
                db.close()
        
        logger.info(f"成功保存 {insert_count} 条K线数据到数据库 ({exchange})")
        return insert_count > 0

    async def _check_data_completeness(
        self, 
        symbol: str, 
        interval: str, 
        start_time: datetime, 
        end_time: datetime,
        existing_data: pd.DataFrame
    ) -> List[Tuple[datetime, datetime]]:
        """检查数据完整性，返回缺失的时间范围"""
        try:
            # 如果没有现有数据，返回整个时间范围
            if existing_data.empty:
                return [(start_time, end_time)]
            
            # 简化逻辑：检查数据的时间范围覆盖
            existing_data['timestamp'] = pd.to_datetime(existing_data['timestamp'])
            existing_min = existing_data['timestamp'].min()
            existing_max = existing_data['timestamp'].max()
            
            missing_ranges = []
            
            # 检查开始时间之前的缺失
            if start_time < existing_min:
                missing_ranges.append((start_time, existing_min))
            
            # 检查结束时间之后的缺失
            if end_time > existing_max:
                missing_ranges.append((existing_max, end_time))
            
            # 简化：不检查中间的gap，避免复杂的时间比较
            logger.info(f"数据完整性检查: 需要下载 {len(missing_ranges)} 个时间段")
            return missing_ranges
            
        except Exception as e:
            logger.error(f"检查数据完整性失败: {e}")
            # 如果检查失败，返回空列表，使用现有数据
            return []

    def _calculate_close_time(self, open_time: datetime, interval: str) -> datetime:
        """根据开盘时间和周期计算收盘时间"""
        interval_map = {
            '1m': timedelta(minutes=1),
            '5m': timedelta(minutes=5),
            '15m': timedelta(minutes=15),
            '1h': timedelta(hours=1),
            '4h': timedelta(hours=4),
            '1d': timedelta(days=1)
        }
        
        time_delta = interval_map.get(interval, timedelta(minutes=5))  # 默认改为5分钟
        return open_time + time_delta - timedelta(milliseconds=1)

    async def get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            if not self.exchange:
                await self._init_exchange()
            
            if self.exchange:
                ticker = await self.exchange.fetch_ticker(symbol)
                return float(ticker['last'])
            else:
                # 从数据库获取最新价格
                return await self._get_latest_price_from_db(symbol)
                
        except Exception as e:
            logger.error(f"获取当前价格失败: {e}")
            return await self._get_latest_price_from_db(symbol)

    async def _get_latest_price_from_db(self, symbol: str) -> Optional[float]:
        """从数据库获取最新价格"""
        db = SessionLocal()
        try:
            query = text("""
                SELECT close_price 
                FROM kline_data 
                WHERE symbol = :symbol 
                ORDER BY open_time DESC 
                LIMIT 1
            """)
            
            result = db.execute(query, {'symbol': symbol})
            row = result.fetchone()
            
            if row:
                return float(row[0])
            else:
                return None
                
        except Exception as e:
            logger.error(f"从数据库获取最新价格失败: {e}")
            return None
        finally:
            db.close()

    async def batch_download_klines(
        self, 
        symbols: List[str], 
        interval: str = '5m',  # 改为5分钟默认
        days_back: int = 30
    ) -> Dict[str, bool]:
        """批量下载多个交易对的K线数据"""
        results = {}
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days_back)
        
        for symbol in symbols:
            try:
                logger.info(f"开始下载 {symbol} 的K线数据")
                df = await self.get_klines(symbol, interval, start_time, end_time, force_download=True)
                results[symbol] = not df.empty
                
                # 避免请求过于频繁
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"下载 {symbol} K线数据失败: {e}")
                results[symbol] = False
        
        return results

    async def _get_mock_klines(
        self, 
        symbol: str, 
        interval: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> pd.DataFrame:
        """生成模拟K线数据"""
        if not start_time:
            start_time = datetime.utcnow() - timedelta(days=30)
        if not end_time:
            end_time = datetime.utcnow()
        
        # 根据间隔确定时间步长
        interval_map = {
            '1m': timedelta(minutes=1),
            '5m': timedelta(minutes=5),
            '15m': timedelta(minutes=15),
            '1h': timedelta(hours=1),
            '4h': timedelta(hours=4),
            '1d': timedelta(days=1)
        }
        
        step = interval_map.get(interval, timedelta(minutes=5))  # 默认改为5分钟
        
        # 生成时间序列
        timestamps = []
        current_time = start_time
        while current_time <= end_time:
            timestamps.append(current_time)
            current_time += step
        
        # 生成模拟价格数据
        import numpy as np
        
        # 基础价格（根据交易对设置）
        base_prices = {
            'BTCUSDT': 45000,
            'ETHUSDT': 2500,
            'ADAUSDT': 0.5,
            'BNBUSDT': 300,
            'SOLUSDT': 100
        }
        
        base_price = base_prices.get(symbol, 1000)
        
        # 生成随机价格走势
        np.random.seed(42)  # 固定种子以获得可重复的结果
        returns = np.random.normal(0, 0.02, len(timestamps))  # 2%的日波动率
        
        prices = [base_price]
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, base_price * 0.5))  # 防止价格过低
        
        # 生成OHLCV数据
        data = []
        for i, (timestamp, close_price) in enumerate(zip(timestamps, prices)):
            # 生成开高低价
            if i == 0:
                open_price = base_price
            else:
                open_price = prices[i-1]
            
            # 高低价在开盘价和收盘价基础上随机波动
            high_low_range = abs(close_price - open_price) + open_price * 0.01
            high_price = max(open_price, close_price) + np.random.uniform(0, high_low_range * 0.5)
            low_price = min(open_price, close_price) - np.random.uniform(0, high_low_range * 0.5)
            
            # 确保价格逻辑正确
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            # 生成成交量
            volume = np.random.uniform(1000, 10000)
            
            data.append({
                'timestamp': timestamp,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
        
        return pd.DataFrame(data)

    async def cleanup_old_data(self, days_to_keep: int = 365):
        """清理过期的K线数据"""
        db = SessionLocal()
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            
            query = text("""
                DELETE FROM kline_data 
                WHERE open_time < :cutoff_date
            """)
            
            result = db.execute(query, {'cutoff_date': cutoff_date})
            deleted_count = result.rowcount
            
            db.commit()
            logger.info(f"清理了 {deleted_count} 条过期K线数据")
            
        except Exception as e:
            db.rollback()
            logger.error(f"清理过期数据失败: {e}")
        finally:
            db.close()

    async def get_data_statistics(self) -> Dict[str, Any]:
        """获取K线数据统计信息"""
        db = SessionLocal()
        try:
            query = text("""
                SELECT 
                    symbol,
                    interval,
                    COUNT(*) as record_count,
                    MIN(open_time) as earliest_time,
                    MAX(open_time) as latest_time
                FROM kline_data 
                GROUP BY symbol, interval
                ORDER BY symbol, interval
            """)
            
            result = db.execute(query)
            rows = result.fetchall()
            
            statistics = {
                'total_symbols': len(set(row[0] for row in rows)),
                'total_records': sum(row[2] for row in rows),
                'by_symbol': {}
            }
            
            for row in rows:
                symbol, interval, count, earliest, latest = row
                if symbol not in statistics['by_symbol']:
                    statistics['by_symbol'][symbol] = {}
                
                statistics['by_symbol'][symbol][interval] = {
                    'record_count': count,
                    'earliest_time': earliest.isoformat() if earliest else None,
                    'latest_time': latest.isoformat() if latest else None
                }
            
            return statistics
            
        except Exception as e:
            logger.error(f"获取数据统计失败: {e}")
            return {}
        finally:
            db.close()


# 创建全局实例
market_data_service = MarketDataService()