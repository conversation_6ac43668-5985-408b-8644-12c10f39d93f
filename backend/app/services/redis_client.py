import redis.asyncio as redis
import logging
from typing import Optional, Any
import json

from app.config import settings

logger = logging.getLogger(__name__)


class RedisClient:
    """Redis异步客户端"""
    
    def __init__(self):
        self.redis = None
        self._initialize()
    
    def _initialize(self):
        """初始化Redis连接"""
        try:
            self.redis = redis.from_url(
                settings.redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            logger.info("Redis客户端初始化成功")
        except Exception as e:
            logger.error(f"Redis客户端初始化失败: {e}")
    
    async def get(self, key: str) -> Optional[str]:
        """获取值"""
        try:
            if not self.redis:
                return None
            return await self.redis.get(key)
        except Exception as e:
            logger.error(f"Redis GET失败 {key}: {e}")
            return None
    
    async def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """设置值"""
        try:
            if not self.redis:
                return False
            await self.redis.set(key, value, ex=ex)
            return True
        except Exception as e:
            logger.error(f"Redis SET失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除键"""
        try:
            if not self.redis:
                return False
            await self.redis.delete(key)
            return True
        except Exception as e:
            logger.error(f"Redis DELETE失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            if not self.redis:
                return False
            return await self.redis.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis EXISTS失败 {key}: {e}")
            return False
    
    async def close(self):
        """关闭连接"""
        if self.redis:
            await self.redis.close()


# 全局实例
redis_client = RedisClient()