import asyncio
import ccxt
import logging
import json
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.database import SessionLocal
from app.models import TelegramSignal, TradeEntryPoint, TradeTakeProfitPoint, SignalStatus
from app.services.exchange_client import exchange_client
from app.services.redis_client import redis_client

logger = logging.getLogger(__name__)


class PriceMonitor:
    """价格监控器 - 获取实时价格并触发交易逻辑"""
    
    def __init__(self):
        self.running = False
        self.price_cache = {}  # 价格缓存
        self.exchange = None
        self.monitored_symbols = set()
        self.trade_cache = {}  # 交易数据缓存
        self._initialize_exchange()
    
    def _initialize_exchange(self):
        """初始化交易所连接（仅用于获取价格）"""
        try:
            # 即使是dry run模式，也需要获取真实价格
            self.exchange = ccxt.binance({
                'enableRateLimit': True,
                'sandbox': False,  # 使用真实价格数据
            })
            logger.info("价格监控器初始化成功")
        except Exception as e:
            logger.error(f"价格监控器初始化失败: {e}")
    
    async def start_monitoring(self):
        """启动价格监控"""
        if not self.exchange:
            logger.error("交易所未初始化，无法启动价格监控")
            return
        
        self.running = True
        logger.info("价格监控已启动")
        
        # 启动多个监控任务
        tasks = [
            asyncio.create_task(self._price_update_loop()),
            asyncio.create_task(self._cache_sync_loop()),
            asyncio.create_task(self._signal_check_loop()),
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"价格监控异常: {e}")
        finally:
            self.running = False
    
    def stop_monitoring(self):
        """停止价格监控"""
        self.running = False
        logger.info("价格监控已停止")
    
    async def _price_update_loop(self):
        """价格更新循环"""
        while self.running:
            try:
                await self._update_prices()
                await asyncio.sleep(5)  # 每5秒更新一次价格
            except Exception as e:
                logger.error(f"价格更新异常: {e}")
                await asyncio.sleep(10)
    
    async def _cache_sync_loop(self):
        """缓存同步循环 - 每5分钟同步一次活跃交易到缓存"""
        while self.running:
            try:
                await self._sync_active_trades_to_cache()
                await asyncio.sleep(300)  # 每5分钟同步一次
            except Exception as e:
                logger.error(f"缓存同步异常: {e}")
                await asyncio.sleep(60)
    
    async def _signal_check_loop(self):
        """信号检查循环 - 基于缓存数据检查"""
        while self.running:
            try:
                await self._check_signal_triggers_from_cache()
                await asyncio.sleep(10)  # 每10秒检查一次信号触发
            except Exception as e:
                logger.error(f"信号检查异常: {e}")
                await asyncio.sleep(15)
    
    async def _update_prices(self):
        """更新价格数据"""
        if not self.monitored_symbols:
            return
        
        try:
            # 批量获取价格
            symbols_list = list(self.monitored_symbols)
            tickers = self.exchange.fetch_tickers(symbols_list)
            
            for symbol, ticker in tickers.items():
                self.price_cache[symbol] = {
                    'price': ticker['last'],
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'timestamp': datetime.utcnow(),
                    'volume': ticker['baseVolume']
                }
            
            logger.debug(f"价格更新完成，监控 {len(symbols_list)} 个交易对")
            
        except Exception as e:
            logger.error(f"获取价格失败: {e}")
    

    
    def _convert_to_ccxt_symbol(self, symbol: str) -> Optional[str]:
        """转换交易对格式"""
        # BTCUSDT -> BTC/USDT
        if symbol.endswith('USDT'):
            base = symbol[:-4]
            return f"{base}/USDT"
        elif symbol.endswith('BTC'):
            base = symbol[:-3]
            return f"{base}/BTC"
        elif symbol.endswith('ETH'):
            base = symbol[:-3]
            return f"{base}/ETH"
        return None
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        ccxt_symbol = self._convert_to_ccxt_symbol(symbol)
        if ccxt_symbol and ccxt_symbol in self.price_cache:
            price_data = self.price_cache[ccxt_symbol]
            # 检查价格是否过期（超过30秒）
            if datetime.utcnow() - price_data['timestamp'] < timedelta(seconds=30):
                return price_data['price']
        return None
    
    async def _sync_active_trades_to_cache(self):
        """同步活跃交易到Redis缓存"""
        db = SessionLocal()
        try:
            # 获取待入场的交易
            pending_entries = db.query(TradeEntryPoint).join(TelegramSignal).filter(
                TradeEntryPoint.status == "pending",
                TelegramSignal.status == SignalStatus.EXECUTED
            ).all()
            
            # 获取等待入场的止盈点
            waiting_tps = db.query(TradeTakeProfitPoint).join(TelegramSignal).filter(
                TradeTakeProfitPoint.status == "waiting_for_entry",
                TelegramSignal.status == SignalStatus.EXECUTED
            ).all()
            
            # 获取有止损的活跃信号
            stop_loss_signals = db.query(TelegramSignal).filter(
                TelegramSignal.status == SignalStatus.EXECUTED,
                TelegramSignal.stop_loss.isnot(None)
            ).all()
            
            # 缓存数据
            cache_data = {
                'pending_entries': [{
                    'id': entry.id,
                    'signal_id': entry.signal_id,
                    'symbol': entry.signal.symbol,
                    'price': entry.price,
                    'signal_type': entry.signal.signal_type.value
                } for entry in pending_entries],
                'waiting_tps': [{
                    'id': tp.id,
                    'signal_id': tp.signal_id,
                    'symbol': tp.signal.symbol,
                    'price': tp.price,
                    'signal_type': tp.signal.signal_type.value
                } for tp in waiting_tps],
                'stop_loss_signals': [{
                    'id': signal.id,
                    'symbol': signal.symbol,
                    'stop_loss': signal.stop_loss,
                    'signal_type': signal.signal_type.value
                } for signal in stop_loss_signals],
                'last_sync': datetime.utcnow().isoformat()
            }
            
            # 存储到Redis
            await redis_client.set('active_trades_cache', json.dumps(cache_data, default=str), ex=600)
            
            # 更新监控的交易对
            symbols = set()
            for entry in pending_entries:
                if entry.signal.symbol:
                    ccxt_symbol = self._convert_to_ccxt_symbol(entry.signal.symbol)
                    if ccxt_symbol:
                        symbols.add(ccxt_symbol)
            
            for tp in waiting_tps:
                if tp.signal.symbol:
                    ccxt_symbol = self._convert_to_ccxt_symbol(tp.signal.symbol)
                    if ccxt_symbol:
                        symbols.add(ccxt_symbol)
            
            for signal in stop_loss_signals:
                if signal.symbol:
                    ccxt_symbol = self._convert_to_ccxt_symbol(signal.symbol)
                    if ccxt_symbol:
                        symbols.add(ccxt_symbol)
            
            self.monitored_symbols = symbols
            logger.info(f"缓存同步完成: {len(pending_entries)} 待入场, {len(waiting_tps)} 待止盈, {len(stop_loss_signals)} 止损监控")
            
        finally:
            db.close()
    
    async def _check_signal_triggers_from_cache(self):
        """基于缓存数据检查信号触发条件"""
        try:
            # 从Redis获取缓存数据
            cache_data_str = await redis_client.get('active_trades_cache')
            if not cache_data_str:
                return
            
            cache_data = json.loads(cache_data_str)
            
            # 检查待入场的信号
            await self._check_entry_triggers_from_cache(cache_data.get('pending_entries', []))
            
            # 检查止盈触发
            await self._check_take_profit_triggers_from_cache(cache_data.get('waiting_tps', []))
            
            # 检查止损触发
            await self._check_stop_loss_triggers_from_cache(cache_data.get('stop_loss_signals', []))
            
        except Exception as e:
            logger.error(f"基于缓存检查信号触发失败: {e}")
    
    async def _check_entry_triggers_from_cache(self, pending_entries: List[Dict]):
        """基于缓存检查入场触发"""
        for entry_data in pending_entries:
            current_price = self.get_current_price(entry_data['symbol'])
            if not current_price:
                continue
            
            should_trigger = False
            
            if entry_data['signal_type'].lower() == 'long':
                should_trigger = current_price <= entry_data['price']
            else:
                should_trigger = current_price >= entry_data['price']
            
            if should_trigger:
                await self._trigger_entry_order_by_id(entry_data['id'], current_price)
    
    async def _check_take_profit_triggers_from_cache(self, waiting_tps: List[Dict]):
        """基于缓存检查止盈触发"""
        for tp_data in waiting_tps:
            # 检查是否有对应的入场成交
            filled_count = await self._check_filled_entries_count(tp_data['signal_id'])
            if filled_count == 0:
                continue
            
            current_price = self.get_current_price(tp_data['symbol'])
            if not current_price:
                continue
            
            should_trigger = False
            
            if tp_data['signal_type'].lower() == 'long':
                should_trigger = current_price >= tp_data['price']
            else:
                should_trigger = current_price <= tp_data['price']
            
            if should_trigger:
                await self._trigger_take_profit_order_by_id(tp_data['id'], current_price)
    
    async def _check_stop_loss_triggers_from_cache(self, stop_loss_signals: List[Dict]):
        """基于缓存检查止损触发"""
        for signal_data in stop_loss_signals:
            # 检查是否有持仓
            filled_count = await self._check_filled_entries_count(signal_data['id'])
            if filled_count == 0:
                continue
            
            current_price = self.get_current_price(signal_data['symbol'])
            if not current_price:
                continue
            
            should_trigger = False
            
            if signal_data['signal_type'].lower() == 'long':
                should_trigger = current_price <= signal_data['stop_loss']
            else:
                should_trigger = current_price >= signal_data['stop_loss']
            
            if should_trigger:
                await self._trigger_stop_loss_order_by_id(signal_data['id'], current_price)
    
    async def _check_filled_entries_count(self, signal_id: int) -> int:
        """检查已成交的入场订单数量"""
        db = SessionLocal()
        try:
            return db.query(TradeEntryPoint).filter(
                TradeEntryPoint.signal_id == signal_id,
                TradeEntryPoint.status == "filled"
            ).count()
        finally:
            db.close()
    
    async def _trigger_entry_order_by_id(self, entry_id: int, current_price: float):
        """通过ID触发入场订单"""
        db = SessionLocal()
        try:
            entry = db.query(TradeEntryPoint).filter(TradeEntryPoint.id == entry_id).first()
            if entry:
                await self._trigger_entry_order(entry, current_price, db)
        finally:
            db.close()
    
    async def _trigger_take_profit_order_by_id(self, tp_id: int, current_price: float):
        """通过ID触发止盈订单"""
        db = SessionLocal()
        try:
            tp = db.query(TradeTakeProfitPoint).filter(TradeTakeProfitPoint.id == tp_id).first()
            if tp:
                await self._trigger_take_profit_order(tp, current_price, db)
        finally:
            db.close()
    
    async def _trigger_stop_loss_order_by_id(self, signal_id: int, current_price: float):
        """通过ID触发止损订单"""
        db = SessionLocal()
        try:
            signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
            if signal:
                await self._trigger_stop_loss_order(signal, current_price, db)
        finally:
            db.close()
    
    async def _trigger_entry_order(self, entry: TradeEntryPoint, current_price: float, db: Session):
        """触发入场订单"""
        try:
            # 计算交易数量
            stake_amount = 100.0  # 默认交易金额
            quantity = stake_amount / current_price
            
            # 创建订单
            order_id = await exchange_client.create_order(
                symbol=entry.signal.symbol,
                side=entry.signal.signal_type.value.lower(),
                amount=quantity,
                price=current_price
            )
            
            if order_id:
                entry.status = "filled"  # 模拟交易立即成交
                entry.order_id = order_id
                entry.actual_price = current_price
                entry.filled_quantity = quantity
                entry.filled_at = datetime.utcnow()
                
                # 激活止盈点
                for tp in entry.signal.take_profit_points:
                    if tp.status == "pending":
                        tp.status = "waiting_for_entry"
                
                db.commit()
                
                logger.info(f"[TRIGGER] 入场订单触发: {entry.signal.symbol} @ {current_price}")
                
        except Exception as e:
            logger.error(f"触发入场订单失败: {e}")
    
    async def _trigger_take_profit_order(self, tp: TradeTakeProfitPoint, current_price: float, db: Session):
        """触发止盈订单"""
        try:
            # 计算止盈数量（简化）
            quantity = 50.0 / current_price  # 简化计算
            
            opposite_side = 'sell' if tp.signal.signal_type.value.lower() == 'long' else 'buy'
            
            order_id = await exchange_client.create_order(
                symbol=tp.signal.symbol,
                side=opposite_side,
                amount=quantity,
                price=current_price
            )
            
            if order_id:
                tp.status = "filled"
                tp.order_id = order_id
                tp.actual_price = current_price
                tp.filled_quantity = quantity
                tp.filled_at = datetime.utcnow()
                
                # 计算盈亏
                entry_price = tp.signal.entry_price or current_price * 0.98  # 简化
                if tp.signal.signal_type.value.lower() == 'long':
                    tp.pnl = (current_price - entry_price) * quantity
                else:
                    tp.pnl = (entry_price - current_price) * quantity
                
                tp.pnl_percentage = (tp.pnl / (entry_price * quantity)) * 100
                
                db.commit()
                
                logger.info(f"[TRIGGER] 止盈订单触发: {tp.signal.symbol} @ {current_price}, 盈亏: {tp.pnl:.2f}")
                
        except Exception as e:
            logger.error(f"触发止盈订单失败: {e}")
    
    async def _trigger_stop_loss_order(self, signal: TelegramSignal, current_price: float, db: Session):
        """触发止损订单"""
        try:
            quantity = 50.0 / current_price  # 简化计算
            
            opposite_side = 'sell' if signal.signal_type.value.lower() == 'long' else 'buy'
            
            order_id = await exchange_client.create_order(
                symbol=signal.symbol,
                side=opposite_side,
                amount=quantity,
                price=current_price
            )
            
            if order_id:
                # 更新信号状态
                signal.status = SignalStatus.CANCELLED  # 止损触发，信号结束
                
                # 计算止损盈亏
                entry_price = signal.entry_price or current_price * 1.02  # 简化
                if signal.signal_type.value.lower() == 'long':
                    pnl = (current_price - entry_price) * quantity
                else:
                    pnl = (entry_price - current_price) * quantity
                
                db.commit()
                
                logger.info(f"[TRIGGER] 止损订单触发: {signal.symbol} @ {current_price}, 盈亏: {pnl:.2f}")
                
        except Exception as e:
            logger.error(f"触发止损订单失败: {e}")


# 全局实例
price_monitor = PriceMonitor()