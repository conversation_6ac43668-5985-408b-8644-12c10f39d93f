import ccxt
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from app.config import settings

logger = logging.getLogger(__name__)


class DryRunSimulator:
    """模拟交易执行器"""
    
    def __init__(self):
        self.orders = {}  # 存储模拟订单
        self.positions = {}  # 存储模拟持仓
        self.balance = 10000.0  # 模拟余额
    
    def create_order(self, symbol: str, side: str, amount: float, price: float) -> str:
        """创建模拟订单"""
        order_id = f"dry_{symbol}_{side}_{int(datetime.now().timestamp())}"
        
        self.orders[order_id] = {
            'id': order_id,
            'symbol': symbol,
            'side': side,
            'amount': amount,
            'price': price,
            'status': 'open',
            'filled': 0,
            'created_at': datetime.now()
        }
        
        # 模拟立即成交（简化）
        asyncio.create_task(self._simulate_fill(order_id))
        
        return order_id
    
    async def _simulate_fill(self, order_id: str):
        """模拟订单成交"""
        await asyncio.sleep(1)  # 模拟延迟
        
        if order_id in self.orders:
            order = self.orders[order_id]
            order['status'] = 'filled'
            order['filled'] = order['amount']
            
            # 更新模拟持仓
            symbol = order['symbol']
            if symbol not in self.positions:
                self.positions[symbol] = 0
            
            if order['side'] == 'buy':
                self.positions[symbol] += order['amount']
                self.balance -= order['amount'] * order['price']
            else:
                self.positions[symbol] -= order['amount']
                self.balance += order['amount'] * order['price']
            
            logger.info(f"[DRY RUN] 订单成交: {order_id} - {symbol} {order['side']} @ {order['price']}")
    
    def get_order(self, order_id: str) -> Dict[str, Any]:
        """获取订单状态"""
        return self.orders.get(order_id, {'status': 'not_found'})
    
    def get_balance(self) -> float:
        """获取模拟余额"""
        return self.balance
    
    def get_positions(self) -> Dict[str, float]:
        """获取模拟持仓"""
        return self.positions.copy()


class ExchangeClient:
    """统一交易所客户端"""
    
    def __init__(self):
        self.exchange = None
        self.dry_run_simulator = DryRunSimulator()
        self._initialize_exchange()
    
    def _initialize_exchange(self):
        """初始化交易所连接"""
        try:
            if settings.is_dry_run_mode:
                logger.info("[DRY RUN] 模拟交易模式")
                # dry_run模式不初始化真实交易所
            else:
                self.exchange = ccxt.binance({
                    'apiKey': settings.binance_api_key,
                    'secret': settings.binance_secret_key,
                    'enableRateLimit': True,
                })
                logger.info("[LIVE] 真实交易所连接成功")
            
        except Exception as e:
            logger.error(f"交易所连接失败: {e}")
    
    async def create_order(self, symbol: str, side: str, amount: float, price: float) -> Optional[str]:
        """创建订单"""
        try:
            if settings.is_dry_run_mode:
                # 模拟交易
                order_id = self.dry_run_simulator.create_order(symbol, side, amount, price)
                return order_id
            else:
                # 真实交易
                if not self.exchange:
                    logger.error("交易所未初始化")
                    return None
                order = self.exchange.create_limit_order(symbol, side, amount, price)
                logger.info(f"[LIVE] 订单创建: {order['id']} - {symbol} {side} @ {price}")
                return order['id']
                
        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return None
    
    async def get_order_status(self, order_id: str, symbol: str = None) -> Dict[str, Any]:
        """获取订单状态"""
        try:
            if settings.is_dry_run_mode:
                return self.dry_run_simulator.get_order(order_id)
            else:
                order = self.exchange.fetch_order(order_id, symbol)
                return {
                    'id': order['id'],
                    'status': order['status'],
                    'filled': order['filled'],
                    'price': order['price']
                }
                
        except Exception as e:
            logger.error(f"查询订单状态失败: {e}")
            return {'status': 'error'}
    
    async def get_balance(self) -> Dict[str, Any]:
        """获取余额"""
        try:
            if settings.is_dry_run_mode:
                return {
                    'USDT': {'free': self.dry_run_simulator.get_balance()},
                    'total_usdt': self.dry_run_simulator.get_balance()
                }
            else:
                balance = self.exchange.fetch_balance()
                return balance
                
        except Exception as e:
            logger.error(f"获取余额失败: {e}")
            return {}
    
    async def get_positions(self) -> Dict[str, float]:
        """获取持仓"""
        try:
            if settings.is_dry_run_mode:
                return self.dry_run_simulator.get_positions()
            else:
                # 真实持仓查询
                positions = self.exchange.fetch_positions()
                return {pos['symbol']: pos['size'] for pos in positions if pos['size'] != 0}
                
        except Exception as e:
            logger.error(f"获取持仓失败: {e}")
            return {}


# 全局实例
exchange_client = ExchangeClient()