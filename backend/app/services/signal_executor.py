import asyncio
import logging
from typing import Dict, Optional
from decimal import Decimal
from datetime import datetime
from sqlalchemy.orm import Session

from app.services.binance_client import BinanceClient
from app.models import TelegramSignal, SignalStatus, SignalTrade
from app.database import get_db

logger = logging.getLogger(__name__)

class SignalExecutor:
    """信号交易执行器"""
    
    def __init__(self, api_key: str, api_secret: str, testnet: bool = True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        self.client = None
        
        # 风险管理参数
        self.max_position_size_usdt = Decimal("100")  # 最大单笔投资金额
        self.min_position_size_usdt = Decimal("10")   # 最小单笔投资金额
        self.max_daily_trades = 20                     # 每日最大交易次数
        self.stop_loss_percentage = Decimal("0.05")   # 默认止损5%
        
    async def initialize(self):
        """初始化客户端"""
        self.client = BinanceClient(self.api_key, self.api_secret, self.testnet)
        await self.client.__aenter__()
        logger.info("信号执行器已初始化")
        
    async def shutdown(self):
        """关闭客户端"""
        if self.client:
            await self.client.__aexit__(None, None, None)
            logger.info("信号执行器已关闭")
            
    async def execute_signal(self, signal: TelegramSignal, db: Session) -> Dict:
        """执行交易信号"""
        try:
            logger.info(f"开始执行信号 {signal.id}: {signal.signal_type} {signal.symbol}")
            
            # 检查客户端
            if not self.client:
                await self.initialize()
                
            # 风险检查
            risk_check = await self._risk_check(signal, db)
            if not risk_check['allowed']:
                return {
                    'success': False,
                    'message': f"风险检查失败: {risk_check['reason']}",
                    'signal_id': signal.id
                }
                
            # 计算仓位大小
            position_size = await self._calculate_position_size(signal)
            
            # 执行交易
            if signal.signal_type.value.upper() in ['BUY', 'LONG']:
                result = await self._execute_buy_signal(signal, position_size, db)
            elif signal.signal_type.value.upper() in ['SELL', 'SHORT']:
                result = await self._execute_sell_signal(signal, position_size, db)
            else:
                return {
                    'success': False,
                    'message': f"不支持的信号类型: {signal.signal_type}",
                    'signal_id': signal.id
                }
                
            # 更新信号状态
            if result['success']:
                signal.status = SignalStatus.EXECUTED
                signal.processed_at = datetime.utcnow()
            else:
                signal.status = SignalStatus.FAILED
                
            db.commit()
            
            return result
            
        except Exception as e:
            logger.error(f"执行信号失败: {e}", exc_info=True)
            
            # 更新为失败状态
            signal.status = SignalStatus.FAILED
            db.commit()
            
            return {
                'success': False,
                'message': f"执行失败: {str(e)}",
                'signal_id': signal.id
            }
            
    async def _risk_check(self, signal: TelegramSignal, db: Session) -> Dict:
        """风险检查"""
        try:
            # 检查今日交易次数
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            today_trades = db.query(SignalTrade).filter(
                SignalTrade.entry_time >= today_start
            ).count()
            
            if today_trades >= self.max_daily_trades:
                return {
                    'allowed': False,
                    'reason': f"今日交易次数已达上限 ({self.max_daily_trades})"
                }
                
            # 检查交易对是否支持
            try:
                symbol_info = await self.client.get_symbol_info(signal.symbol)
                if symbol_info['status'] != 'TRADING':
                    return {
                        'allowed': False,
                        'reason': f"交易对 {signal.symbol} 当前不可交易"
                    }
            except Exception as e:
                return {
                    'allowed': False,
                    'reason': f"无法获取交易对信息: {str(e)}"
                }
                
            # 检查余额（如果是买入信号）
            if signal.signal_type.value.upper() in ['BUY', 'LONG']:
                usdt_balance = await self.client.get_balance('USDT')
                if not usdt_balance or usdt_balance['free'] < self.min_position_size_usdt:
                    return {
                        'allowed': False,
                        'reason': f"USDT余额不足，最少需要 {self.min_position_size_usdt}"
                    }
                    
            return {'allowed': True}
            
        except Exception as e:
            logger.error(f"风险检查失败: {e}")
            return {
                'allowed': False,
                'reason': f"风险检查异常: {str(e)}"
            }
            
    async def _calculate_position_size(self, signal: TelegramSignal) -> Decimal:
        """计算仓位大小"""
        try:
            # 基于置信度调整仓位大小
            confidence_multiplier = Decimal(str(signal.confidence_score))
            base_size = self.min_position_size_usdt + (
                self.max_position_size_usdt - self.min_position_size_usdt
            ) * confidence_multiplier
            
            # 确保在限制范围内
            position_size = max(
                self.min_position_size_usdt,
                min(self.max_position_size_usdt, base_size)
            )
            
            logger.info(f"信号 {signal.id} 计算仓位: ${position_size} (置信度: {signal.confidence_score})")
            return position_size
            
        except Exception as e:
            logger.warning(f"仓位计算失败，使用最小仓位: {e}")
            return self.min_position_size_usdt
            
    async def _execute_buy_signal(self, signal: TelegramSignal, position_size: Decimal, db: Session) -> Dict:
        """执行买入信号"""
        try:
            # 市价买入
            order = await self.client.market_buy(signal.symbol, position_size)
            
            # 记录交易
            trade = SignalTrade(
                signal_id=signal.id,
                order_id=str(order['orderId']),
                symbol=signal.symbol,
                side='BUY',
                quantity=Decimal(order.get('executedQty', '0')),
                entry_price=Decimal(order.get('fills', [{}])[0].get('price', '0')) if order.get('fills') else None,
                status='filled' if order['status'] == 'FILLED' else 'open',
                entry_time=datetime.utcnow()
            )
            
            db.add(trade)
            db.commit()
            
            logger.info(f"买入订单执行成功: {order['orderId']}")
            
            # 如果有止损价格，设置止损单
            if signal.stop_loss:
                await self._set_stop_loss(signal, trade, db)
                
            return {
                'success': True,
                'message': '买入订单执行成功',
                'order_id': order['orderId'],
                'trade_id': trade.id,
                'signal_id': signal.id
            }
            
        except Exception as e:
            logger.error(f"买入订单执行失败: {e}")
            return {
                'success': False,
                'message': f"买入失败: {str(e)}",
                'signal_id': signal.id
            }
            
    async def _execute_sell_signal(self, signal: TelegramSignal, position_size: Decimal, db: Session) -> Dict:
        """执行卖出信号"""
        try:
            # 获取对应的币种余额
            base_asset = signal.symbol.replace('USDT', '')  # 简单处理，假设都是USDT交易对
            balance = await self.client.get_balance(base_asset)
            
            if not balance or balance['free'] <= 0:
                return {
                    'success': False,
                    'message': f"没有足够的 {base_asset} 余额进行卖出",
                    'signal_id': signal.id
                }
                
            # 市价卖出全部余额
            sell_quantity = balance['free']
            order = await self.client.market_sell(signal.symbol, sell_quantity)
            
            # 记录交易
            trade = SignalTrade(
                signal_id=signal.id,
                order_id=str(order['orderId']),
                symbol=signal.symbol,
                side='SELL',
                quantity=Decimal(order.get('executedQty', '0')),
                exit_price=Decimal(order.get('fills', [{}])[0].get('price', '0')) if order.get('fills') else None,
                status='filled' if order['status'] == 'FILLED' else 'open',
                exit_time=datetime.utcnow()
            )
            
            db.add(trade)
            db.commit()
            
            logger.info(f"卖出订单执行成功: {order['orderId']}")
            
            return {
                'success': True,
                'message': '卖出订单执行成功',
                'order_id': order['orderId'],
                'trade_id': trade.id,
                'signal_id': signal.id
            }
            
        except Exception as e:
            logger.error(f"卖出订单执行失败: {e}")
            return {
                'success': False,
                'message': f"卖出失败: {str(e)}",
                'signal_id': signal.id
            }
            
    async def _set_stop_loss(self, signal: TelegramSignal, trade: SignalTrade, db: Session):
        """设置止损单"""
        try:
            stop_price = signal.stop_loss
            quantity = trade.quantity
            
            # 创建止损单
            stop_order = await self.client.create_order(
                symbol=signal.symbol,
                side='SELL',
                order_type='STOP_LOSS_LIMIT',
                quantity=quantity,
                price=stop_price * Decimal('0.999'),  # 略低于止损价
                time_in_force='GTC'
            )
            
            logger.info(f"止损单创建成功: {stop_order['orderId']}")
            
        except Exception as e:
            logger.warning(f"创建止损单失败: {e}")
            
    async def get_account_summary(self) -> Dict:
        """获取账户摘要"""
        try:
            if not self.client:
                await self.initialize()
                
            account_info = await self.client.get_account_info()
            balances = await self.client.get_balance()
            
            # 计算总价值（简化版本，只计算USDT）
            total_usdt = balances.get('USDT', {}).get('free', Decimal('0'))
            
            return {
                'success': True,
                'account_type': account_info.get('accountType', 'SPOT'),
                'total_usdt_value': float(total_usdt),
                'balances': {k: {'free': float(v['free']), 'locked': float(v['locked'])} 
                           for k, v in balances.items()},
                'can_trade': account_info.get('canTrade', False)
            }
            
        except Exception as e:
            logger.error(f"获取账户摘要失败: {e}")
            return {
                'success': False,
                'message': str(e)
            }

# 全局实例
signal_executor = None

async def get_signal_executor() -> SignalExecutor:
    """获取信号执行器实例"""
    global signal_executor
    
    if signal_executor is None:
        # 从配置中获取API密钥
        from app.config import settings
        
        signal_executor = SignalExecutor(
            api_key=settings.binance_api_key if hasattr(settings, 'binance_api_key') else "",
            api_secret=settings.binance_api_secret if hasattr(settings, 'binance_api_secret') else "",
            testnet=settings.binance_testnet if hasattr(settings, 'binance_testnet') else True
        )
        
    return signal_executor 