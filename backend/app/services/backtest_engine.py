import asyncio
import json
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
import traceback
from decimal import Decimal
import decimal

from app.models import (
    BacktestResult, BacktestStatus, TelegramSignal, SignalSource,
    SignalType, SignalStatus
)
from app.services.market_data import market_data_service
from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


def safe_float(value) -> float:
    """安全地将各种数值类型转换为float"""
    if value is None:
        return 0.0
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, Decimal):
        return float(value)
    if isinstance(value, str):
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    return 0.0

def safe_decimal(value) -> Decimal:
    """安全地将各种数值类型转换为Decimal"""
    if value is None:
        return Decimal('0')
    if isinstance(value, Decimal):
        return value
    if isinstance(value, (int, float, str)):
        try:
            return Decimal(str(value))
        except (ValueError, TypeError, decimal.InvalidOperation):
            return Decimal('0')
    return Decimal('0')


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, db: Session):
        self.db = db
        # 使用全局市场数据服务实例
        self.market_data = market_data_service
        
    async def run_backtest(self, backtest: BacktestResult) -> Dict[str, Any]:
        """运行回测"""
        try:
            logger.info(f"开始回测: {backtest.id} - {backtest.symbol} ({backtest.start_date} ~ {backtest.end_date})")
            
            # 更新状态为运行中
            backtest.status = BacktestStatus.RUNNING
            backtest.started_at = datetime.utcnow()
            self.db.commit()
            
            # 获取历史信号
            logger.info("步骤1: 获取历史信号")
            signals = await self._get_historical_signals(backtest)
            if not signals:
                raise ValueError(f"未找到符合条件的历史信号 (时间范围: {backtest.start_date} ~ {backtest.end_date})")
            
            # 获取所有交易对
            symbols = list(set(signal['symbol'] for signal in signals))
            logger.info(f"步骤2: 需要获取K线数据的交易对: {symbols}")
            
            # 获取K线数据
            klines_data = await self._get_kline_data(backtest, symbols)
            if not klines_data:
                raise ValueError("未能获取到任何K线数据")
            
            # 检查数据完整性
            missing_symbols = []
            for symbol in symbols:
                if symbol not in klines_data or klines_data[symbol].empty:
                    missing_symbols.append(symbol)
            
            if missing_symbols:
                logger.warning(f"缺少K线数据的交易对: {missing_symbols}")
                # 过滤掉没有K线数据的信号
                signals = [s for s in signals if s['symbol'] not in missing_symbols]
                if not signals:
                    raise ValueError(f"所有信号的交易对都缺少K线数据: {missing_symbols}")
                
            logger.info(f"步骤3: 开始执行回测，包含 {len(signals)} 个信号")
            
            # 执行回测
            results = await self._execute_backtest(backtest, signals, klines_data)
            
            logger.info("步骤4: 保存回测结果")
            # 保存结果
            await self._save_backtest_results(backtest, results)
            
            logger.info(f"回测完成: {backtest.id}")
            return results
            
        except Exception as e:
            error_msg = f"回测失败: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            
            # 更新状态为失败并清理所有财务计算字段
            backtest.status = BacktestStatus.FAILED
            backtest.error_message = error_msg[:1000]  # 限制错误信息长度
            backtest.completed_at = datetime.utcnow()
            
            # 清理所有可能被错误计算的财务指标
            backtest.final_balance = None
            backtest.total_return = None
            backtest.total_trades = None
            backtest.winning_trades = None
            backtest.losing_trades = None
            backtest.win_rate = None
            backtest.profit_factor = None
            backtest.max_drawdown = None
            backtest.sharpe_ratio = None
            backtest.result_json = None
            
            try:
                self.db.commit()
            except Exception as db_error:
                logger.error(f"保存错误状态失败: {db_error}")
            
            raise
    
    async def _get_historical_signals(self, backtest: BacktestResult) -> List[Dict]:
        """获取历史信号数据 - 不过滤信号状态，只按时间范围和信号源"""
        source_ids = [int(x) for x in backtest.source_ids.split(',')]
        
        # 预加载entry_points关系
        from sqlalchemy.orm import joinedload
        
        query = self.db.query(TelegramSignal).options(
            joinedload(TelegramSignal.entry_points)
        ).filter(
            and_(
                TelegramSignal.source_id.in_(source_ids),
                TelegramSignal.signal_time >= backtest.start_date,
                TelegramSignal.signal_time <= backtest.end_date
                # 移除信号状态过滤，不管信号状态都包含在回测中
            )
        )
        
        # 如果指定了交易对，则过滤
        if backtest.symbol != 'ALL':
            query = query.filter(TelegramSignal.symbol == backtest.symbol)
            
        query = query.order_by(TelegramSignal.signal_time)
        
        # 应用信号过滤器
        if backtest.signal_filters:
            filters = json.loads(backtest.signal_filters)
            query = self._apply_signal_filters(query, filters)
        
        signals = query.all()
        
        # 安全的数据转换，处理可能的None值
        signal_list = []
        for signal in signals:
            try:
                # 获取入场价格 - 优先使用entry_price，如果没有则从entry_points获取
                entry_price = signal.entry_price
                if entry_price is None and signal.entry_points:
                    # 从entry_points中选择第一个价格作为主入场价格
                    sorted_entry_points = sorted(signal.entry_points, key=lambda x: x.price)
                    if sorted_entry_points:
                        entry_price = sorted_entry_points[0].price
                        logger.debug(f"信号 {signal.id} 使用入场点价格: {entry_price}")
                
                signal_dict = {
                    'id': signal.id,
                    'signal_time': signal.signal_time,
                    'signal_type': signal.signal_type,
                    'symbol': signal.symbol,
                    'entry_price': entry_price,  # 使用修正后的入场价格
                    'stop_loss': signal.stop_loss,
                    'take_profit': signal.take_profit,
                    'leverage': signal.leverage or 1,
                    'source_id': signal.source_id,
                    'confidence_score': getattr(signal, 'confidence_score', 0.5),  # 安全获取
                    'status': signal.status,
                    'raw_message': signal.raw_message
                }
                
                # 只有当我们能获取到有效入场价格时才添加信号
                if entry_price is not None:
                    signal_list.append(signal_dict)
                else:
                    logger.warning(f"跳过信号 {signal.id} ({signal.symbol}): 无有效入场价格")
                
            except Exception as e:
                logger.error(f"转换信号数据失败: {signal.id} - {e}")
                # 跳过有问题的信号，继续处理其他信号
                continue
        
        logger.info(f"获取到 {len(signal_list)} 个历史信号用于回测")
        return signal_list
    
    async def _get_kline_data(self, backtest: BacktestResult, symbols: List[str] = None) -> Dict[str, pd.DataFrame]:
        """获取K线数据 - 使用增强的市场数据服务，自动处理缺失数据"""
        # 扩展开始时间以获取足够的历史数据用于技术指标计算
        extended_start = backtest.start_date - timedelta(days=30)
        klines_data = {}
        
        logger.info(f"开始获取回测K线数据，时间范围: {extended_start} ~ {backtest.end_date}")
        
        if backtest.symbol == 'ALL':
            # 获取所有需要的交易对K线数据
            if symbols:
                total_symbols = len(symbols)
                successful_downloads = 0
                failed_symbols = []
                
                for i, symbol in enumerate(symbols):
                    try:
                        logger.info(f"获取 {symbol} 的K线数据 ({i+1}/{total_symbols})")
                        
                        # 添加超时控制
                        klines = await asyncio.wait_for(
                            self.market_data.get_klines(
                            symbol=symbol,
                            interval='5m',  # 改为5分钟
                            start_time=extended_start,
                            end_time=backtest.end_date,
                            force_download=False  # 允许使用缓存数据
                            ),
                            timeout=120.0  # 2分钟超时
                        )
                        
                        klines_data[symbol] = klines
                        
                        if klines.empty:
                            logger.warning(f"未获取到 {symbol} 的K线数据")
                            failed_symbols.append(symbol)
                        else:
                            logger.info(f"成功获取 {symbol} 的 {len(klines)} 条K线数据")
                            successful_downloads += 1
                            
                    except asyncio.TimeoutError:
                        logger.error(f"获取 {symbol} K线数据超时")
                        klines_data[symbol] = pd.DataFrame()
                        failed_symbols.append(symbol)
                    except Exception as e:
                        logger.error(f"获取{symbol}K线数据失败: {e}")
                        klines_data[symbol] = pd.DataFrame()
                        failed_symbols.append(symbol)
                
                # 详细的结果报告
                if failed_symbols:
                    logger.warning(f"以下交易对数据获取失败: {', '.join(failed_symbols)}")
                
                logger.info(f"K线数据获取完成: {successful_downloads}/{total_symbols} 个交易对成功")
                
                # 如果成功率太低，给出警告但不停止回测
                if successful_downloads == 0:
                    logger.error("没有成功获取任何交易对的K线数据，回测将使用模拟数据")
                elif successful_downloads < total_symbols * 0.5:
                    logger.warning(f"数据获取成功率较低 ({successful_downloads}/{total_symbols})，回测结果可能不准确")
                    
        else:
            # 单个交易对
            try:
                logger.info(f"获取单个交易对 {backtest.symbol} 的K线数据")
                
                klines = await asyncio.wait_for(
                    self.market_data.get_klines(
                    symbol=backtest.symbol,
                    interval='5m',  # 改为5分钟
                    start_time=extended_start,
                    end_time=backtest.end_date,
                    force_download=False
                    ),
                    timeout=120.0
                )
                
                klines_data[backtest.symbol] = klines
                
                if klines.empty:
                    logger.warning(f"未获取到 {backtest.symbol} 的K线数据，将使用模拟数据")
                else:
                    logger.info(f"成功获取 {backtest.symbol} 的 {len(klines)} 条K线数据")
                    
            except asyncio.TimeoutError:
                logger.error(f"获取 {backtest.symbol} K线数据超时")
                klines_data[backtest.symbol] = pd.DataFrame()
            except Exception as e:
                logger.error(f"获取{backtest.symbol}K线数据失败: {e}")
                klines_data[backtest.symbol] = pd.DataFrame()
        
        # 统计获取结果
        total_symbols = len(klines_data)
        successful_symbols = len([k for k in klines_data.values() if not k.empty])
        logger.info(f"K线数据获取完成: {successful_symbols}/{total_symbols} 个交易对成功")
        
        return klines_data
    
    async def _execute_backtest(
        self, 
        backtest: BacktestResult, 
        signals: List[Dict], 
        klines_data: Dict[str, pd.DataFrame]
    ) -> Dict[str, Any]:
        """执行回测逻辑 - 支持多仓位和灵活资金管理"""
        
        from app.config import settings
        
        # 从系统配置读取回测参数
        config = {
            'trade_amount_mode': settings.backtest_trade_amount_mode,
            'trade_amount_value': settings.backtest_trade_amount_value,
            'max_positions': min(settings.backtest_max_positions, settings.max_concurrent_trades),  # 取较小值
            'max_allocation_percent': settings.backtest_max_allocation_percent,
        }
        
        # 兼容性检查和日志
        logger.info(f"回测配置整合:")
        logger.info(f"  系统最大并发交易数: {settings.max_concurrent_trades}")
        logger.info(f"  系统默认下注金额: {settings.default_stake_amount} USDT")
        logger.info(f"  回测资金模式: {config['trade_amount_mode']}")
        logger.info(f"  回测每笔金额/比例: {config['trade_amount_value']}")
        logger.info(f"  回测实际最大持仓数: {config['max_positions']} (受系统并发限制)")
        logger.info(f"  回测最大资金使用率: {config['max_allocation_percent']*100}%")
        
        # 验证配置合理性
        if config['trade_amount_mode'] == 'fixed_amount':
            # 固定金额模式：检查是否与系统default_stake_amount兼容
            if abs(config['trade_amount_value'] - settings.default_stake_amount) > 1:
                logger.warning(f"回测固定金额({config['trade_amount_value']})与系统默认下注金额({settings.default_stake_amount})不匹配")
        
        elif config['trade_amount_mode'] == 'percentage':
            # 百分比模式：检查是否会超过资金限制
            max_usage = config['trade_amount_value'] * config['max_positions']
            if max_usage > config['max_allocation_percent']:
                logger.warning(f"配置冲突: {config['max_positions']}个{config['trade_amount_value']*100}%的仓位将使用{max_usage*100}%资金，超过限制{config['max_allocation_percent']*100}%")
        
        # 初始化多仓位回测状态
        portfolio = {
            'balance': safe_float(backtest.initial_balance),     # 可用现金
            'allocated_balance': 0.0,                           # 已分配的保证金
            'positions': {},                                    # 多个持仓: {symbol: position_info}
            'trades': [],                                       # 所有交易记录
            'equity_curve': [],                                 # 权益曲线
            'max_drawdown': 0.0,
            'peak_equity': safe_float(backtest.initial_balance),
            'config': config,
            'system_config': {  # 保存系统配置以供参考
                'max_concurrent_trades': settings.max_concurrent_trades,
                'default_stake_amount': settings.default_stake_amount,
                'auto_trading_enabled': settings.auto_trading_enabled
            }
        }
        
        # 按时间顺序处理：先收集所有时间点
        all_timestamps = set()
        
        # 添加信号时间点
        for signal in signals:
            all_timestamps.add(signal['signal_time'])
        
        # 添加所有K线时间点
        for symbol, klines in klines_data.items():
            if not klines.empty:
                for timestamp in klines['timestamp']:
                    all_timestamps.add(timestamp)
        
        # 按时间顺序排序
        sorted_timestamps = sorted(all_timestamps)
        
        logger.info(f"多仓位回测开始: {len(sorted_timestamps)} 个时间点, 配置: {config}")
        
        # 按时间顺序处理每个时间点
        for current_time in sorted_timestamps:
            # 1. 更新所有持仓的市值
            self._update_all_positions_value(current_time, portfolio, klines_data)
            
            # 2. 检查所有持仓的止盈止损
            await self._check_all_stop_loss_take_profit(current_time, portfolio, klines_data)
            
            # 3. 处理该时间点的新信号
            current_signals = [s for s in signals if s['signal_time'] == current_time]
            for signal in current_signals:
                await self._process_signal_multi_position(signal, current_time, portfolio, klines_data)
            
            # 4. 记录权益曲线
            total_equity = self._calculate_total_equity(portfolio)
            portfolio['equity_curve'].append({
                'timestamp': current_time,
                'equity': total_equity,
                'balance': portfolio['balance'],
                'allocated_balance': portfolio['allocated_balance'],
                'position_count': len(portfolio['positions']),
                'positions_value': sum(pos.get('position_value', 0) for pos in portfolio['positions'].values())
            })
            
            # 5. 更新最大回撤
            if total_equity > portfolio['peak_equity']:
                portfolio['peak_equity'] = total_equity
            
            # 确保类型安全的回撤计算
            peak_equity_f = safe_float(portfolio['peak_equity'])
            total_equity_f = safe_float(total_equity)
            
            if peak_equity_f > 0:
                drawdown = (peak_equity_f - total_equity_f) / peak_equity_f
                if drawdown > portfolio['max_drawdown']:
                    portfolio['max_drawdown'] = drawdown
        
        # 回测结束时，强制平仓所有持仓
        if portfolio['positions']:
            await self._force_close_all_positions_multi(backtest.end_date, portfolio, klines_data)
        
        # 计算最终结果
        results = self._calculate_results_multi_position(backtest, portfolio)
        
        return results
    
    def _calculate_trade_amount(self, portfolio: Dict) -> float:
        """计算单笔交易的金额"""
        config = portfolio['config']
        
        if config['trade_amount_mode'] == 'fixed_amount':
            # 固定金额模式
            return min(config['trade_amount_value'], portfolio['balance'])
        
        elif config['trade_amount_mode'] == 'percentage':
            # 百分比模式
            initial_balance = portfolio['balance'] + portfolio['allocated_balance']
            return initial_balance * config['trade_amount_value']
        
        else:
            # 默认使用20%
            initial_balance = portfolio['balance'] + portfolio['allocated_balance']
            return initial_balance * 0.2

    def _can_open_new_position(self, portfolio: Dict, required_margin: float) -> bool:
        """检查是否可以开新仓位"""
        config = portfolio['config']
        
        # 检查资金是否充足
        if portfolio['balance'] < required_margin:
            return False
        
        # 检查最大持仓数限制
        if len(portfolio['positions']) >= config.get('max_positions', 5):
            return False
        
        # 检查最大资金使用比例
        total_balance = portfolio['balance'] + portfolio['allocated_balance']
        max_allocation = total_balance * config.get('max_allocation_percent', 0.8)
        
        if portfolio['allocated_balance'] + required_margin > max_allocation:
            return False
        
        return True

    async def _process_signal_multi_position(
        self, 
        signal: Dict, 
        current_time: datetime,
        portfolio: Dict, 
        klines_data: Dict[str, pd.DataFrame]
    ):
        """处理单个信号 - 多仓位版本"""
        symbol = signal['symbol']
        signal_type = signal['signal_type']
        
        # 获取当前价格
        klines = klines_data.get(symbol, pd.DataFrame())
        if klines.empty:
            logger.warning(f"没有找到 {symbol} 的K线数据，跳过信号")
            return
        
        current_price = self._get_price_at_time(klines, current_time)
        if current_price is None:
            logger.warning(f"无法获取 {symbol} 在 {current_time} 的价格")
            return
        
        # 检查是否已有该交易对的持仓
        existing_position = portfolio['positions'].get(symbol)
        
        if signal_type in [SignalType.BUY, SignalType.LONG]:
            await self._execute_buy_signal_multi(signal, current_price, portfolio, existing_position)
        elif signal_type in [SignalType.SELL, SignalType.SHORT]:
            await self._execute_sell_signal_multi(signal, current_price, portfolio, existing_position)
        elif signal_type == SignalType.CLOSE:
            await self._execute_close_signal_multi(signal, current_price, portfolio, symbol)
    
    async def _execute_buy_signal_multi(
        self, 
        signal: Dict, 
        current_price: float, 
        portfolio: Dict,
        existing_position: Optional[Dict]
    ):
        """执行买入信号 - 多仓位版本"""
        symbol = signal['symbol']
        
        # 如果已有空头持仓，先平仓
        if existing_position and existing_position['quantity'] < 0:
            await self._close_position_multi(symbol, current_price, portfolio)
            existing_position = None
        
        # 如果已有多头持仓，跳过（避免重复开仓）
        if existing_position and existing_position['quantity'] > 0:
            logger.info(f"跳过信号: {symbol} 已有多头持仓")
            return
        
        # 计算交易金额
        trade_amount = self._calculate_trade_amount(portfolio)
        entry_price = signal.get('entry_price', current_price)
        leverage = signal.get('leverage', 1)
        
        # 确保价格有效性
        if entry_price is None or entry_price <= 0:
            logger.error(f"无效的入场价格: {entry_price} for {symbol}")
            return
        
        # 确保数值类型安全
        trade_amount_f = safe_float(trade_amount)
        entry_price_f = safe_float(entry_price)
        leverage_f = safe_float(leverage)
        margin_required = trade_amount_f
        
        # 检查是否可以开新仓位
        if not self._can_open_new_position(portfolio, margin_required):
            logger.warning(f"无法开多仓: 资金或持仓数限制 - {symbol}")
            return
        
        # 计算交易数量 - 防止除零错误
        if entry_price_f <= 0:
            logger.error(f"无效的入场价格: {entry_price_f} for {symbol}")
            return
        quantity = trade_amount_f / entry_price_f
        
        # 创建新持仓
        new_position = {
            'quantity': quantity,
            'entry_price': entry_price,
            'leverage': leverage,
            'margin': margin_required,
            'stop_loss': signal.get('stop_loss'),
            'take_profit': signal.get('take_profit'),
            'position_value': margin_required,  # 初始持仓价值等于保证金
            'unrealized_pnl': 0.0,
            'signal_id': signal['id'],
            'open_time': signal['signal_time']
        }
        
        # 更新portfolio状态
        portfolio['positions'][symbol] = new_position
        portfolio['balance'] -= margin_required
        portfolio['allocated_balance'] += margin_required
        
        # 记录交易
        trade = {
            'signal_id': signal['id'],
            'type': 'buy',
            'symbol': symbol,
            'timestamp': signal['signal_time'],
            'price': entry_price,
            'quantity': quantity,
            'margin': margin_required,
            'trade_value': trade_amount_f,
            'leverage': leverage,
            'stop_loss': signal.get('stop_loss'),
            'take_profit': signal.get('take_profit')
        }
        portfolio['trades'].append(trade)
    
        logger.info(f"开多仓: {symbol} @ {entry_price}, 数量: {quantity:.4f}, 保证金: {margin_required:.2f}, 杠杆: {leverage}x")

    async def _execute_sell_signal_multi(
        self, 
        signal: Dict, 
        current_price: float, 
        portfolio: Dict,
        existing_position: Optional[Dict]
    ):
        """执行卖出信号 - 多仓位版本"""
        symbol = signal['symbol']
        
        # 如果已有多头持仓，先平仓
        if existing_position and existing_position['quantity'] > 0:
            await self._close_position_multi(symbol, current_price, portfolio)
            existing_position = None
        
        # 如果已有空头持仓，跳过（避免重复开仓）
        if existing_position and existing_position['quantity'] < 0:
            logger.info(f"跳过信号: {symbol} 已有空头持仓")
            return
        
        # 计算交易金额
        trade_amount = self._calculate_trade_amount(portfolio)
        entry_price = signal.get('entry_price', current_price)
        leverage = signal.get('leverage', 1)
        
        # 确保价格有效性
        if entry_price is None or entry_price <= 0:
            logger.error(f"无效的入场价格: {entry_price} for {symbol}")
            return
        
        # 确保数值类型安全
        trade_amount_f = safe_float(trade_amount)
        entry_price_f = safe_float(entry_price)
        leverage_f = safe_float(leverage)
        margin_required = trade_amount_f
        
        # 检查是否可以开新仓位
        if not self._can_open_new_position(portfolio, margin_required):
            logger.info(f"跳过信号: {symbol} 资金不足或达到持仓限制")
            return
        
        # 计算交易数量 - 防止除零错误
        if entry_price_f <= 0:
            logger.error(f"无效的入场价格: {entry_price_f} for {symbol}")
            return
        quantity = trade_amount_f / entry_price_f
        
        # 创建新持仓
        new_position = {
            'quantity': -quantity, # 空头持仓
            'entry_price': entry_price,
            'leverage': leverage,
            'margin': margin_required,
            'stop_loss': signal.get('stop_loss'),
            'take_profit': signal.get('take_profit'),
            'position_value': margin_required, # 初始持仓价值等于保证金
            'unrealized_pnl': 0.0,
            'signal_id': signal['id'],
            'open_time': signal['signal_time']
        }
        
        # 更新portfolio状态
        portfolio['positions'][symbol] = new_position
        portfolio['balance'] -= margin_required
        portfolio['allocated_balance'] += margin_required
        
        # 记录交易
        trade = {
            'signal_id': signal['id'],
            'type': 'sell',
            'symbol': symbol,
            'timestamp': signal['signal_time'],
            'price': entry_price,
            'quantity': quantity,
            'margin': margin_required,
            'trade_value': trade_amount_f,
            'leverage': leverage,
            'stop_loss': signal.get('stop_loss'),
            'take_profit': signal.get('take_profit')
        }
        portfolio['trades'].append(trade)
    
        logger.info(f"开空仓: {symbol} @ {entry_price}, 数量: {quantity:.4f}, 保证金: {margin_required:.2f}, 杠杆: {leverage}x")

    async def _execute_close_signal_multi(
        self, 
        signal: Dict, 
        current_price: float, 
        portfolio: Dict,
        symbol: str
    ):
        """执行平仓信号 - 多仓位版本"""
        existing_position = portfolio['positions'].get(symbol)
        
        if not existing_position:
            logger.warning(f"尝试平仓 {symbol} 但无持仓")
            return
        
        # 检查是否需要平仓
        if existing_position['quantity'] == 0:
            logger.info(f"跳过信号: {symbol} 已无持仓")
            return
        
        # 计算平仓数量
        quantity_to_close = abs(existing_position['quantity'])
        
        # 计算平仓盈亏 - 确保类型安全
        current_price_f = safe_float(current_price)
        entry_price_f = safe_float(existing_position['entry_price'])
        quantity_to_close_f = safe_float(quantity_to_close)
        
        if existing_position['quantity'] > 0: # 多头平仓
            pnl = (current_price_f - entry_price_f) * quantity_to_close_f
        else: # 空头平仓
            pnl = (entry_price_f - current_price_f) * quantity_to_close_f
        
        # 计算实际盈亏（考虑杠杆）
        # 注意：杠杆只放大盈亏，不改变计算方式
        actual_pnl = pnl
        
        # 计算原始保证金（从交易记录中获取更准确）
        # 简化计算：假设保证金是交易价值除以杠杆
        trade_value = safe_float(abs(existing_position['quantity'])) * safe_float(existing_position['entry_price'])
        leverage_val = safe_float(existing_position['leverage'])
        original_margin = trade_value / leverage_val if leverage_val > 0 else trade_value
        
        # 更新余额：返还保证金 + 盈亏
        portfolio['balance'] += original_margin + actual_pnl
        
        # 计算收益率
        return_pct = (actual_pnl / original_margin * 100) if original_margin > 0 else 0
        
        # 记录平仓交易
        close_trade = {
            'type': 'close',
            'timestamp': datetime.utcnow(),
            'price': current_price,
            'quantity': quantity_to_close,
            'pnl': actual_pnl,
            'pnl_percentage': return_pct,
            'entry_price': existing_position['entry_price'],
            'exit_price': current_price,
            'leverage': existing_position['leverage'],
            'margin_returned': original_margin,
            'symbol': symbol
        }
        portfolio['trades'].append(close_trade)
        
        logger.info(f"平仓: {symbol} @ {current_price}, 盈亏: {actual_pnl:.2f} ({return_pct:.2f}%)")
        
        # 更新持仓信息
        existing_position['quantity'] = 0
        existing_position['position_value'] = 0
        existing_position['unrealized_pnl'] = 0
        existing_position['open_time'] = None # 标记为已平仓

    async def _force_close_all_positions_multi(
        self, 
        end_time: datetime, 
        portfolio: Dict, 
        klines_data: Dict[str, pd.DataFrame]
    ):
        """强制平仓所有持仓 - 多仓位版本"""
        for symbol, position in list(portfolio['positions'].items()): # 遍历副本，避免在迭代时修改
            if position['quantity'] != 0: # 只平非零持仓
                klines = klines_data.get(symbol)
                if klines is not None and not klines.empty:
                    # 使用最后的价格平仓
                    last_price = klines.iloc[-1]['close']
                    await self._close_position_multi(symbol, last_price, portfolio)
                
                # 记录强制平仓
                total_equity = portfolio['balance'] + portfolio['allocated_balance'] + sum(pos.get('position_value', 0) for pos in portfolio['positions'].values())
                portfolio['equity_curve'].append({
                    'timestamp': end_time,
                    'equity': total_equity,
                    'balance': portfolio['balance'],
                    'allocated_balance': portfolio['allocated_balance'],
                    'position_count': len(portfolio['positions']),
                    'positions_value': sum(pos.get('position_value', 0) for pos in portfolio['positions'].values())
                })
    
    def _get_price_at_time(self, klines: pd.DataFrame, timestamp: datetime) -> Optional[float]:
        """获取指定时间的价格"""
        if klines is None or klines.empty:
            return None
        
        # 找到最接近的时间点
        klines['time_diff'] = abs((klines['timestamp'] - timestamp).dt.total_seconds())
        closest_idx = klines['time_diff'].idxmin()
        
        # 如果时间差超过15分钟，认为数据不可用（5分钟K线允许最多3个周期的误差）
        if klines.loc[closest_idx, 'time_diff'] > 900:  # 15分钟 = 900秒
            return None
        
        return klines.loc[closest_idx, 'close']

    def _update_all_positions_value(self, current_time: datetime, portfolio: Dict, klines_data: Dict[str, pd.DataFrame]):
        """更新所有持仓的市值"""
        for symbol, position in portfolio['positions'].items():
            klines = klines_data.get(symbol)
            if klines is not None and not klines.empty:
                current_price = self._get_price_at_time(klines, current_time)
                if current_price is not None:
                    # 确保所有价格和数量都是float类型
                    current_price_f = safe_float(current_price)
                    entry_price_f = safe_float(position['entry_price'])
                    quantity_f = safe_float(position['quantity'])
                    
                    # 计算未实现盈亏
                    if quantity_f > 0: # 多头
                        unrealized_pnl = (current_price_f - entry_price_f) * quantity_f
                    else: # 空头
                        unrealized_pnl = (entry_price_f - current_price_f) * abs(quantity_f)
                    
                    # 更新持仓价值
                    position['position_value'] = safe_float(position['margin']) + unrealized_pnl
                    position['unrealized_pnl'] = unrealized_pnl
                else:
                    position['position_value'] = safe_float(position['margin']) # 如果价格不可用，市值等于保证金
                    position['unrealized_pnl'] = 0.0
            else:
                position['position_value'] = safe_float(position['margin']) # 如果没有K线数据，市值等于保证金
                position['unrealized_pnl'] = 0.0

    async def _check_all_stop_loss_take_profit(
        self, 
        current_time: datetime, 
        portfolio: Dict, 
        klines_data: Dict[str, pd.DataFrame]
    ):
        """检查并执行止盈止损 - 多仓位版本"""
        for symbol, position in list(portfolio['positions'].items()): # 遍历副本，避免在迭代时修改
            if position['quantity'] == 0: # 只检查非零持仓
                continue
            
            # 获取当前价格
            klines = klines_data.get(symbol)
            if klines is None or klines.empty:
                logger.warning(f"无法获取 {symbol} 的K线数据，跳过止盈止损检查")
                continue
            
            current_price = self._get_price_at_time(klines, current_time)
            if current_price is None:
                logger.warning(f"无法获取 {symbol} 在 {current_time} 的价格，跳过止盈止损检查")
                continue
            
            stop_loss = position.get('stop_loss')
            take_profit = position.get('take_profit')
            
            should_close = False
            close_reason = ""
            
            if position['quantity'] > 0:  # 多头持仓
                # 检查止损
                if stop_loss and current_price <= stop_loss:
                    should_close = True
                    close_reason = f"止损触发: {current_price} <= {stop_loss}"
                # 检查止盈
                elif take_profit and current_price >= take_profit:
                    should_close = True
                    close_reason = f"止盈触发: {current_price} >= {take_profit}"
            
            else:  # 空头持仓
                # 检查止损
                if stop_loss and current_price >= stop_loss:
                    should_close = True
                    close_reason = f"止损触发: {current_price} >= {stop_loss}"
                # 检查止盈  
                elif take_profit and current_price <= take_profit:
                    should_close = True
                    close_reason = f"止盈触发: {current_price} <= {take_profit}"
            
            if should_close:
                logger.info(f"执行止盈止损: {close_reason}")
                await self._close_position_multi(symbol, current_price, portfolio)
                
                # 记录到权益曲线
                total_equity = portfolio['balance'] + portfolio['allocated_balance'] + sum(pos.get('position_value', 0) for pos in portfolio['positions'].values())
                portfolio['equity_curve'].append({
                    'timestamp': current_time,
                    'equity': total_equity,
                    'balance': portfolio['balance'],
                    'allocated_balance': portfolio['allocated_balance'],
                    'position_count': len(portfolio['positions']),
                    'positions_value': sum(pos.get('position_value', 0) for pos in portfolio['positions'].values())
                })

    def _calculate_total_equity(self, portfolio: Dict) -> float:
        """计算总权益"""
        return portfolio['balance'] + portfolio['allocated_balance'] + sum(pos.get('position_value', 0) for pos in portfolio['positions'].values())
    
    def _calculate_results_multi_position(self, backtest: BacktestResult, portfolio: Dict) -> Dict[str, Any]:
        """计算回测结果 - 多仓位版本"""
        trades = portfolio['trades']
        equity_curve = portfolio['equity_curve']
        
        # 区分开仓交易和平仓交易
        open_trades = [t for t in trades if t['type'] in ['buy', 'sell']]
        closed_trades = [t for t in trades if t['type'] == 'close']
        
        # 基础统计
        total_trades = len(open_trades)  # 开仓交易数量
        completed_trades = len(closed_trades)  # 完成的交易数量（有平仓）
        
        winning_trades = len([t for t in closed_trades if t.get('pnl', 0) > 0])
        losing_trades = len([t for t in closed_trades if t.get('pnl', 0) < 0])
        
        # 胜率基于完成的交易
        win_rate = winning_trades / completed_trades if completed_trades > 0 else 0
        
        # 收益计算 - 修正逻辑
        # 只计算现金余额，不包含未实现的持仓价值（除非明确平仓）
        if portfolio['positions']: # 如果有持仓，需要按当前市价平仓来计算最终价值
            # 假设所有持仓都以当前市价平仓
            current_total_equity = self._calculate_total_equity(portfolio)
        else:
            # 如果没有持仓，最终余额就是现金余额
            current_total_equity = portfolio['balance'] + portfolio['allocated_balance']
        
        # 总收益率基于实际余额变化 - 确保类型安全
        initial_balance_f = safe_float(backtest.initial_balance)
        current_total_equity_f = safe_float(current_total_equity)
        
        total_return = (current_total_equity_f - initial_balance_f) / initial_balance_f if initial_balance_f > 0 else 0
        
        # 盈亏统计 - 只计算已实现的盈亏
        realized_pnl = sum(t.get('pnl', 0) for t in closed_trades)
        unrealized_pnl = sum(pos.get('unrealized_pnl', 0) for pos in portfolio['positions'].values())
        total_pnl = realized_pnl + unrealized_pnl
        
        gross_profit = sum(t.get('pnl', 0) for t in closed_trades if t.get('pnl', 0) > 0)
        gross_loss = abs(sum(t.get('pnl', 0) for t in closed_trades if t.get('pnl', 0) < 0))
        
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else (gross_profit if gross_profit > 0 else 0)
        
        # 夏普比率计算
        if len(equity_curve) > 1:
            returns = []
            for i in range(1, len(equity_curve)):
                prev_equity = equity_curve[i-1]['equity']
                curr_equity = equity_curve[i]['equity']
                if prev_equity > 0:
                    returns.append((curr_equity - prev_equity) / prev_equity)
            
            if returns:
                avg_return = np.mean(returns)
                std_return = np.std(returns)
                sharpe_ratio = avg_return / std_return * np.sqrt(252) if std_return > 0 else 0
            else:
                sharpe_ratio = 0
        else:
            sharpe_ratio = 0
        
        # 安全的序列化结果，避免循环引用
        def safe_serialize_data(data):
            """递归转换数据为可序列化格式"""
            if isinstance(data, dict):
                return {k: safe_serialize_data(v) for k, v in data.items() if k != '_backtest'}  # 排除可能的backtest引用
            elif isinstance(data, list):
                return [safe_serialize_data(item) for item in data]
            elif isinstance(data, Decimal):  # 处理Decimal类型
                return float(data)
            elif hasattr(data, 'to_dict'):  # pandas对象
                return data.to_dict()
            elif hasattr(data, 'tolist'):  # numpy数组
                return data.tolist()
            elif isinstance(data, (datetime, pd.Timestamp)):
                return data.isoformat() if hasattr(data, 'isoformat') else str(data)
            elif isinstance(data, (np.integer, np.floating)):
                return float(data)
            else:
                return data
        
        # 构建结果，确保所有数据都是可序列化的
        results = {
            'summary': {
                'initial_balance': safe_float(backtest.initial_balance),
                'final_balance': safe_float(current_total_equity),
                'total_return': safe_float(total_return),
                'realized_pnl': safe_float(realized_pnl),
                'unrealized_pnl': safe_float(unrealized_pnl),
                'total_pnl': safe_float(total_pnl),
                'total_trades': int(total_trades),
                'completed_trades': int(completed_trades),
                'open_positions': len(portfolio['positions']),
                'winning_trades': int(winning_trades),
                'losing_trades': int(losing_trades),
                'win_rate': safe_float(win_rate),
                'profit_factor': safe_float(profit_factor),
                'max_drawdown': safe_float(portfolio['max_drawdown']),
                'sharpe_ratio': safe_float(sharpe_ratio),
                'gross_profit': safe_float(gross_profit),
                'gross_loss': safe_float(gross_loss)
            },
            'trades': safe_serialize_data(trades),
            'equity_curve': safe_serialize_data(equity_curve),
            'daily_returns': safe_serialize_data(self._calculate_daily_returns(equity_curve)),
            'monthly_returns': safe_serialize_data(self._calculate_monthly_returns(equity_curve)),
            'trade_analysis': safe_serialize_data(self._analyze_trades(closed_trades))
        }
        
        return results
    
    def _calculate_daily_returns(self, equity_curve: List[Dict]) -> List[Dict]:
        """计算日收益率"""
        if not equity_curve:
            return []
        
        df = pd.DataFrame(equity_curve)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['date'] = df['timestamp'].dt.date
        
        # 按日期分组，取每日最后一个权益值
        daily_equity = df.groupby('date')['equity'].last().reset_index()
        
        daily_returns = []
        for i in range(1, len(daily_equity)):
            prev_equity = daily_equity.iloc[i-1]['equity']
            curr_equity = daily_equity.iloc[i]['equity']
            daily_return = (curr_equity - prev_equity) / prev_equity if prev_equity > 0 else 0
            
            daily_returns.append({
                'date': daily_equity.iloc[i]['date'].isoformat(),
                'equity': curr_equity,
                'return': daily_return
            })
        
        return daily_returns
    
    def _calculate_monthly_returns(self, equity_curve: List[Dict]) -> List[Dict]:
        """计算月收益率"""
        if not equity_curve:
            return []
        
        df = pd.DataFrame(equity_curve)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['month'] = df['timestamp'].dt.to_period('M')
        
        # 按月份分组，取每月最后一个权益值
        monthly_equity = df.groupby('month')['equity'].last().reset_index()
        
        monthly_returns = []
        for i in range(1, len(monthly_equity)):
            prev_equity = monthly_equity.iloc[i-1]['equity']
            curr_equity = monthly_equity.iloc[i]['equity']
            monthly_return = (curr_equity - prev_equity) / prev_equity if prev_equity > 0 else 0
            
            monthly_returns.append({
                'month': str(monthly_equity.iloc[i]['month']),
                'equity': curr_equity,
                'return': monthly_return
            })
        
        return monthly_returns
    
    def _analyze_trades(self, closed_trades: List[Dict]) -> Dict[str, Any]:
        """分析交易统计"""
        if not closed_trades:
            return {}
        
        pnls = [t.get('pnl', 0) for t in closed_trades]
        winning_pnls = [p for p in pnls if p > 0]
        losing_pnls = [p for p in pnls if p < 0]
        
        return {
            'total_trades': len(closed_trades),
            'winning_trades': len(winning_pnls),
            'losing_trades': len(losing_pnls),
            'largest_win': max(winning_pnls) if winning_pnls else 0,
            'largest_loss': min(losing_pnls) if losing_pnls else 0,
            'average_win': np.mean(winning_pnls) if winning_pnls else 0,
            'average_loss': np.mean(losing_pnls) if losing_pnls else 0,
            'win_rate': len(winning_pnls) / len(closed_trades),
            'avg_trade_pnl': np.mean(pnls),
            'std_trade_pnl': np.std(pnls)
        }
    
    async def _save_backtest_results(self, backtest: BacktestResult, results: Dict[str, Any]):
        """保存回测结果"""
        summary = results['summary']
        
        # 更新回测记录
        backtest.status = BacktestStatus.COMPLETED
        backtest.completed_at = datetime.utcnow()
        backtest.final_balance = summary['final_balance']
        backtest.total_return = summary['total_return']
        backtest.total_trades = summary['total_trades']
        backtest.winning_trades = summary['winning_trades']
        backtest.losing_trades = summary['losing_trades']
        backtest.win_rate = summary['win_rate']
        backtest.profit_factor = summary['profit_factor']
        backtest.max_drawdown = summary['max_drawdown']
        backtest.sharpe_ratio = summary['sharpe_ratio']
        
        # 安全的JSON序列化，处理无穷大、NaN值和循环引用
        def safe_json_serialize(obj, seen_objects=None):
            import math
            import uuid
            
            if seen_objects is None:
                seen_objects = set()
            
            # 检查循环引用
            obj_id = id(obj)
            if obj_id in seen_objects:
                return f"<circular_reference:{type(obj).__name__}>"
            
            if isinstance(obj, (dict, list, tuple)):
                seen_objects.add(obj_id)
            
            try:
                if isinstance(obj, Decimal):  # 处理Decimal类型
                    return float(obj)
                elif isinstance(obj, float):
                    if math.isinf(obj) or math.isnan(obj):
                        return None
                    return obj
                elif isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    result = {}
                    for key, value in obj.items():
                        try:
                            result[str(key)] = safe_json_serialize(value, seen_objects.copy())
                        except Exception as e:
                            result[str(key)] = f"<serialization_error:{type(value).__name__}>"
                    return result
                elif isinstance(obj, (list, tuple)):
                    result = []
                    for item in obj:
                        try:
                            result.append(safe_json_serialize(item, seen_objects.copy()))
                        except Exception as e:
                            result.append(f"<serialization_error:{type(item).__name__}>")
                    return result
                elif hasattr(obj, '__dict__'):
                    # 自定义对象转换为字典
                    return safe_json_serialize(obj.__dict__, seen_objects.copy())
                else:
                    return obj
            except Exception as e:
                return f"<serialization_error:{type(obj).__name__}:{str(e)[:50]}>"
            finally:
                if isinstance(obj, (dict, list, tuple)):
                    seen_objects.discard(obj_id)
        
        try:
            # 创建结果的安全副本
            safe_results = safe_json_serialize(results)
            backtest.result_json = json.dumps(safe_results, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"JSON序列化失败: {e}")
            # 如果序列化仍然失败，保存基本信息
            minimal_results = {
                'summary': summary,
                'error': f"Full results serialization failed: {str(e)[:200]}",
                'timestamp': datetime.utcnow().isoformat()
            }
            backtest.result_json = json.dumps(minimal_results, ensure_ascii=False, indent=2)
            backtest.error_message = f"Partial serialization: {str(e)[:200]}"
        
        self.db.commit()
    
    def _apply_signal_filters(self, query, filters: Dict[str, Any]):
        """应用信号过滤器"""
        if 'signal_types' in filters:
            signal_types = [SignalType(t) for t in filters['signal_types']]
            query = query.filter(TelegramSignal.signal_type.in_(signal_types))
        
        if 'min_confidence' in filters:
            # 这里可以根据实际的置信度字段进行过滤
            pass
        
        if 'weekdays_only' in filters and filters['weekdays_only']:
            # 只包含工作日的信号
            query = query.filter(
                func.extract('dow', TelegramSignal.signal_time).between(1, 5)
            )
        
        return query

    async def _close_position_multi(self, symbol: str, current_price: float, portfolio: Dict):
        """平仓指定交易对 - 多仓位版本"""
        position = portfolio['positions'].get(symbol)
        if not position or position['quantity'] == 0:
            return
        
        # 获取持仓信息
        position_qty = position['quantity']
        entry_price = position['entry_price']
        leverage = position['leverage']
        margin = position['margin']
        
        # 确保所有数值都是float类型
        current_price_f = safe_float(current_price)
        entry_price_f = safe_float(entry_price)
        position_qty_f = safe_float(position_qty)
        margin_f = safe_float(margin)
        
        # 计算盈亏
        if position_qty_f > 0:  # 多头平仓
            pnl = (current_price_f - entry_price_f) * position_qty_f
        else:  # 空头平仓
            pnl = (entry_price_f - current_price_f) * abs(position_qty_f)
        
        # 计算实际盈亏
        actual_pnl = pnl
        
        # 更新余额：返还保证金 + 盈亏
        portfolio['balance'] += margin_f + actual_pnl
        portfolio['allocated_balance'] -= margin_f
        
        # 计算收益率
        return_pct = (actual_pnl / margin_f * 100) if margin_f > 0 else 0
        
        # 记录平仓交易
        close_trade = {
            'type': 'close',
            'timestamp': datetime.utcnow(),
            'price': current_price,
            'quantity': abs(position_qty),
            'pnl': actual_pnl,
            'pnl_percentage': return_pct,
            'entry_price': entry_price,
            'exit_price': current_price,
            'leverage': leverage,
            'margin_returned': margin_f,
            'symbol': symbol
        }
        portfolio['trades'].append(close_trade)
        
        logger.info(f"平仓: {symbol} @ {current_price}, 盈亏: {actual_pnl:.2f} ({return_pct:.2f}%)")
        
        # 删除持仓
        del portfolio['positions'][symbol]