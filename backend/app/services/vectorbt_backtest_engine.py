"""
VectorBT回测引擎
使用vectorbt库进行高性能回测
"""

import vectorbt as vbt
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
import traceback
from sqlalchemy.orm import Session

from app.models import BacktestResult, BacktestStatus, TelegramSignal, SignalStatus
from app.services.vectorbt_adapter import SignalDataAdapter
from app.services.vectorbt_visualizer import VectorbtVisualizer
from app.config import settings

logger = logging.getLogger(__name__)


class VectorbtBacktestEngine:
    """基于VectorBT的高性能回测引擎"""
    
    def __init__(self, db: Session):
        self.db = db
        self.adapter = SignalDataAdapter(db)
        self.visualizer = VectorbtVisualizer()
    
    async def run_backtest(self, backtest: BacktestResult) -> Dict[str, Any]:
        """运行回测"""
        try:
            logger.info(f"开始VectorBT回测: {backtest.id} - {backtest.symbol} ({backtest.start_date} ~ {backtest.end_date})")
            
            # 更新状态为运行中
            backtest.status = BacktestStatus.RUNNING
            backtest.started_at = datetime.utcnow()
            self.db.commit()
            
            # 1. 准备数据
            logger.info("步骤1: 准备回测数据")
            price_data, entries, exits = await self.adapter.prepare_backtest_data(backtest)
            
            logger.info(f"数据准备完成:")
            logger.info(f"  - 价格数据: {price_data.shape}")
            logger.info(f"  - 入场信号: {entries.sum().sum()} 个")
            logger.info(f"  - 出场信号: {exits.sum().sum()} 个")
            
            # 2. 配置回测参数
            logger.info("步骤2: 配置回测参数")
            backtest_config = self._get_backtest_config(backtest)
            
            # 3. 执行VectorBT回测
            logger.info("步骤3: 执行VectorBT回测")
            portfolio = self._run_vectorbt_backtest(
                price_data, entries, exits, backtest_config, backtest
            )
            
            # 4. 处理结果
            logger.info("步骤4: 处理回测结果")
            results = self.adapter.format_vectorbt_results(portfolio, backtest)

            # 5. 生成可视化报告和web图表
            logger.info("步骤5: 生成可视化报告和web图表")

            # 生成完整报告（用于下载）
            visualization_report = self.visualizer.generate_comprehensive_report(
                portfolio, backtest_config
            )
            results['visualization'] = visualization_report

            # 生成web图表（用于页面显示）
            web_charts = self.visualizer.generate_web_charts(
                portfolio, backtest_config
            )
            results['web_charts'] = web_charts

            # 6. 保存结果
            logger.info("步骤6: 保存回测结果")
            await self._save_backtest_results(backtest, results)

            # 7. 在控制台显示结果
            logger.info("步骤7: 显示回测结果")
            self._display_console_results(results, portfolio)
            
            logger.info(f"VectorBT回测完成: {backtest.id}")
            return results
            
        except Exception as e:
            error_msg = f"VectorBT回测失败: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            
            # 更新状态为失败
            backtest.status = BacktestStatus.FAILED
            backtest.error_message = error_msg[:1000]
            backtest.completed_at = datetime.utcnow()
            
            # 清理财务指标
            self._clear_financial_metrics(backtest)
            
            try:
                self.db.commit()
            except Exception as db_error:
                logger.error(f"保存错误状态失败: {db_error}")
            
            raise
    
    def _get_backtest_config(self, backtest: BacktestResult) -> Dict[str, Any]:
        """获取回测配置"""

        # 根据交易类型调整手续费
        # 现货交易：0.1% maker, 0.1% taker
        # 期货交易：0.02% maker, 0.04% taker
        fees = 0.0004  # 使用期货的平均手续费

        # 基础配置
        config = {
            'init_cash': float(backtest.initial_balance),
            'fees': fees,
            'slippage': 0.0002,  # 降低滑点到0.02%
            'freq': '5T',  # 5分钟频率
            'max_orders': settings.backtest_max_positions or 10,
            'size_type': 'percent',  # 按百分比下单
            'size': 0.1,  # 基础仓位：每次使用10%资金
            'direction': 'both',  # 支持多空
            'accumulate': False,  # 不累积仓位
            'conflict_mode': 'ignore'  # 忽略冲突信号
        }

        logger.info(f"回测配置: {config}")
        return config
    
    def _run_vectorbt_backtest(
        self,
        price_data: pd.DataFrame,
        entries: pd.DataFrame,
        exits: pd.DataFrame,
        config: Dict[str, Any],
        backtest: BacktestResult
    ) -> vbt.Portfolio:
        """执行VectorBT回测"""

        logger.info("开始执行VectorBT回测...")

        try:
            # 获取信号数据以计算动态仓位大小
            signals = self._get_signals_for_backtest(backtest)

            # 创建动态仓位大小矩阵
            size_matrix = self._create_dynamic_size_matrix(
                entries, signals, config['size']
            )

            logger.info(f"动态仓位配置完成，平均仓位大小: {size_matrix[size_matrix > 0].mean():.3f}")

            # 创建Portfolio对象
            portfolio = vbt.Portfolio.from_signals(
                close=price_data,
                entries=entries,
                exits=exits,
                init_cash=config['init_cash'],
                fees=config['fees'],
                slippage=config['slippage'],
                freq=config['freq'],
                size=size_matrix,  # 使用动态仓位大小
                size_type=config['size_type'],
                direction=config['direction'],
                accumulate=config['accumulate'],
                conflict_mode=config['conflict_mode']
            )

            logger.info("VectorBT回测执行完成")
            return portfolio

        except Exception as e:
            logger.error(f"VectorBT回测执行失败: {e}")
            raise

    def _get_signals_for_backtest(self, backtest: BacktestResult) -> List[Dict[str, Any]]:
        """获取回测相关的信号数据"""
        try:
            query = self.db.query(TelegramSignal).filter(
                TelegramSignal.signal_time >= backtest.start_date,
                TelegramSignal.signal_time <= backtest.end_date,
                TelegramSignal.status == SignalStatus.EXECUTED
            )

            if backtest.symbol != 'ALL':
                query = query.filter(TelegramSignal.symbol == backtest.symbol)

            signals_db = query.order_by(TelegramSignal.signal_time).all()

            signals = []
            for signal in signals_db:
                signals.append({
                    'id': signal.id,
                    'symbol': signal.symbol,
                    'signal_type': signal.signal_type,
                    'signal_time': signal.signal_time,
                    'entry_price': signal.entry_price,
                    'stop_loss': signal.stop_loss,
                    'take_profit': signal.take_profit,
                    'leverage': signal.leverage or 1,
                    'quantity': signal.quantity
                })

            return signals

        except Exception as e:
            logger.error(f"获取信号数据失败: {e}")
            return []

    def _create_dynamic_size_matrix(
        self,
        entries: pd.DataFrame,
        signals: List[Dict[str, Any]],
        base_size: float
    ) -> pd.DataFrame:
        """创建动态仓位大小矩阵，考虑杠杆和风险"""

        # 初始化仓位矩阵
        size_matrix = pd.DataFrame(
            base_size,
            index=entries.index,
            columns=entries.columns
        )

        # 为每个信号调整仓位大小
        for signal in signals:
            symbol = signal['symbol']
            signal_time = signal['signal_time']
            leverage = signal.get('leverage', 1)
            entry_price = signal.get('entry_price')
            stop_loss = signal.get('stop_loss')

            if symbol not in entries.columns:
                continue

            # 找到最接近的时间点
            try:
                closest_idx = entries.index.get_indexer([signal_time], method='nearest')[0]
                if closest_idx == -1:
                    continue
                closest_time = entries.index[closest_idx]

                # 计算风险调整后的仓位大小
                adjusted_size = self._calculate_risk_adjusted_size(
                    base_size, leverage, entry_price, stop_loss
                )

                # 设置该时间点的仓位大小
                size_matrix.loc[closest_time, symbol] = adjusted_size

                logger.debug(f"信号 {signal['id']} 仓位调整: {base_size:.3f} -> {adjusted_size:.3f} (杠杆: {leverage}x)")

            except Exception as e:
                logger.warning(f"处理信号 {signal['id']} 仓位调整失败: {e}")
                continue

        return size_matrix

    def _calculate_risk_adjusted_size(
        self,
        base_size: float,
        leverage: int,
        entry_price: Optional[float],
        stop_loss: Optional[float]
    ) -> float:
        """计算风险调整后的仓位大小"""

        # 基础仓位大小
        adjusted_size = base_size

        # 杠杆调整：杠杆越高，基础仓位越小
        if leverage > 1:
            # 使用平方根缩放，避免过度激进
            leverage_factor = 1.0 / (leverage ** 0.5)
            adjusted_size *= leverage_factor

        # 风险调整：如果有止损价格，根据风险距离调整仓位
        if entry_price and stop_loss and entry_price > 0:
            risk_percent = abs(stop_loss - entry_price) / entry_price

            # 风险越大，仓位越小
            if risk_percent > 0.1:  # 风险超过10%
                risk_factor = 0.5
            elif risk_percent > 0.05:  # 风险超过5%
                risk_factor = 0.7
            elif risk_percent > 0.02:  # 风险超过2%
                risk_factor = 0.85
            else:
                risk_factor = 1.0

            adjusted_size *= risk_factor

        # 确保仓位大小在合理范围内
        adjusted_size = max(0.01, min(adjusted_size, 0.5))  # 1%-50%之间

        return adjusted_size

    async def _save_backtest_results(self, backtest: BacktestResult, results: Dict[str, Any]):
        """保存回测结果"""
        try:
            summary = results['summary']
            
            # 更新回测记录
            backtest.status = BacktestStatus.COMPLETED
            backtest.completed_at = datetime.utcnow()
            backtest.final_balance = summary['final_balance']
            backtest.total_return = summary['total_return']
            backtest.total_trades = summary['total_trades']
            backtest.winning_trades = summary['winning_trades']
            backtest.losing_trades = summary['losing_trades']
            backtest.win_rate = summary['win_rate']
            backtest.profit_factor = summary['profit_factor']
            backtest.max_drawdown = summary['max_drawdown']
            backtest.sharpe_ratio = summary['sharpe_ratio']
            
            # 保存详细结果
            backtest.result_json = {
                'summary': summary,
                'engine': 'vectorbt',
                'version': vbt.__version__,
                'config': results.get('config', {}),
                'stats': results.get('stats', {})
            }
            
            self.db.commit()
            logger.info("回测结果保存成功")
            
        except Exception as e:
            logger.error(f"保存回测结果失败: {e}")
            raise
    
    def _clear_financial_metrics(self, backtest: BacktestResult):
        """清理财务指标"""
        backtest.final_balance = None
        backtest.total_return = None
        backtest.total_trades = None
        backtest.winning_trades = None
        backtest.losing_trades = None
        backtest.win_rate = None
        backtest.profit_factor = None
        backtest.max_drawdown = None
        backtest.sharpe_ratio = None
        backtest.result_json = None
    
    def generate_performance_report(self, portfolio: vbt.Portfolio) -> Dict[str, Any]:
        """生成详细的性能报告"""
        try:
            # 基础统计
            stats = portfolio.stats()
            
            # 交易分析
            trades = portfolio.trades
            orders = portfolio.orders
            
            # 回撤分析
            drawdowns = portfolio.drawdowns
            
            # 收益分析
            returns = portfolio.returns()
            
            report = {
                'basic_stats': {
                    'total_return': portfolio.total_return(),
                    'annual_return': portfolio.annualized_return(),
                    'max_drawdown': portfolio.max_drawdown(),
                    'sharpe_ratio': portfolio.sharpe_ratio(),
                    'calmar_ratio': portfolio.calmar_ratio(),
                    'omega_ratio': portfolio.omega_ratio(),
                    'sortino_ratio': portfolio.sortino_ratio()
                },
                'trade_stats': {
                    'total_trades': trades.count(),
                    'win_rate': trades.win_rate(),
                    'profit_factor': trades.profit_factor(),
                    'avg_trade': trades.pnl.mean(),
                    'avg_win': trades.winning.pnl.mean() if trades.winning.count() > 0 else 0,
                    'avg_loss': trades.losing.pnl.mean() if trades.losing.count() > 0 else 0,
                    'max_win': trades.pnl.max(),
                    'max_loss': trades.pnl.min(),
                    'avg_duration': trades.duration.mean()
                },
                'drawdown_stats': {
                    'max_drawdown': drawdowns.max_drawdown(),
                    'avg_drawdown': drawdowns.drawdown.mean(),
                    'max_duration': drawdowns.duration.max(),
                    'avg_duration': drawdowns.duration.mean()
                },
                'monthly_returns': returns.resample('M').apply(lambda x: (1 + x).prod() - 1),
                'yearly_returns': returns.resample('Y').apply(lambda x: (1 + x).prod() - 1)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成性能报告失败: {e}")
            return {}
    
    def get_portfolio_metrics(self, portfolio: vbt.Portfolio) -> Dict[str, float]:
        """获取组合指标"""
        try:
            metrics = {
                'total_return': float(portfolio.total_return()),
                'annual_return': float(portfolio.annualized_return()),
                'volatility': float(portfolio.returns().std() * np.sqrt(252)),
                'max_drawdown': float(portfolio.max_drawdown()),
                'sharpe_ratio': float(portfolio.sharpe_ratio()),
                'calmar_ratio': float(portfolio.calmar_ratio()),
                'sortino_ratio': float(portfolio.sortino_ratio()),
                'omega_ratio': float(portfolio.omega_ratio()),
                'skew': float(portfolio.returns().skew()),
                'kurtosis': float(portfolio.returns().kurtosis()),
                'var_95': float(portfolio.returns().quantile(0.05)),
                'cvar_95': float(portfolio.returns()[portfolio.returns() <= portfolio.returns().quantile(0.05)].mean())
            }
            
            # 交易相关指标
            if hasattr(portfolio, 'trades') and portfolio.trades.count() > 0:
                trades = portfolio.trades
                metrics.update({
                    'total_trades': int(trades.count()),
                    'win_rate': float(trades.win_rate()),
                    'profit_factor': float(trades.profit_factor()),
                    'avg_trade_duration': float(trades.duration.mean().total_seconds() / 3600),  # 小时
                    'max_consecutive_wins': int(trades.winning.count()),
                    'max_consecutive_losses': int(trades.losing.count())
                })
            
            return metrics

        except Exception as e:
            logger.error(f"获取组合指标失败: {e}")
            return {}

    def _display_console_results(self, results: Dict[str, Any], portfolio: vbt.Portfolio):
        """在控制台显示回测结果"""
        try:
            print("\n" + "="*80)
            print("🚀 VECTORBT 回测结果报告")
            print("="*80)

            # 显示基础统计
            summary = results.get('summary', {})
            print(f"\n📊 基础统计:")
            print(f"   初始资金: ${summary.get('initial_balance', 0):,.2f}")
            print(f"   最终资金: ${summary.get('final_balance', 0):,.2f}")
            print(f"   总收益率: {summary.get('total_return', 0):.2%}")
            print(f"   总交易数: {summary.get('total_trades', 0)}")
            print(f"   胜率: {summary.get('win_rate', 0):.2%}")
            print(f"   盈亏比: {summary.get('profit_factor', 0):.2f}")
            print(f"   最大回撤: {summary.get('max_drawdown', 0):.2%}")
            print(f"   夏普比率: {summary.get('sharpe_ratio', 0):.2f}")

            # 显示详细指标
            if 'visualization' in results and 'summary' in results['visualization']:
                viz_summary = results['visualization']['summary']

                if 'performance' in viz_summary:
                    perf = viz_summary['performance']
                    print(f"\n📈 性能指标:")
                    print(f"   年化收益率: {perf.get('annual_return', 0):.2%}")
                    print(f"   卡尔玛比率: {perf.get('calmar_ratio', 0):.2f}")
                    print(f"   索提诺比率: {perf.get('sortino_ratio', 0):.2f}")
                    print(f"   欧米茄比率: {perf.get('omega_ratio', 0):.2f}")

                if 'risk' in viz_summary:
                    risk = viz_summary['risk']
                    print(f"\n⚠️  风险指标:")
                    print(f"   年化波动率: {risk.get('volatility', 0):.2%}")
                    print(f"   VaR (95%): {risk.get('var_95', 0):.2%}")
                    print(f"   CVaR (95%): {risk.get('cvar_95', 0):.2%}")
                    print(f"   偏度: {risk.get('skewness', 0):.3f}")
                    print(f"   峰度: {risk.get('kurtosis', 0):.3f}")

                if 'trading' in viz_summary:
                    trading = viz_summary['trading']
                    print(f"\n💼 交易统计:")
                    print(f"   平均交易: ${trading.get('avg_trade', 0):.2f}")
                    print(f"   平均盈利: ${trading.get('avg_win', 0):.2f}")
                    print(f"   平均亏损: ${trading.get('avg_loss', 0):.2f}")
                    print(f"   最大盈利: ${trading.get('max_win', 0):.2f}")
                    print(f"   最大亏损: ${trading.get('max_loss', 0):.2f}")
                    print(f"   平均持仓时间: {trading.get('avg_duration_hours', 0):.1f} 小时")

            # 显示分析结论
            if 'visualization' in results and 'analysis' in results['visualization']:
                analysis = results['visualization']['analysis']

                print(f"\n🔍 分析结论:")
                if 'performance_summary' in analysis:
                    print(f"   {analysis['performance_summary']}")
                if 'risk_assessment' in analysis:
                    print(f"   {analysis['risk_assessment']}")
                if 'trade_quality' in analysis:
                    print(f"   {analysis['trade_quality']}")

                if 'recommendations' in analysis:
                    print(f"\n💡 改进建议:")
                    for i, rec in enumerate(analysis['recommendations'], 1):
                        print(f"   {i}. {rec}")

            # 显示图表信息
            if 'visualization' in results and 'charts' in results['visualization']:
                charts = results['visualization']['charts']
                print(f"\n📊 生成的图表:")
                for chart_name in charts.keys():
                    if chart_name != 'error':
                        print(f"   ✓ {chart_name}")

            print("\n" + "="*80)
            print("✅ 回测完成！详细图表已生成，可通过API获取。")
            print("="*80 + "\n")

        except Exception as e:
            logger.error(f"显示控制台结果失败: {e}")
            print(f"\n❌ 显示结果失败: {e}\n")
