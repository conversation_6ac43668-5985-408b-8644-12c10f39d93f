import requests
import json
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from app.config import settings

logger = logging.getLogger(__name__)


class FreqtradeClient:
    """Freqtrade API客户端"""
    
    def __init__(self):
        self.base_url = settings.freqtrade_api_url.rstrip('/')
        self.username = settings.freqtrade_api_username
        self.password = settings.freqtrade_api_password
        self.token = None
        self.token_expires = None
    
    async def _ensure_authenticated(self):
        """确保已认证"""
        if not self.token or (self.token_expires and datetime.utcnow() >= self.token_expires):
            await self._authenticate()
    
    async def _authenticate(self):
        """获取访问令牌"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/token/login",
                data={
                    "username": self.username,
                    "password": self.password
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                # 假设token有效期为1小时
                self.token_expires = datetime.utcnow() + timedelta(hours=1)
                logger.info("Freqtrade authentication successful")
            else:
                logger.error(f"Freqtrade authentication failed: {response.status_code}")
                raise Exception("Authentication failed")
                
        except Exception as e:
            logger.error(f"Error authenticating with Freqtrade: {e}")
            raise
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers
    
    async def get_status(self) -> Optional[Dict[str, Any]]:
        """获取Freqtrade状态"""
        try:
            await self._ensure_authenticated()
            
            response = requests.get(
                f"{self.base_url}/api/v1/status",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get status: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting Freqtrade status: {e}")
            return None
    
    async def get_balance(self) -> Optional[Dict[str, Any]]:
        """获取账户余额"""
        try:
            await self._ensure_authenticated()
            
            response = requests.get(
                f"{self.base_url}/api/v1/balance",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get balance: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return None
    
    async def get_trades(self, limit: int = 50) -> Optional[List[Dict[str, Any]]]:
        """获取交易历史"""
        try:
            await self._ensure_authenticated()
            
            response = requests.get(
                f"{self.base_url}/api/v1/trades",
                headers=self._get_headers(),
                params={"limit": limit},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("trades", [])
            else:
                logger.error(f"Failed to get trades: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting trades: {e}")
            return None
    
    async def get_open_trades(self) -> Optional[List[Dict[str, Any]]]:
        """获取未平仓交易"""
        try:
            await self._ensure_authenticated()
            
            response = requests.get(
                f"{self.base_url}/api/v1/status",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("open_trades", [])
            else:
                logger.error(f"Failed to get open trades: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting open trades: {e}")
            return None
    
    async def start_trading(self) -> bool:
        """启动交易"""
        try:
            await self._ensure_authenticated()
            
            response = requests.post(
                f"{self.base_url}/api/v1/start",
                headers=self._get_headers(),
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error starting trading: {e}")
            return False
    
    async def stop_trading(self) -> bool:
        """停止交易"""
        try:
            await self._ensure_authenticated()
            
            response = requests.post(
                f"{self.base_url}/api/v1/stop",
                headers=self._get_headers(),
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error stopping trading: {e}")
            return False
    
    async def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            await self._ensure_authenticated()
            
            response = requests.post(
                f"{self.base_url}/api/v1/reload_config",
                headers=self._get_headers(),
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error reloading config: {e}")
            return False
    
    async def force_buy(self, pair: str, price: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """强制买入"""
        try:
            await self._ensure_authenticated()
            
            data = {"pair": pair}
            if price:
                data["price"] = price
            
            response = requests.post(
                f"{self.base_url}/api/v1/forcebuy",
                headers=self._get_headers(),
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to force buy: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error force buying: {e}")
            return None
    
    async def force_sell(self, trade_id: int) -> Optional[Dict[str, Any]]:
        """强制卖出"""
        try:
            await self._ensure_authenticated()
            
            response = requests.post(
                f"{self.base_url}/api/v1/forcesell",
                headers=self._get_headers(),
                json={"tradeid": trade_id},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to force sell: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error force selling: {e}")
            return None
    
    async def get_profit(self) -> Optional[Dict[str, Any]]:
        """获取盈亏统计"""
        try:
            await self._ensure_authenticated()
            
            response = requests.get(
                f"{self.base_url}/api/v1/profit",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get profit: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting profit: {e}")
            return None
    
    async def get_performance(self) -> Optional[List[Dict[str, Any]]]:
        """获取性能统计"""
        try:
            await self._ensure_authenticated()
            
            response = requests.get(
                f"{self.base_url}/api/v1/performance",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get performance: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting performance: {e}")
            return None
    
    async def get_daily_profit(self, days: int = 30) -> Optional[Dict[str, Any]]:
        """获取每日盈亏"""
        try:
            await self._ensure_authenticated()
            
            response = requests.get(
                f"{self.base_url}/api/v1/daily",
                headers=self._get_headers(),
                params={"timescale": days},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get daily profit: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting daily profit: {e}")
            return None
    
    async def send_webhook(self, signal_data: Dict[str, Any]) -> bool:
        """通过Webhook发送信号"""
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/webhook",
                headers={"Content-Type": "application/json"},
                json=signal_data,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error sending webhook: {e}")
            return False
    
    def format_signal_for_webhook(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """格式化信号数据为Webhook格式"""
        webhook_data = {
            "action": signal.get("signal_type", "").lower(),
            "pair": signal.get("symbol", ""),
            "exchange": "binance",
        }
        
        if signal.get("entry_price"):
            webhook_data["price"] = signal["entry_price"]
        
        if signal.get("stop_loss"):
            webhook_data["stoploss"] = signal["stop_loss"]
        
        if signal.get("take_profit"):
            webhook_data["take_profit"] = signal["take_profit"]
        
        if signal.get("leverage"):
            webhook_data["leverage"] = signal["leverage"]
        
        return webhook_data


# 全局实例
freqtrade_client = FreqtradeClient() 