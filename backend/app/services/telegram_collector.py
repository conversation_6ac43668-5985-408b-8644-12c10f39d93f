import asyncio
import re
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from telethon import TelegramClient, events
from telethon.tl.types import Channel, Chat, User, MessageMediaPhoto, MessageMediaDocument
from telethon.errors import (
    SessionPasswordNeededError, PhoneCodeInvalidError, PhoneNumberInvalidError,
    FloodWaitError, PhoneCodeExpiredError, PhoneCodeHashEmptyError
)
from telethon.sessions import StringSession
from sqlalchemy.orm import Session
from app.config import settings
from app.database import SessionLocal
from app.models import TelegramSignal, SignalSource, SignalType, SignalStatus
from app.services.signal_parser import EnhancedSignalParser

logger = logging.getLogger(__name__)


class TelegramCollector:
    """Telegram信号采集器 - 基于Telethon用户客户端"""
    
    def __init__(self):
        self.client: Optional[TelegramClient] = None
        self.signal_parser = EnhancedSignalParser()
        self.is_running = False
        self.is_authenticated = False
        self._chat_cache: Dict[str, Dict[str, Any]] = {}
        self._monitored_chats: set = set()
        
    async def initialize(self):
        """初始化Telegram客户端"""
        try:
            api_id = settings.telegram_api_id
            api_hash = settings.telegram_api_hash
            session_string = settings.telegram_session_string
            
            if not api_id or not api_hash:
                raise ValueError("Telegram API ID和API Hash是必需的")
            
            # 使用字符串会话或创建新会话
            if session_string:
                session = StringSession(session_string)
            else:
                session = StringSession()
            
            self.client = TelegramClient(
                session,
                api_id,
                api_hash,
                device_model="Telegram Trading Bot",
                system_version="1.0.0",
                app_version="1.0.0"
            )
            
            await self.client.connect()
            logger.info("Telegram客户端初始化成功")
            
            # 尝试自动登录
            await self.auto_login()
            
        except Exception as e:
            logger.error(f"初始化Telegram客户端失败: {e}")
            raise
    
    async def auto_login(self) -> bool:
        """自动登录（如果有有效的session）"""
        try:
            # 检查是否有必要的配置信息
            if not settings.telegram_session_string:
                logger.debug("没有保存的会话信息，跳过自动登录")
                return False
            
            if not self.client:
                logger.warning("客户端未初始化，无法自动登录")
                return False
            
            # 检查是否已经认证
            if await self.client.is_user_authorized():
                self.is_authenticated = True
                me = await self.client.get_me()
                logger.info(f"自动登录成功: {me.username or me.phone}")
                
                # 更新会话字符串（可能有变化）
                new_session_string = self.client.session.save()
                if new_session_string != settings.telegram_session_string:
                    from app.services.config_manager import config_manager
                    from app.database import get_db
                    
                    db = next(get_db())
                    try:
                        config_manager.set_config(
                            "telegram_session_string", 
                            new_session_string, 
                            db
                        )
                        logger.info("会话字符串已更新")
                    finally:
                        db.close()
                
                return True
            else:
                logger.info("保存的会话已失效，需要重新登录")
                # 清除无效的会话字符串
                await self._clear_invalid_session()
                return False
                
        except Exception as e:
            logger.error(f"自动登录失败: {e}")
            # 如果自动登录失败，清除可能无效的会话
            await self._clear_invalid_session()
            return False
    
    async def _clear_invalid_session(self):
        """清除无效的会话字符串"""
        try:
            from app.services.config_manager import config_manager
            from app.database import get_db
            
            db = next(get_db())
            try:
                config_manager.set_config("telegram_session_string", "", db)
                logger.info("已清除无效的会话字符串")
            finally:
                db.close()
        except Exception as e:
            logger.error(f"清除会话字符串失败: {e}")
    
    async def authenticate(self, phone_number: str, code: str = None, password: str = None) -> Dict[str, Any]:
        """用户认证"""
        try:
            if not self.client:
                await self.initialize()
            
            # 检查是否已经认证
            if await self.client.is_user_authorized():
                self.is_authenticated = True
                me = await self.client.get_me()
                logger.info(f"用户已认证: {me.username or me.phone}")
                return {
                    "success": True,
                    "authenticated": True,
                    "user_info": {
                        "id": me.id,
                        "username": me.username,
                        "phone": me.phone,
                        "first_name": me.first_name,
                        "last_name": me.last_name
                    },
                    "session_string": self.client.session.save()
                }
            
            # 发送验证码
            if not code:
                try:
                    await self.client.send_code_request(phone_number)
                    logger.info(f"验证码已发送到: {phone_number}")
                    return {
                        "success": True,
                        "code_sent": True,
                        "message": "验证码已发送，请输入验证码"
                    }
                except PhoneNumberInvalidError:
                    return {
                        "success": False,
                        "error": "无效的手机号码"
                    }
                except FloodWaitError as e:
                    wait_time = e.seconds
                    logger.warning(f"发送验证码被限制，需要等待 {wait_time} 秒")
                    return {
                        "success": False,
                        "error": f"发送验证码过于频繁，请等待 {wait_time} 秒后重试"
                    }
                except Exception as e:
                    logger.error(f"发送验证码失败: {e}")
                    error_msg = str(e)
                    
                    # 处理常见的Telegram API错误
                    if "ResendCodeRequest" in error_msg:
                        return {
                            "success": False,
                            "error": "验证码发送次数已达上限，请等待5-10分钟后重试，或使用现有验证码"
                        }
                    elif "PHONE_CODE_EXPIRED" in error_msg:
                        return {
                            "success": False,
                            "error": "验证码已过期，请重新获取"
                        }
                    elif "PHONE_CODE_HASH_EMPTY" in error_msg:
                        return {
                            "success": False,
                            "error": "验证码会话已失效，请重新开始"
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"发送验证码失败: {error_msg}"
                        }
            
            # 验证登录
            try:
                await self.client.sign_in(phone_number, code)
                self.is_authenticated = True
                me = await self.client.get_me()
                logger.info(f"用户登录成功: {me.username or me.phone}")
                
                return {
                    "success": True,
                    "authenticated": True,
                    "user_info": {
                        "id": me.id,
                        "username": me.username,
                        "phone": me.phone,
                        "first_name": me.first_name,
                        "last_name": me.last_name
                    },
                    "session_string": self.client.session.save()
                }
                
            except SessionPasswordNeededError:
                # 需要两步验证密码
                if not password:
                    return {
                        "success": False,
                        "two_factor_required": True,
                        "message": "需要两步验证密码"
                    }
                
                try:
                    await self.client.sign_in(password=password)
                    self.is_authenticated = True
                    me = await self.client.get_me()
                    logger.info(f"用户登录成功: {me.username or me.phone}")
                    
                    return {
                        "success": True,
                        "authenticated": True,
                        "user_info": {
                            "id": me.id,
                            "username": me.username,
                            "phone": me.phone,
                            "first_name": me.first_name,
                            "last_name": me.last_name
                        },
                        "session_string": self.client.session.save()
                    }
                except Exception as e:
                    logger.error(f"两步验证失败: {e}")
                    return {
                        "success": False,
                        "error": "两步验证密码错误"
                    }
                    
            except PhoneCodeInvalidError:
                return {
                    "success": False,
                    "error": "验证码无效"
                }
            except Exception as e:
                logger.error(f"登录失败: {e}")
                return {
                    "success": False,
                    "error": f"登录失败: {str(e)}"
                }
                
        except Exception as e:
            logger.error(f"认证过程发生错误: {e}")
            return {
                "success": False,
                "error": f"认证失败: {str(e)}"
            }
    
    async def start(self):
        """启动消息监听"""
        try:
            if not self.client:
                await self.initialize()
            
            if not self.is_authenticated:
                logger.warning("用户未认证，无法启动消息监听")
                return
            
            if self.is_running:
                logger.info("消息监听已在运行中")
                return
            
            # 更新监听的群组列表
            await self._update_monitored_chats()
            
            # 添加消息处理器
            @self.client.on(events.NewMessage)
            async def handle_new_message(event):
                await self._handle_message(event)
            
            self.is_running = True
            logger.info("Telegram消息监听已启动")
            
        except Exception as e:
            logger.error(f"启动消息监听失败: {e}")
            raise
    
    async def start_monitoring(self):
        """在用户登录后启动监听（不阻塞）"""
        try:
            if not self.is_authenticated:
                logger.warning("用户未认证，无法启动监听")
                return False
            
            if self.is_running:
                logger.info("监听已在运行中")
                return True
            
            # 更新监听的群组列表
            await self._update_monitored_chats()
            
            # 添加消息处理器
            @self.client.on(events.NewMessage)
            async def handle_new_message(event):
                await self._handle_message(event)
            
            self.is_running = True
            logger.info("Telegram消息监听已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动消息监听失败: {e}")
            return False
    
    async def stop(self):
        """停止消息监听"""
        if self.client and self.is_running:
            await self.client.disconnect()
            self.is_running = False
            logger.info("Telegram消息监听已停止")
    
    async def _update_monitored_chats(self):
        """更新监听的群组列表"""
        try:
            db = SessionLocal()
            try:
                # 从数据库获取活跃的信号源
                active_sources = db.query(SignalSource).filter(
                    SignalSource.is_active == True
                ).all()
                
                chat_ids = [source.telegram_chat_id for source in active_sources]
                self._monitored_chats = set(chat_ids)
                logger.info(f"监听群组列表已更新: {len(self._monitored_chats)} 个群组")
            finally:
                db.close()
        except Exception as e:
            logger.error(f"更新监听群组列表失败: {e}")
    
    async def _handle_message(self, event):
        """处理接收到的消息"""
        try:
            message = event.message
            if not message or not message.text:
                return
            
            # 获取聊天信息
            chat = await message.get_chat()
            chat_id = str(chat.id)
            
            # 检查是否是监听的群组
            if not self._is_monitored_chat(chat_id):
                return
            
            # 跳过回复消息（只处理原始信号）
            if message.reply_to:
                logger.debug(f"跳过回复消息 from chat {chat_id}: {message.text[:100]}...")
                return
            
            # 跳过转发消息
            if message.forward:
                logger.debug(f"跳过转发消息 from chat {chat_id}: {message.text[:100]}...")
                return
            
            message_id = str(message.id)
            message_text = message.text
            message_time = message.date
            
            logger.info(f"接收到消息 from chat {chat_id}: {message_text[:100]}...")
            
            # 先检查是否为状态更新消息，如果是则直接跳过
            if self.signal_parser.is_status_update_message(message_text):
                logger.info(f"跳过状态更新消息 from chat {chat_id}: {message_text[:100]}...")
                return
            
            # 解析信号
            parsed_signal = await self.signal_parser.parse_message(message_text)
            if not parsed_signal:
                logger.debug("未找到有效的交易信号")
                return
            
            # 保存信号到数据库
            await self._save_signal(
                chat_id=chat_id,
                message_id=message_id,
                message_text=message_text,
                message_time=message_time,
                parsed_signal=parsed_signal
            )
            
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
    
    def _is_monitored_chat(self, chat_id: str) -> bool:
        """检查是否是监听的聊天"""
        return chat_id in self._monitored_chats
    
    async def _save_signal(
        self,
        chat_id: str,
        message_id: str,
        message_text: str,
        message_time: datetime,
        parsed_signal: Dict[str, Any]
    ):
        """保存信号到数据库"""
        db = SessionLocal()
        try:
            # 获取或创建信号源
            source = self._get_or_create_source(db, chat_id)
            
            # 检查是否已存在相同的信号（避免重复处理）
            existing_signal = db.query(TelegramSignal).filter(
                TelegramSignal.source_id == source.id,
                TelegramSignal.message_id == message_id
            ).first()
            
            if existing_signal:
                logger.debug(f"信号已存在，跳过处理: {message_id}")
                return
            
            # 使用信号管理器处理信号
            from app.services.signal_manager import signal_manager
            result = await signal_manager.process_raw_signal(
                raw_message=message_text,
                source_id=source.id,
                message_id=message_id,
                signal_time=message_time
            )
            
            if result:
                logger.info(f"信号已通过管理器保存: {result['signal_id']} - {parsed_signal.get('symbol')} {parsed_signal.get('signal_type')}")
            else:
                logger.warning(f"信号管理器无法处理信号: {message_text[:100]}...")
            
        except Exception as e:
            logger.error(f"保存信号时发生错误: {e}")
        finally:
            db.close()
    
    def _get_or_create_source(self, db: Session, chat_id: str) -> SignalSource:
        """获取或创建信号源"""
        source = db.query(SignalSource).filter(
            SignalSource.telegram_chat_id == chat_id
        ).first()
        
        if not source:
            # 尝试从缓存获取群组信息
            chat_info = self._chat_cache.get(chat_id, {})
            chat_title = chat_info.get("title", f"Chat_{chat_id}")
            
            source = SignalSource(
                name=chat_title,
                telegram_chat_id=chat_id,
                description=f"Auto-created source for chat {chat_id}",
                is_active=True
            )
            db.add(source)
            db.commit()
            db.refresh(source)
            logger.info(f"创建新的信号源: {source.name}")
        
        return source
    
    async def get_user_dialogs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取用户的对话列表（包括群组和频道）"""
        try:
            if not self.client or not self.is_authenticated:
                logger.warning("客户端未初始化或用户未认证")
                return []
            
            dialogs = []
            async for dialog in self.client.iter_dialogs(limit=limit):
                try:
                    entity = dialog.entity
                    
                    # 只处理群组和频道
                    if isinstance(entity, (Channel, Chat)):
                        # 安全地获取对话信息
                        dialog_info = {
                            "id": str(entity.id),
                            "title": getattr(entity, 'title', 'Unknown'),
                            "type": self._get_entity_type(entity),
                            "username": getattr(entity, 'username', None),
                            "description": getattr(entity, 'about', ''),
                            "member_count": getattr(entity, 'participants_count', 0),
                            "unread_count": getattr(dialog, 'unread_count', 0),
                            "is_muted": self._get_dialog_mute_status(dialog),
                            "date": self._get_dialog_date(dialog)
                        }
                        dialogs.append(dialog_info)
                        
                        # 缓存群组信息
                        self._chat_cache[str(entity.id)] = dialog_info
                        
                except Exception as dialog_error:
                    logger.warning(f"处理单个对话时出错: {dialog_error}")
                    continue
            
            logger.info(f"获取到 {len(dialogs)} 个群组/频道")
            return dialogs
            
        except Exception as e:
            logger.error(f"获取用户对话列表失败: {e}")
            return []
    
    def _get_dialog_mute_status(self, dialog) -> bool:
        """安全地获取对话静音状态"""
        try:
            # 尝试不同的属性名称
            if hasattr(dialog, 'is_muted'):
                return dialog.is_muted
            elif hasattr(dialog, 'muted'):
                return dialog.muted
            elif hasattr(dialog, 'notify_settings'):
                notify_settings = dialog.notify_settings
                if hasattr(notify_settings, 'mute_until'):
                    # 如果有 mute_until 且值大于0，说明被静音
                    return notify_settings.mute_until > 0
            return False
        except Exception as e:
            logger.debug(f"获取静音状态失败: {e}")
            return False
    
    def _get_dialog_date(self, dialog) -> Optional[str]:
        """安全地获取对话日期"""
        try:
            if hasattr(dialog, 'date') and dialog.date:
                return dialog.date.isoformat()
            elif hasattr(dialog, 'top_message') and dialog.top_message:
                if hasattr(dialog.top_message, 'date'):
                    return dialog.top_message.date.isoformat()
            return None
        except Exception as e:
            logger.debug(f"获取对话日期失败: {e}")
            return None
    
    def _get_entity_type(self, entity) -> str:
        """获取实体类型"""
        if isinstance(entity, Channel):
            if entity.broadcast:
                return "channel"
            elif entity.megagroup:
                return "supergroup"
            else:
                return "channel"
        elif isinstance(entity, Chat):
            return "group"
        else:
            return "unknown"
    
    async def get_chat_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """获取聊天信息"""
        try:
            if not self.client or not self.is_authenticated:
                return None
            
            # 尝试多种方式获取实体
            entity = None
            chat_id_int = int(chat_id)
            
            # 如果是正数且看起来像群组ID，尝试转换为负数
            if chat_id_int > 0 and len(chat_id) >= 10:
                try:
                    # 尝试作为超级群组ID (加上-100前缀)
                    super_group_id = -1000000000000 - chat_id_int
                    entity = await self.client.get_entity(super_group_id)
                except:
                    try:
                        # 尝试作为普通群组ID (直接转负数)
                        group_id = -chat_id_int
                        entity = await self.client.get_entity(group_id)
                    except:
                        # 最后尝试原始ID
                        entity = await self.client.get_entity(chat_id_int)
            else:
                # 直接使用原始ID
                entity = await self.client.get_entity(chat_id_int)
            
            if isinstance(entity, (Channel, Chat)):
                return {
                    "id": str(entity.id),
                    "title": entity.title,
                    "type": self._get_entity_type(entity),
                    "username": getattr(entity, 'username', None),
                    "description": getattr(entity, 'about', ''),
                    "member_count": getattr(entity, 'participants_count', 0),
                }
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取聊天信息失败: {e}")
            return None
    
    async def search_dialogs(self, query: str, dialogs: List[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """搜索对话"""
        if dialogs is None:
            dialogs = await self.get_user_dialogs()
        
        if not query:
            return dialogs
        
        query = query.lower()
        filtered_dialogs = []
        
        for dialog in dialogs:
            # 搜索条件：群组ID、用户名、标题、描述
            search_fields = [
                dialog.get('username', '').lower(),
                dialog.get('id', '').lower(),
                dialog.get('title', '').lower(),
                dialog.get('description', '').lower()
            ]
            
            # 检查是否匹配任一搜索字段
            if any(query in field for field in search_fields if field):
                filtered_dialogs.append(dialog)
        
        return filtered_dialogs
    
    def clear_cache(self):
        """清除缓存"""
        self._chat_cache.clear()
    
    async def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        try:
            if not self.client or not self.is_authenticated:
                return None
            
            me = await self.client.get_me()
            return {
                "id": me.id,
                "username": me.username,
                "phone": me.phone,
                "first_name": me.first_name,
                "last_name": me.last_name,
                "is_authenticated": True
            }
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None
    
    async def logout(self):
        """用户登出"""
        try:
            if self.client:
                await self.client.log_out()
                self.is_authenticated = False
                logger.info("用户已登出")
        except Exception as e:
            logger.error(f"登出失败: {e}")
    
    async def fetch_chat_history(
        self, 
        chat_id: str, 
        limit: int = 100,
        days_back: int = 30,
        skip_replies: bool = True
    ) -> Dict[str, Any]:
        """
        拉取指定群组的历史消息
        
        Args:
            chat_id: 群组ID
            limit: 拉取消息数量限制
            days_back: 向前拉取多少天的消息
            skip_replies: 是否跳过回复消息，只获取原始信号
        
        Returns:
            包含成功状态、消息数量和处理结果的字典
        """
        try:
            if not self.client or not self.is_authenticated:
                return {
                    "success": False,
                    "error": "Telegram客户端未初始化或用户未认证"
                }
            
            logger.info(f"开始拉取群组 {chat_id} 的历史消息，限制 {limit} 条，回溯 {days_back} 天")
            
            # 计算时间范围
            from datetime import datetime, timedelta
            start_date = datetime.now() - timedelta(days=days_back)
            
            # 获取群组实体
            try:
                # 尝试多种方式获取实体
                chat_entity = None
                chat_id_int = int(chat_id)
                
                # 如果是正数且看起来像群组ID，尝试转换为负数
                if chat_id_int > 0 and len(chat_id) >= 10:
                    try:
                        # 尝试作为超级群组ID (加上-100前缀)
                        super_group_id = -1000000000000 - chat_id_int
                        chat_entity = await self.client.get_entity(super_group_id)
                        logger.info(f"成功获取超级群组实体: {super_group_id}")
                    except:
                        try:
                            # 尝试作为普通群组ID (直接转负数)
                            group_id = -chat_id_int
                            chat_entity = await self.client.get_entity(group_id)
                            logger.info(f"成功获取普通群组实体: {group_id}")
                        except:
                            # 最后尝试原始ID
                            chat_entity = await self.client.get_entity(chat_id_int)
                            logger.info(f"成功获取原始ID实体: {chat_id_int}")
                else:
                    # 直接使用原始ID
                    chat_entity = await self.client.get_entity(chat_id_int)
                    logger.info(f"成功获取实体: {chat_id_int}")
                    
            except Exception as e:
                logger.error(f"无法获取群组实体 {chat_id}: {e}")
                return {
                    "success": False,
                    "error": f"无法访问群组 {chat_id}: {str(e)}"
                }
            
            # 拉取历史消息
            messages = []
            processed_count = 0
            skipped_count = 0
            duplicate_count = 0
            signal_count = 0
            
            # 如果limit很大（比如10000），说明用户想要拉取时间范围内的所有消息
            # 我们需要分批拉取以避免内存问题和API限制
            actual_limit = min(limit, 100) if limit <= 1000 else None
            
            async for message in self.client.iter_messages(
                chat_entity, 
                limit=actual_limit,
                offset_date=start_date
            ):
                try:
                    # 检查消息是否有效
                    if not message or not message.text:
                        skipped_count += 1
                        continue
                    
                    # 跳过回复消息（只要原始信号）
                    if skip_replies and message.reply_to:
                        skipped_count += 1
                        continue
                    
                    # 跳过转发消息
                    if message.forward:
                        skipped_count += 1
                        continue
                    
                    # 检查消息是否包含交易信号关键词
                    if not self._contains_signal_keywords(message.text):
                        skipped_count += 1
                        continue
                    
                    message_id = str(message.id)
                    message_text = message.text
                    message_time = message.date
                    
                    # 检查消息是否已存在（基于message_id去重）
                    if await self._is_message_duplicate(chat_id, message_id):
                        duplicate_count += 1
                        continue
                    
                    logger.info(f"处理历史消息 {message_id}: {message_text[:100]}...")
                    
                    # 先检查是否为状态更新消息，如果是则直接跳过
                    if self.signal_parser.is_status_update_message(message_text):
                        logger.debug(f"跳过历史状态更新消息 {message_id}: {message_text[:100]}...")
                        skipped_count += 1
                        continue
                    
                    # 解析信号
                    parsed_signal = await self.signal_parser.parse_message(message_text)
                    if not parsed_signal:
                        skipped_count += 1
                        continue
                    
                    # 保存信号到数据库（标记为历史信号，不触发自动交易）
                    await self._save_historical_signal(
                        chat_id=chat_id,
                        message_id=message_id,
                        message_text=message_text,
                        message_time=message_time,
                        parsed_signal=parsed_signal
                    )
                    
                    signal_count += 1
                    processed_count += 1
                    
                    messages.append({
                        "id": message_id,
                        "text": message_text[:200] + "..." if len(message_text) > 200 else message_text,
                        "date": message_time.isoformat(),
                        "signal_type": parsed_signal.get('signal_type'),
                        "symbol": parsed_signal.get('symbol')
                    })
                    
                except Exception as msg_error:
                    logger.error(f"处理消息时出错: {msg_error}")
                    skipped_count += 1
                    continue
            
            logger.info(f"历史消息拉取完成: 处理 {processed_count} 条，跳过 {skipped_count} 条，重复 {duplicate_count} 条，信号 {signal_count} 条")
            
            return {
                "success": True,
                "message": f"成功拉取 {signal_count} 条历史信号",
                "data": {
                    "total_processed": processed_count,
                    "signals_found": signal_count,
                    "skipped": skipped_count,
                    "duplicates": duplicate_count,
                    "messages": messages[:10]  # 只返回前10条作为预览
                }
            }
            
        except Exception as e:
            logger.error(f"拉取历史消息失败: {e}")
            return {
                "success": False,
                "error": f"拉取历史消息失败: {str(e)}"
            }
    
    def _contains_signal_keywords(self, text: str) -> bool:
        """检查消息是否包含交易信号关键词"""
        if not text:
            return False
        
        text_upper = text.upper()
        
        # 信号关键词
        signal_keywords = [
            'LONG', 'SHORT', 'BUY', 'SELL', 'ENTRY', 'TAKE PROFIT', 'TP', 'STOP LOSS', 'SL',
            'LEVERAGE', 'TARGET', 'USDT', '#', 'SIGNAL', 'CALL', 'POSITION',
            '做多', '做空', '买入', '卖出', '入场', '止盈', '止损', '杠杆', '目标'
        ]
        
        # 价格相关模式
        import re
        price_patterns = [
            r'\$\d+',  # $价格
            r'\d+\.\d+',  # 小数价格
            r'USDT',  # USDT交易对
            r'[A-Z]{2,10}/USDT',  # 交易对格式
            r'#[A-Z]+',  # #标签格式
        ]
        
        # 检查关键词
        for keyword in signal_keywords:
            if keyword in text_upper:
                return True
        
        # 检查价格模式
        for pattern in price_patterns:
            if re.search(pattern, text_upper):
                return True
        
        return False
    
    async def _is_message_duplicate(self, chat_id: str, message_id: str) -> bool:
        """检查消息是否已存在"""
        db = SessionLocal()
        try:
            # 获取信号源
            source = db.query(SignalSource).filter(
                SignalSource.telegram_chat_id == chat_id
            ).first()
            
            if not source:
                return False
            
            # 检查消息ID是否已存在
            existing = db.query(TelegramSignal).filter(
                TelegramSignal.source_id == source.id,
                TelegramSignal.message_id == message_id
            ).first()
            
            return existing is not None
            
        except Exception as e:
            logger.error(f"检查消息重复时出错: {e}")
            return False
        finally:
            db.close()
    
    async def _save_historical_signal(
        self,
        chat_id: str,
        message_id: str,
        message_text: str,
        message_time: datetime,
        parsed_signal: Dict[str, Any]
    ):
        """保存历史信号到数据库（不触发自动交易）"""
        db = SessionLocal()
        try:
            # 获取或创建信号源
            source = self._get_or_create_source(db, chat_id)
            
            # 使用信号管理器处理信号，但禁用自动执行
            from app.services.signal_manager import signal_manager
            
            # 临时禁用自动执行
            original_auto_execute = signal_manager.auto_execute_enabled
            signal_manager.auto_execute_enabled = False
            
            try:
                result = await signal_manager.process_raw_signal(
                    raw_message=message_text,
                    source_id=source.id,
                    message_id=message_id,
                    signal_time=message_time
                )
                
                if result:
                    logger.info(f"历史信号已保存: {result['signal_id']} - {parsed_signal.get('symbol')} {parsed_signal.get('signal_type')}")
                else:
                    logger.warning(f"信号管理器无法处理历史信号: {message_text[:100]}...")
            finally:
                # 恢复自动执行设置
                signal_manager.auto_execute_enabled = original_auto_execute
            
        except Exception as e:
            logger.error(f"保存历史信号时发生错误: {e}")
        finally:
            db.close()


# 全局实例
telegram_collector = TelegramCollector() 