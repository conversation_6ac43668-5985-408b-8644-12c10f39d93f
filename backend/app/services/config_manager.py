import json
import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.database import get_db
from app.models import SystemConfig, FreqtradeConfig, ConfigHistory, ConfigCategory, ConfigType
from app.schemas import ConfigValidationError, ConfigValidationResult


logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self._cache: Dict[str, Any] = {}
        self._initialized = False
    
    def initialize_default_configs(self, db: Session):
        """初始化默认配置"""
        if self._initialized:
            return
        
        # 删除旧的配置项
        old_config_keys = [
            "telegram_bot_token",
            "telegram_chat_ids"
        ]
        
        for key in old_config_keys:
            old_config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
            if old_config:
                db.delete(old_config)
                logger.info(f"已删除旧配置项: {key}")
        
        # 提交删除操作
        try:
            db.commit()
        except Exception as e:
            db.rollback()
            logger.error(f"删除旧配置项失败: {e}")
            
        default_configs = [
            # Telegram配置
            {
                "key": "telegram_api_id",
                "default_value": "",
                "description": "Telegram API ID（从my.telegram.org获取）",
                "category": ConfigCategory.TELEGRAM,
                "config_type": ConfigType.STRING,
                "is_required": True,
                "is_sensitive": False,
                "sort_order": 1
            },
            {
                "key": "telegram_api_hash",
                "default_value": "",
                "description": "Telegram API Hash（从my.telegram.org获取）",
                "category": ConfigCategory.TELEGRAM,
                "config_type": ConfigType.PASSWORD,
                "is_required": True,
                "is_sensitive": True,
                "sort_order": 2
            },
            {
                "key": "telegram_session_string",
                "default_value": "",
                "description": "Telegram会话字符串（登录后自动生成）",
                "category": ConfigCategory.TELEGRAM,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": True,
                "sort_order": 3
            },
            {
                "key": "telegram_phone_number",
                "default_value": "",
                "description": "Telegram手机号码（用于登录）",
                "category": ConfigCategory.TELEGRAM,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": True,
                "sort_order": 4
            },
            
            # Freqtrade配置
            {
                "key": "freqtrade_api_url",
                "default_value": "http://localhost:8080",
                "description": "Freqtrade API地址",
                "category": ConfigCategory.FREQTRADE,
                "config_type": ConfigType.STRING,
                "is_required": True,
                "is_sensitive": False,
                "sort_order": 1
            },
            {
                "key": "freqtrade_api_username",
                "default_value": "freqtrade",
                "description": "Freqtrade API用户名",
                "category": ConfigCategory.FREQTRADE,
                "config_type": ConfigType.STRING,
                "is_required": True,
                "is_sensitive": False,
                "sort_order": 2
            },
            {
                "key": "freqtrade_api_password",
                "default_value": "freqtrade",
                "description": "Freqtrade API密码",
                "category": ConfigCategory.FREQTRADE,
                "config_type": ConfigType.PASSWORD,
                "is_required": True,
                "is_sensitive": True,
                "sort_order": 3
            },
            
            # 交易所配置
            {
                "key": "binance_api_key",
                "default_value": "",
                "description": "Binance API Key",
                "category": ConfigCategory.EXCHANGE,
                "config_type": ConfigType.PASSWORD,
                "is_required": False,
                "is_sensitive": True,
                "sort_order": 1
            },
            {
                "key": "binance_secret_key",
                "default_value": "",
                "description": "Binance Secret Key",
                "category": ConfigCategory.EXCHANGE,
                "config_type": ConfigType.PASSWORD,
                "is_required": False,
                "is_sensitive": True,
                "sort_order": 2
            },
            
            # 日志配置
            {
                "key": "log_level",
                "default_value": "INFO",
                "description": "日志级别",
                "category": ConfigCategory.LOGGING,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "choices": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
                }),
                "sort_order": 1
            },
            {
                "key": "log_file",
                "default_value": "logs/app.log",
                "description": "日志文件路径",
                "category": ConfigCategory.LOGGING,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": False,
                "sort_order": 2
            },
            
            # JWT配置
            {
                "key": "access_token_expire_minutes",
                "default_value": "30",
                "description": "访问令牌过期时间（分钟）",
                "category": ConfigCategory.SYSTEM,
                "config_type": ConfigType.INTEGER,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "min": 1,
                    "max": 1440
                }),
                "sort_order": 1
            },
            
            # 交易配置
            {
                "key": "max_concurrent_trades",
                "default_value": "5",
                "description": "最大并发交易数",
                "category": ConfigCategory.TRADING,
                "config_type": ConfigType.INTEGER,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "min": 1,
                    "max": 100
                }),
                "sort_order": 1
            },
            {
                "key": "default_stake_amount",
                "default_value": "100",
                "description": "默认下注金额（USDT）",
                "category": ConfigCategory.TRADING,
                "config_type": ConfigType.FLOAT,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "min": 1.0,
                    "max": 10000.0
                }),
                "sort_order": 2
            },
            {
                "key": "auto_trading_enabled",
                "default_value": "false",
                "description": "是否启用自动交易",
                "category": ConfigCategory.TRADING,
                "config_type": ConfigType.BOOLEAN,
                "is_required": False,
                "is_sensitive": False,
                "sort_order": 3
            },
            
            # 回测配置
            {
                "key": "backtest_trade_amount_mode",
                "default_value": "percentage",
                "description": "回测交易金额模式",
                "category": ConfigCategory.TRADING,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "choices": ["fixed_amount", "percentage"]
                }),
                "sort_order": 4
            },
            {
                "key": "backtest_trade_amount_value",
                "default_value": "0.2",
                "description": "回测每笔交易金额/百分比（固定金额模式为USDT，百分比模式为0-1）",
                "category": ConfigCategory.TRADING,
                "config_type": ConfigType.FLOAT,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "min": 0.01,
                    "max": 10000.0
                }),
                "sort_order": 5
            },
            {
                "key": "backtest_max_positions",
                "default_value": "5",
                "description": "回测最大同时持仓数",
                "category": ConfigCategory.TRADING,
                "config_type": ConfigType.INTEGER,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "min": 1,
                    "max": 20
                }),
                "sort_order": 6
            },
            {
                "key": "backtest_max_allocation_percent",
                "default_value": "0.8",
                "description": "回测最大资金使用比例（0-1）",
                "category": ConfigCategory.TRADING,
                "config_type": ConfigType.FLOAT,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "min": 0.1,
                    "max": 1.0
                }),
                "sort_order": 7
            },
            
            # LLM配置
            {
                "key": "llm_provider",
                "default_value": "deepseek",
                "description": "LLM服务提供商",
                "category": ConfigCategory.LLM,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "choices": ["deepseek", "openai", "claude", "qwen"]
                }),
                "sort_order": 1
            },
            {
                "key": "deepseek_api_key",
                "default_value": "",
                "description": "DeepSeek API密钥",
                "category": ConfigCategory.LLM,
                "config_type": ConfigType.PASSWORD,
                "is_required": False,
                "is_sensitive": True,
                "sort_order": 2
            },
            {
                "key": "deepseek_base_url",
                "default_value": "https://api.deepseek.com",
                "description": "DeepSeek API基础URL",
                "category": ConfigCategory.LLM,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": False,
                "sort_order": 3
            },
            {
                "key": "deepseek_model",
                "default_value": "deepseek-chat",
                "description": "DeepSeek模型名称",
                "category": ConfigCategory.LLM,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "choices": ["deepseek-chat", "deepseek-coder"]
                }),
                "sort_order": 4
            },
            {
                "key": "llm_temperature",
                "default_value": "0.1",
                "description": "LLM生成温度（控制输出随机性）",
                "category": ConfigCategory.LLM,
                "config_type": ConfigType.FLOAT,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "min": 0.0,
                    "max": 2.0
                }),
                "sort_order": 5
            },
            {
                "key": "llm_max_tokens",
                "default_value": "1000",
                "description": "LLM最大输出Token数",
                "category": ConfigCategory.LLM,
                "config_type": ConfigType.INTEGER,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "min": 100,
                    "max": 4000
                }),
                "sort_order": 6
            },
            {
                "key": "signal_parser_type",
                "default_value": "builtin",
                "description": "信号解析器类型",
                "category": ConfigCategory.SYSTEM,
                "config_type": ConfigType.STRING,
                "is_required": False,
                "is_sensitive": False,
                "validation_rules": json.dumps({
                    "choices": ["builtin", "llm"]
                }),
                "sort_order": 10
            }
        ]
        
        for config_data in default_configs:
            existing_config = db.query(SystemConfig).filter(
                SystemConfig.key == config_data["key"]
            ).first()
            
            if not existing_config:
                config = SystemConfig(**config_data)
                db.add(config)
        
        try:
            db.commit()
            self._initialized = True
            logger.info("默认配置初始化完成")
        except Exception as e:
            db.rollback()
            logger.error(f"初始化默认配置失败: {e}")
            raise
    
    def get_config(self, key: str, db: Session, default: Any = None) -> Any:
        """获取配置值"""
        # 先从缓存获取
        if key in self._cache:
            return self._cache[key]
        
        # 从数据库获取
        config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
        if not config:
            return default
        
        value = config.value if config.value is not None else config.default_value
        if value is None:
            return default
        
        # 根据类型转换
        typed_value = self._convert_value(value, config.config_type)
        self._cache[key] = typed_value
        return typed_value
    
    def set_config(self, key: str, value: Any, db: Session, user_id: str = None, reason: str = None) -> bool:
        """设置配置值"""
        config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
        if not config:
            logger.error(f"配置项不存在: {key}")
            return False
        
        if config.is_readonly:
            logger.error(f"配置项只读: {key}")
            return False
        
        # 验证配置值
        validation_result = self._validate_config_value(config, value)
        if not validation_result.is_valid:
            logger.error(f"配置值验证失败: {key}, 错误: {validation_result.errors}")
            return False
        
        old_value = config.value
        new_value = str(value) if value is not None else None
        
        # 记录变更历史
        if old_value != new_value:
            history = ConfigHistory(
                config_key=key,
                old_value=old_value,
                new_value=new_value,
                changed_by=user_id,
                change_reason=reason
            )
            db.add(history)
        
        # 更新配置
        config.value = new_value
        
        try:
            db.commit()
            # 更新缓存
            self._cache[key] = self._convert_value(new_value, config.config_type)
            logger.info(f"配置更新成功: {key}")
            return True
        except Exception as e:
            db.rollback()
            logger.error(f"配置更新失败: {key}, 错误: {e}")
            return False
    
    def get_configs_by_category(self, category: ConfigCategory, db: Session, include_sensitive: bool = False) -> List[SystemConfig]:
        """根据分类获取配置"""
        query = db.query(SystemConfig).filter(SystemConfig.category == category)
        
        if not include_sensitive:
            query = query.filter(SystemConfig.is_sensitive == False)
        
        return query.order_by(SystemConfig.sort_order).all()
    
    def get_all_configs(self, db: Session, include_sensitive: bool = False) -> Dict[str, List[SystemConfig]]:
        """获取所有配置，按分类分组"""
        query = db.query(SystemConfig)
        
        if not include_sensitive:
            query = query.filter(SystemConfig.is_sensitive == False)
        
        configs = query.order_by(SystemConfig.category, SystemConfig.sort_order).all()
        
        result = {}
        for config in configs:
            category = config.category.value
            if category not in result:
                result[category] = []
            result[category].append(config)
        
        return result
    
    def validate_configs(self, updates: Dict[str, Any], db: Session) -> ConfigValidationResult:
        """验证配置值（不实际更新）"""
        errors = []
        
        # 验证所有配置
        for key, value in updates.items():
            config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
            if not config:
                errors.append(ConfigValidationError(
                    key=key,
                    error="配置项不存在",
                    current_value=None
                ))
                continue
            
            if config.is_readonly:
                errors.append(ConfigValidationError(
                    key=key,
                    error="配置项只读",
                    current_value=config.value
                ))
                continue
            
            validation_result = self._validate_config_value(config, value)
            if not validation_result.is_valid:
                errors.extend(validation_result.errors)
        
        return ConfigValidationResult(is_valid=len(errors) == 0, errors=errors)
    
    def bulk_update_configs(self, updates: Dict[str, Any], db: Session, user_id: str = None, reason: str = None) -> ConfigValidationResult:
        """批量更新配置"""
        # 先验证所有配置
        validation_result = self.validate_configs(updates, db)
        if not validation_result.is_valid:
            return validation_result
        
        # 执行更新
        try:
            for key, value in updates.items():
                config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
                old_value = config.value
                new_value = str(value) if value is not None else None
                
                if old_value != new_value:
                    # 记录历史
                    history = ConfigHistory(
                        config_key=key,
                        old_value=old_value,
                        new_value=new_value,
                        changed_by=user_id,
                        change_reason=reason
                    )
                    db.add(history)
                    
                    # 更新配置
                    config.value = new_value
                    
                    # 更新缓存
                    self._cache[key] = self._convert_value(new_value, config.config_type)
            
            db.commit()
            logger.info(f"批量配置更新成功，共更新{len(updates)}项配置")
            return ConfigValidationResult(is_valid=True, errors=[])
            
        except Exception as e:
            db.rollback()
            logger.error(f"批量配置更新失败: {e}")
            return ConfigValidationResult(
                is_valid=False,
                errors=[ConfigValidationError(key="system", error=f"更新失败: {str(e)}", current_value=None)]
            )
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        logger.info("配置缓存已清空")
    
    def _convert_value(self, value: str, config_type: ConfigType) -> Any:
        """根据配置类型转换值"""
        if value is None:
            return None
        
        try:
            if config_type == ConfigType.INTEGER:
                return int(value)
            elif config_type == ConfigType.FLOAT:
                return float(value)
            elif config_type == ConfigType.BOOLEAN:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif config_type == ConfigType.JSON:
                return json.loads(value)
            elif config_type == ConfigType.LIST:
                return [item.strip() for item in value.split(',') if item.strip()]
            else:
                return value
        except (ValueError, json.JSONDecodeError) as e:
            logger.error(f"配置值转换失败: {value}, 期望类型: {config_type}, 错误: {e}")
            return value
    
    def _validate_config_value(self, config: SystemConfig, value: Any) -> ConfigValidationResult:
        """验证配置值"""
        errors = []
        
        # 必需项检查
        if config.is_required and (value is None or str(value).strip() == ""):
            errors.append(ConfigValidationError(
                key=config.key,
                error="此配置项为必需项",
                current_value=str(value) if value is not None else None
            ))
            return ConfigValidationResult(is_valid=False, errors=errors)
        
        # 如果有验证规则
        if config.validation_rules:
            try:
                rules = json.loads(config.validation_rules)
                
                # 选择项验证
                if "choices" in rules and value not in rules["choices"]:
                    errors.append(ConfigValidationError(
                        key=config.key,
                        error=f"值必须为以下之一: {', '.join(rules['choices'])}",
                        current_value=str(value)
                    ))
                
                # 数值范围验证
                if config.config_type in [ConfigType.INTEGER, ConfigType.FLOAT]:
                    num_value = float(value) if config.config_type == ConfigType.FLOAT else int(value)
                    
                    if "min" in rules and num_value < rules["min"]:
                        errors.append(ConfigValidationError(
                            key=config.key,
                            error=f"值不能小于 {rules['min']}",
                            current_value=str(value)
                        ))
                    
                    if "max" in rules and num_value > rules["max"]:
                        errors.append(ConfigValidationError(
                            key=config.key,
                            error=f"值不能大于 {rules['max']}",
                            current_value=str(value)
                        ))
                
                # 字符串长度验证
                elif config.config_type == ConfigType.STRING:
                    if "min_length" in rules and len(str(value)) < rules["min_length"]:
                        errors.append(ConfigValidationError(
                            key=config.key,
                            error=f"长度不能少于 {rules['min_length']} 个字符",
                            current_value=str(value)
                        ))
                    
                    if "max_length" in rules and len(str(value)) > rules["max_length"]:
                        errors.append(ConfigValidationError(
                            key=config.key,
                            error=f"长度不能超过 {rules['max_length']} 个字符",
                            current_value=str(value)
                        ))
                
            except json.JSONDecodeError:
                logger.error(f"配置验证规则格式错误: {config.key}")
        
        return ConfigValidationResult(is_valid=len(errors) == 0, errors=errors)


# 全局配置管理器实例
config_manager = ConfigManager() 