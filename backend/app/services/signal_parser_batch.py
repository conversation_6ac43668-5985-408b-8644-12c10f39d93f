import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from app.models import SignalType
from app.services.signal_parser import EnhancedSignalParser
from app.services.llm_signal_parser import LLMSignalParserFactory
from app.services.config_manager import config_manager

logger = logging.getLogger(__name__)


class BatchSignalParser:
    """批量信号解析器 - 优化批量处理性能"""
    
    def __init__(self, db):
        self.db = db
        self.builtin_parser = EnhancedSignalParser()
        self.llm_parser = None
        
    async def parse_messages_batch(self, messages: List[str], parser_type: str = None) -> List[Optional[Dict[str, Any]]]:
        """高效批量解析信号"""
        try:
            if not messages:
                return []
            
            # 确定使用的解析器类型
            if parser_type is None:
                parser_type = config_manager.get_config("signal_parser_type", self.db, "builtin")
            
            logger.info(f"[BATCH_PARSER] 批量解析 {len(messages)} 个信号，使用解析器: {parser_type}")
            
            if parser_type == "llm":
                return await self._parse_with_llm_batch(messages)
            else:
                return await self._parse_with_builtin_batch(messages)
                
        except Exception as e:
            logger.error(f"[BATCH_PARSER] 批量解析失败: {e}")
            return [None] * len(messages)
    
    async def _parse_with_llm_batch(self, messages: List[str]) -> List[Optional[Dict[str, Any]]]:
        """使用LLM解析器批量处理"""
        try:
            # 创建或获取LLM解析器
            if not self.llm_parser:
                self.llm_parser = LLMSignalParserFactory.create_parser(self.db)
            
            # 使用LLM解析器的批量方法
            return await self.llm_parser.parse_messages(messages)
            
        except Exception as e:
            logger.error(f"[BATCH_PARSER] LLM批量解析失败: {e}")
            return [None] * len(messages)
    
    async def _parse_with_builtin_batch(self, messages: List[str]) -> List[Optional[Dict[str, Any]]]:
        """使用内置解析器批量处理（并行优化）"""
        try:
            # 对于内置解析器，我们可以并行处理以提高效率
            semaphore = asyncio.Semaphore(10)  # 限制并发数
            
            async def parse_single(message: str) -> Optional[Dict[str, Any]]:
                async with semaphore:
                    try:
                        # 检查状态更新消息
                        if self.builtin_parser.is_status_update_message(message):
                            return None
                        
                        # 解析信号
                        return await self._parse_single_builtin(message)
                    except Exception as e:
                        logger.error(f"[BATCH_PARSER] 单个信号解析失败: {e}")
                        return None
            
            # 并行处理所有消息
            tasks = [parse_single(message) for message in messages]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            final_results = []
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"[BATCH_PARSER] 解析任务异常: {result}")
                    final_results.append(None)
                else:
                    final_results.append(result)
            
            return final_results
            
        except Exception as e:
            logger.error(f"[BATCH_PARSER] 内置批量解析失败: {e}")
            return [None] * len(messages)
    
    async def _parse_single_builtin(self, message: str) -> Optional[Dict[str, Any]]:
        """解析单个信号（内置解析器）"""
        try:
            # 先尝试用原始消息解析（保留emoji）
            general_result = await self.builtin_parser._parse_general_format(message)
            if general_result:
                return general_result
            
            # 清理消息文本后再尝试
            cleaned_message = self.builtin_parser._clean_message(message)
            
            # 尝试Cornix格式解析
            cornix_result = await self.builtin_parser._parse_cornix_format(cleaned_message)
            if cornix_result:
                return cornix_result
            
            # 通用格式解析（清理后的消息）
            general_result_cleaned = await self.builtin_parser._parse_general_format(cleaned_message)
            if general_result_cleaned:
                return general_result_cleaned
            
            return None
            
        except Exception as e:
            logger.error(f"[BATCH_PARSER] 单个内置解析失败: {e}")
            return None


# 全局批量解析器工厂
class BatchSignalParserFactory:
    _instances = {}
    
    @classmethod
    def get_parser(cls, db) -> BatchSignalParser:
        """获取批量解析器实例（复用连接）"""
        db_id = id(db)
        if db_id not in cls._instances:
            cls._instances[db_id] = BatchSignalParser(db)
        return cls._instances[db_id]
    
    @classmethod
    def clear_cache(cls):
        """清理缓存的解析器实例"""
        cls._instances.clear() 