from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from app.database import get_db
from app.models import TelegramSignal, SignalSource, SignalStatus, SignalType
from app.schemas import APIResponse, PaginationParams
from app.services.signal_manager import signal_manager
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()


class SignalExecuteRequest(BaseModel):
    """手动执行信号请求"""
    signal_id: int
    force: bool = False


class SignalFilterRequest(BaseModel):
    """信号过滤请求"""
    symbol: Optional[str] = None
    signal_type: Optional[str] = None
    status: Optional[str] = None
    source_id: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    search: Optional[str] = None


class ProcessRawSignalRequest(BaseModel):
    """处理原始信号请求"""
    raw_message: str
    source_id: int
    message_id: Optional[str] = None
    signal_time: Optional[datetime] = None


class AutoExecuteSettingRequest(BaseModel):
    """自动执行设置请求"""
    enabled: bool


@router.get("/", response_model=APIResponse)
async def get_signals(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    symbol: Optional[str] = Query(None, description="交易对符号"),
    signal_type: Optional[str] = Query(None, description="信号类型"),
    status: Optional[str] = Query(None, description="信号状态"),
    source_id: Optional[int] = Query(None, description="信号源ID"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """获取信号列表"""
    try:
        filters = {
            'symbol': symbol,
            'signal_type': signal_type,
            'status': status,
            'source_id': source_id,
            'start_date': start_date,
            'end_date': end_date,
            'search': search
        }
        
        # 移除空值
        filters = {k: v for k, v in filters.items() if v is not None}
        
        result = await signal_manager.get_signal_list(filters, page, page_size)
        
        if result['success']:
            return APIResponse(
                success=True,
                message="获取信号列表成功",
                data=result['data']
            )
        else:
            raise HTTPException(status_code=500, detail=result['message'])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取信号列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取信号列表失败")


@router.get("/statistics", response_model=APIResponse)
async def get_signal_statistics(
    days: int = Query(30, ge=1, le=365, description="统计天数")
):
    """获取信号统计信息"""
    try:
        result = await signal_manager.get_signal_statistics(days)
        
        if result['success']:
            return APIResponse(
                success=True,
                message="获取信号统计成功",
                data=result['data']
            )
        else:
            raise HTTPException(status_code=500, detail=result['message'])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取信号统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取信号统计失败")


@router.post("/process-raw", response_model=APIResponse)
async def process_raw_signal(
    request: ProcessRawSignalRequest,
    background_tasks: BackgroundTasks
):
    """处理原始信号文本"""
    try:
        result = await signal_manager.process_raw_signal(
            raw_message=request.raw_message,
            source_id=request.source_id,
            message_id=request.message_id,
            signal_time=request.signal_time
        )
        
        if result:
            return APIResponse(
                success=True,
                message="信号处理成功",
                data=result
            )
        else:
            return APIResponse(
                success=False,
                message="无法解析该文本为有效信号"
            )
            
    except Exception as e:
        logger.error(f"处理原始信号失败: {e}")
        raise HTTPException(status_code=500, detail="处理信号失败")


@router.post("/execute", response_model=APIResponse)
async def execute_signal(request: SignalExecuteRequest):
    """手动执行信号"""
    try:
        result = await signal_manager.manual_execute_signal(request.signal_id)
        
        if result['success']:
            return APIResponse(
                success=True,
                message="信号执行成功",
                data=result
            )
        else:
            return APIResponse(
                success=False,
                message=result['message']
            )
            
    except Exception as e:
        logger.error(f"执行信号失败: {e}")
        raise HTTPException(status_code=500, detail="执行信号失败")


@router.post("/batch-execute", response_model=APIResponse)
async def batch_execute_signals(
    signal_ids: List[int],
    background_tasks: BackgroundTasks
):
    """批量执行信号"""
    try:
        results = []
        
        for signal_id in signal_ids:
            result = await signal_manager.manual_execute_signal(signal_id)
            results.append({
                'signal_id': signal_id,
                'success': result['success'],
                'message': result['message']
            })
        
        success_count = sum(1 for r in results if r['success'])
        
        return APIResponse(
            success=True,
            message=f"批量执行完成，成功: {success_count}/{len(signal_ids)}",
            data={
                'results': results,
                'success_count': success_count,
                'total_count': len(signal_ids)
            }
        )
        
    except Exception as e:
        logger.error(f"批量执行信号失败: {e}")
        raise HTTPException(status_code=500, detail="批量执行失败")


@router.get("/auto-execute/status", response_model=APIResponse)
async def get_auto_execute_status():
    """获取自动执行状态"""
    try:
        status = signal_manager.get_auto_execute_status()
        
        return APIResponse(
            success=True,
            message="获取自动执行状态成功",
            data={
                'enabled': status,
                'description': '信号将自动执行' if status else '信号需要手动执行'
            }
        )
        
    except Exception as e:
        logger.error(f"获取自动执行状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取状态失败")


@router.post("/auto-execute/toggle", response_model=APIResponse)
async def toggle_auto_execute(request: AutoExecuteSettingRequest):
    """切换自动执行开关"""
    try:
        signal_manager.set_auto_execute(request.enabled)
        
        return APIResponse(
            success=True,
            message=f"自动执行已{'启用' if request.enabled else '禁用'}",
            data={
                'enabled': request.enabled,
                'description': '信号将自动执行' if request.enabled else '信号需要手动执行'
            }
        )
        
    except Exception as e:
        logger.error(f"切换自动执行失败: {e}")
        raise HTTPException(status_code=500, detail="切换失败")


@router.get("/sources/active", response_model=APIResponse)
async def get_active_signal_sources(db: Session = Depends(get_db)):
    """获取活跃的信号源列表"""
    try:
        sources = db.query(SignalSource).filter(SignalSource.is_active == True).all()
        
        source_list = []
        for source in sources:
            source_data = {
                'id': source.id,
                'name': source.name or source.description or source.telegram_chat_id,
                'telegram_chat_id': source.telegram_chat_id,
                'description': source.description,
                'total_signals': source.total_signals,
                'successful_signals': source.successful_signals,
                'reliability_score': source.reliability_score,
                'created_at': source.created_at.isoformat() if source.created_at else None
            }
            source_list.append(source_data)
        
        return APIResponse(
            success=True,
            message="获取活跃信号源成功",
            data=source_list
        )
        
    except Exception as e:
        logger.error(f"获取活跃信号源失败: {e}")
        raise HTTPException(status_code=500, detail="获取信号源失败")


@router.get("/types", response_model=APIResponse)
async def get_signal_types():
    """获取信号类型列表"""
    try:
        signal_types = [
            {'value': 'long', 'label': '做多', 'description': '买入开多仓'},
            {'value': 'short', 'label': '做空', 'description': '卖出开空仓'},
            {'value': 'buy', 'label': '买入', 'description': '现货买入'},
            {'value': 'sell', 'label': '卖出', 'description': '现货卖出'},
            {'value': 'close', 'label': '平仓', 'description': '平仓操作'}
        ]
        
        return APIResponse(
            success=True,
            message="获取信号类型成功",
            data=signal_types
        )
        
    except Exception as e:
        logger.error(f"获取信号类型失败: {e}")
        raise HTTPException(status_code=500, detail="获取信号类型失败")


@router.get("/status-options", response_model=APIResponse)
async def get_signal_status_options():
    """获取信号状态选项"""
    try:
        status_options = [
            {'value': 'pending', 'label': '等待中', 'description': '信号已创建，等待执行'},
            {'value': 'processing', 'label': '处理中', 'description': '信号正在处理'},
            {'value': 'executed', 'label': '已执行', 'description': '信号已成功执行'},
            {'value': 'failed', 'label': '执行失败', 'description': '信号执行失败'},
            {'value': 'cancelled', 'label': '已取消', 'description': '信号已被取消'},
            {'value': 'active', 'label': '进行中', 'description': '交易正在进行'},
            {'value': 'completed', 'label': '已完成', 'description': '交易已完成'}
        ]
        
        return APIResponse(
            success=True,
            message="获取信号状态选项成功",
            data=status_options
        )
        
    except Exception as e:
        logger.error(f"获取信号状态选项失败: {e}")
        raise HTTPException(status_code=500, detail="获取状态选项失败")


@router.delete("/{signal_id}", response_model=APIResponse)
async def delete_signal(signal_id: int, db: Session = Depends(get_db)):
    """删除信号"""
    try:
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        
        if not signal:
            raise HTTPException(status_code=404, detail="信号不存在")
        
        # 检查信号状态，只允许删除未执行的信号
        if signal.status in [SignalStatus.PROCESSING, SignalStatus.EXECUTED]:
            raise HTTPException(status_code=400, detail="无法删除正在处理或已执行的信号")
        
        db.delete(signal)
        db.commit()
        
        return APIResponse(
            success=True,
            message="信号删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除信号失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="删除信号失败")


@router.put("/{signal_id}/status", response_model=APIResponse)
async def update_signal_status(
    signal_id: int,
    status: str,
    db: Session = Depends(get_db)
):
    """更新信号状态"""
    try:
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        
        if not signal:
            raise HTTPException(status_code=404, detail="信号不存在")
        
        # 验证状态值
        try:
            new_status = SignalStatus(status)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的状态值: {status}")
        
        signal.status = new_status
        if new_status == SignalStatus.EXECUTED:
            signal.processed_at = datetime.utcnow()
        
        db.commit()
        
        return APIResponse(
            success=True,
            message="信号状态更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新信号状态失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="更新状态失败")


@router.get("/{signal_id}/detail", response_model=APIResponse)
async def get_signal_detail(signal_id: int, db: Session = Depends(get_db)):
    """获取信号详情"""
    try:
        from app.models import TradeEntryPoint, TradeTakeProfitPoint, SignalTrade
        
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        
        if not signal:
            raise HTTPException(status_code=404, detail="信号不存在")
        
        # 获取入场点
        entry_points = db.query(TradeEntryPoint).filter(
            TradeEntryPoint.signal_id == signal_id
        ).all()
        
        # 获取止盈点
        tp_points = db.query(TradeTakeProfitPoint).filter(
            TradeTakeProfitPoint.signal_id == signal_id
        ).all()
        
        # 获取交易记录
        trades = db.query(SignalTrade).filter(
            SignalTrade.signal_id == signal_id
        ).all()
        
        # 计算统计信息
        total_pnl = sum(tp.pnl or 0 for tp in tp_points)
        total_entry_filled = sum(ep.filled_quantity or 0 for ep in entry_points)
        total_tp_filled = sum(tp.filled_quantity or 0 for tp in tp_points)
        
        signal_detail = {
            'signal': {
                'id': signal.id,
                'symbol': signal.symbol,
                'signal_type': signal.signal_type.value,
                'entry_price': signal.entry_price,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'leverage': signal.leverage,
                'status': signal.status.value,
                'source_id': signal.source_id,
                'signal_time': signal.signal_time.isoformat(),
                'received_at': signal.received_at.isoformat(),
                'processed_at': signal.processed_at.isoformat() if signal.processed_at else None,
                'raw_message': signal.raw_message
            },
            'entry_points': [
                {
                    'id': ep.id,
                    'price': ep.price,
                    'allocation': ep.allocation,
                    'description': ep.description,
                    'status': ep.status,
                    'filled_quantity': ep.filled_quantity,
                    'actual_price': ep.actual_price,
                    'order_id': ep.order_id,
                    'created_at': ep.created_at.isoformat(),
                    'filled_at': ep.filled_at.isoformat() if ep.filled_at else None
                }
                for ep in entry_points
            ],
            'take_profit_points': [
                {
                    'id': tp.id,
                    'price': tp.price,
                    'allocation': tp.allocation,
                    'description': tp.description,
                    'status': tp.status,
                    'filled_quantity': tp.filled_quantity,
                    'actual_price': tp.actual_price,
                    'order_id': tp.order_id,
                    'pnl': tp.pnl,
                    'pnl_percentage': tp.pnl_percentage,
                    'created_at': tp.created_at.isoformat(),
                    'filled_at': tp.filled_at.isoformat() if tp.filled_at else None
                }
                for tp in tp_points
            ],
            'trades': [
                {
                    'id': trade.id,
                    'trade_id': trade.trade_id,
                    'order_id': trade.order_id,
                    'symbol': trade.symbol,
                    'side': trade.side,
                    'entry_price': trade.entry_price,
                    'exit_price': trade.exit_price,
                    'quantity': trade.quantity,
                    'pnl': trade.pnl,
                    'pnl_percentage': trade.pnl_percentage,
                    'fee': trade.fee,
                    'status': trade.status,
                    'entry_time': trade.entry_time.isoformat() if trade.entry_time else None,
                    'exit_time': trade.exit_time.isoformat() if trade.exit_time else None
                }
                for trade in trades
            ],
            'statistics': {
                'total_pnl': total_pnl,
                'total_entry_filled': total_entry_filled,
                'total_tp_filled': total_tp_filled,
                'entry_points_count': len(entry_points),
                'tp_points_count': len(tp_points),
                'trades_count': len(trades)
            }
        }
        
        return APIResponse(
            success=True,
            message="获取信号详情成功",
            data=signal_detail
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取信号详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取信号详情失败")