from fastapi import APIRouter, Request, HTTPException
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import SignalTrade, TradeTakeProfitPoint, TradeEntryPoint
from app.services.cornix_signal_executor import cornix_executor
import logging

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/webhook", tags=["Webhook"])


@router.post("/freqtrade")
async def freqtrade_webhook(request: Request, db: Session = get_db()):
    """接收Freqtrade的订单状态回调"""
    try:
        data = await request.json()
        event_type = data.get("type")
        
        if event_type == "buy_fill":
            await handle_buy_fill(data, db)
        elif event_type == "sell_fill":
            await handle_sell_fill(data, db)
        elif event_type == "buy_cancel":
            await handle_order_cancel(data, db)
        elif event_type == "sell_cancel":
            await handle_order_cancel(data, db)
        
        return {"status": "ok"}
        
    except Exception as e:
        logger.error(f"处理Freqtrade webhook失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def handle_buy_fill(data: dict, db: Session):
    """处理买入成交"""
    pair = data.get("pair")
    price = data.get("price")
    amount = data.get("amount")
    
    # 更新入场点状态
    entry_point = db.query(TradeEntryPoint).filter(
        TradeEntryPoint.price == price,
        TradeEntryPoint.signal.has(symbol=pair)
    ).first()
    
    if entry_point:
        entry_point.status = "filled"
        entry_point.filled_quantity = amount
        entry_point.actual_price = price
        db.commit()
        
        # 触发止盈点设置
        await cornix_executor.activate_take_profit_points(entry_point.signal, db)
    
    logger.info(f"买入成交: {pair} @ {price}, 数量: {amount}")


async def handle_sell_fill(data: dict, db: Session):
    """处理卖出成交（止盈）"""
    pair = data.get("pair")
    price = data.get("price")
    amount = data.get("amount")
    
    # 更新止盈点状态
    tp_point = db.query(TradeTakeProfitPoint).filter(
        TradeTakeProfitPoint.price == price,
        TradeTakeProfitPoint.signal.has(symbol=pair)
    ).first()
    
    if tp_point:
        tp_point.status = "filled"
        tp_point.filled_quantity = amount
        tp_point.actual_price = price
        # 计算盈亏
        tp_point.pnl = (price - tp_point.signal.entry_price) * amount
        db.commit()
    
    logger.info(f"止盈成交: {pair} @ {price}, 数量: {amount}")


async def handle_order_cancel(data: dict, db: Session):
    """处理订单取消"""
    pair = data.get("pair")
    price = data.get("price")
    
    # 更新相关订单状态为取消
    logger.info(f"订单取消: {pair} @ {price}")