from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import json

from app.database import get_db
from app.models import SystemConfig, ConfigHistory, ConfigCategory
from app.schemas import (
    SystemConfigCreate, SystemConfigUpdate, SystemConfigResponse, SystemConfigPublic,
    ConfigCategoryResponse, ConfigHistoryResponse, ConfigUpdateRequest,
    ConfigValidationResult, APIResponse
)
from app.services.config_manager import config_manager


router = APIRouter(prefix="/config", tags=["配置管理"])


@router.get("/categories", response_model=List[ConfigCategoryResponse])
async def get_config_categories(
    include_sensitive: bool = False,
    db: Session = Depends(get_db)
):
    """获取所有配置分类"""
    try:
        configs_by_category = config_manager.get_all_configs(db, include_sensitive)
        
        result = []
        for category, configs in configs_by_category.items():
            config_list = []
            for config in configs:
                config_dict = {
                    "id": config.id,
                    "key": config.key,
                    "description": config.description,
                    "category": config.category.value,
                    "config_type": config.config_type.value,
                    "is_required": config.is_required,
                    "is_sensitive": config.is_sensitive,
                    "validation_rules": config.validation_rules,
                    "sort_order": config.sort_order
                }
                
                # 处理敏感信息
                if config.is_sensitive and not include_sensitive:
                    config_dict["value"] = "***" if config.value else None
                else:
                    config_dict["value"] = config.value if config.value is not None else config.default_value
                
                config_list.append(SystemConfigPublic(**config_dict))
            
            result.append(ConfigCategoryResponse(
                category=category,
                configs=config_list
            ))
        
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置分类失败: {str(e)}"
        )


@router.get("/category/{category}", response_model=List[SystemConfigPublic])
async def get_configs_by_category(
    category: str,
    include_sensitive: bool = False,
    db: Session = Depends(get_db)
):
    """根据分类获取配置"""
    try:
        category_enum = ConfigCategory(category)
        configs = config_manager.get_configs_by_category(category_enum, db, include_sensitive)
        
        result = []
        for config in configs:
            config_dict = {
                "id": config.id,
                "key": config.key,
                "description": config.description,
                "category": config.category.value,
                "config_type": config.config_type.value,
                "is_required": config.is_required,
                "is_sensitive": config.is_sensitive,
                "validation_rules": config.validation_rules,
                "sort_order": config.sort_order
            }
            
            # 处理敏感信息
            if config.is_sensitive and not include_sensitive:
                config_dict["value"] = "***" if config.value else None
            else:
                config_dict["value"] = config.value if config.value is not None else config.default_value
            
            result.append(SystemConfigPublic(**config_dict))
        
        return result
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的配置分类: {category}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置失败: {str(e)}"
        )


@router.get("/{config_key}", response_model=SystemConfigResponse)
async def get_config(
    config_key: str,
    db: Session = Depends(get_db)
):
    """获取单个配置"""
    config = db.query(SystemConfig).filter(SystemConfig.key == config_key).first()
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"配置项不存在: {config_key}"
        )
    
    return config


@router.post("/bulk-update", response_model=APIResponse)
async def bulk_update_configs(
    request: ConfigUpdateRequest,
    user_id: str = "system",  # TODO: 从JWT中获取用户ID
    db: Session = Depends(get_db)
):
    """批量更新配置"""
    try:
        result = config_manager.bulk_update_configs(
            request.configs, 
            db, 
            user_id, 
            request.change_reason
        )
        
        if result.is_valid:
            return APIResponse(
                success=True,
                message=f"成功更新{len(request.configs)}项配置",
                data={"updated_count": len(request.configs)}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"配置验证失败: {result.errors}"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新配置失败: {str(e)}"
        )


@router.put("/{config_key}", response_model=APIResponse)
async def update_config(
    config_key: str,
    value: str,
    reason: str = None,
    user_id: str = "system",
    db: Session = Depends(get_db)
):
    """更新单个配置"""
    try:
        success = config_manager.set_config(config_key, value, db, user_id, reason)
        if success:
            return APIResponse(
                success=True,
                message=f"配置更新成功: {config_key}"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"配置更新失败: {config_key}"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新配置失败: {str(e)}"
        )


@router.get("/{config_key}/history", response_model=List[ConfigHistoryResponse])
async def get_config_history(
    config_key: str,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """获取配置变更历史"""
    try:
        history = db.query(ConfigHistory).filter(
            ConfigHistory.config_key == config_key
        ).order_by(
            ConfigHistory.timestamp.desc()
        ).limit(limit).all()
        
        return history
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置历史失败: {str(e)}"
        )


@router.post("/validate", response_model=ConfigValidationResult)
async def validate_configs(
    configs: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """验证配置值"""
    try:
        # 只验证配置，不实际更新
        result = config_manager.validate_configs(configs, db)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"配置验证失败: {str(e)}"
        )


@router.post("/cache/clear", response_model=APIResponse)
async def clear_config_cache():
    """清空配置缓存"""
    try:
        config_manager.clear_cache()
        return APIResponse(
            success=True,
            message="配置缓存已清空"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清空缓存失败: {str(e)}"
        )


 