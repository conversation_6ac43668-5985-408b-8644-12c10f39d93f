from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from datetime import datetime, timedelta
from typing import List, Optional
import logging

from app.database import get_db
from app.schemas import APIResponse
from app.services.market_data import market_data_service

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/market-data", tags=["market-data"])


@router.get("/statistics", response_model=APIResponse)
async def get_kline_statistics():
    """获取K线数据统计信息"""
    try:
        statistics = await market_data_service.get_data_statistics()
        return APIResponse(
            success=True,
            message="获取统计信息成功",
            data=statistics
        )
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")


@router.post("/download-klines", response_model=APIResponse)
async def download_klines(
    symbols: List[str],
    interval: str = "1h",
    days_back: int = 30,
    background_tasks: BackgroundTasks = None
):
    """批量下载K线数据"""
    try:
        if background_tasks:
            # 异步下载
            background_tasks.add_task(
                market_data_service.batch_download_klines,
                symbols, interval, days_back
            )
            return APIResponse(
                success=True,
                message=f"已启动 {len(symbols)} 个交易对的K线数据下载任务",
                data={"symbols": symbols, "interval": interval, "days_back": days_back}
            )
        else:
            # 同步下载
            results = await market_data_service.batch_download_klines(symbols, interval, days_back)
            success_count = sum(1 for success in results.values() if success)
            
            return APIResponse(
                success=True,
                message=f"K线数据下载完成: {success_count}/{len(symbols)} 成功",
                data=results
            )
            
    except Exception as e:
        logger.error(f"下载K线数据失败: {e}")
        raise HTTPException(status_code=500, detail="下载K线数据失败")


@router.get("/klines/{symbol}", response_model=APIResponse)
async def get_symbol_klines(
    symbol: str,
    interval: str = Query("5m", description="时间间隔"),  # 改为5分钟默认
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    limit: int = Query(1000, description="返回数量限制"),
    force_download: bool = Query(False, description="强制从交易所下载")
):
    """获取指定交易对的K线数据"""
    try:
        # 设置默认时间范围
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        klines = await market_data_service.get_klines(
            symbol=symbol.upper(),
            interval=interval,
            start_time=start_date,
            end_time=end_date,
            limit=limit,
            force_download=force_download
        )
        
        if klines.empty:
            return APIResponse(
                success=False,
                message="未找到K线数据",
                data={"symbol": symbol, "count": 0}
            )
        
        # 转换为JSON格式
        klines_data = klines.to_dict('records')
        for record in klines_data:
            if 'timestamp' in record:
                record['timestamp'] = record['timestamp'].isoformat()
        
        return APIResponse(
            success=True,
            message="获取K线数据成功",
            data={
                "symbol": symbol,
                "interval": interval,
                "count": len(klines_data),
                "start_time": start_date.isoformat(),
                "end_time": end_date.isoformat(),
                "klines": klines_data[:100]  # 只返回前100条用于展示
            }
        )
        
    except Exception as e:
        logger.error(f"获取K线数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取K线数据失败")


@router.get("/current-price/{symbol}")
async def get_current_price(symbol: str):
    """获取指定交易对的当前价格"""
    try:
        price = await market_data_service.get_current_price(symbol.upper())
        
        if price is None:
            raise HTTPException(status_code=404, detail="未找到价格数据")
        
        return {
            "symbol": symbol.upper(),
            "price": price,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取当前价格失败: {e}")
        raise HTTPException(status_code=500, detail="获取当前价格失败")


@router.post("/check-data-completeness", response_model=APIResponse)
async def check_data_completeness(
    symbol: str,
    interval: str = "5m",  # 改为5分钟默认
    start_date: datetime = None,
    end_date: datetime = None
):
    """检查K线数据完整性"""
    try:
        # 设置默认时间范围
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=7)
        
        # 获取现有数据
        existing_data = await market_data_service._get_klines_from_db(
            symbol.upper(), interval, start_date, end_date
        )
        
        # 检查完整性
        missing_ranges = await market_data_service._check_data_completeness(
            symbol.upper(), interval, start_date, end_date, existing_data
        )
        
        return APIResponse(
            success=True,
            message="数据完整性检查完成",
            data={
                "symbol": symbol.upper(),
                "interval": interval,
                "existing_count": len(existing_data),
                "missing_ranges_count": len(missing_ranges),
                "missing_ranges": [
                    {
                        "start": start.isoformat(),
                        "end": end.isoformat()
                    }
                    for start, end in missing_ranges
                ],
                "completeness_percentage": round(
                    (1 - len(missing_ranges) / max(1, len(existing_data) + len(missing_ranges))) * 100, 2
                ) if missing_ranges else 100
            }
        )
        
    except Exception as e:
        logger.error(f"检查数据完整性失败: {e}")
        raise HTTPException(status_code=500, detail="检查数据完整性失败")


@router.delete("/cleanup-old-data")
async def cleanup_old_data(
    days_to_keep: int = Query(365, description="保留天数"),
    background_tasks: BackgroundTasks = None
):
    """清理过期的K线数据"""
    try:
        if background_tasks:
            # 异步清理
            background_tasks.add_task(market_data_service.cleanup_old_data, days_to_keep)
            return APIResponse(
                success=True,
                message=f"已启动数据清理任务，将保留最近 {days_to_keep} 天的数据"
            )
        else:
            # 同步清理
            await market_data_service.cleanup_old_data(days_to_keep)
            return APIResponse(
                success=True,
                message="数据清理完成"
            )
            
    except Exception as e:
        logger.error(f"清理数据失败: {e}")
        raise HTTPException(status_code=500, detail="清理数据失败")


@router.get("/supported-symbols")
async def get_supported_symbols():
    """获取支持的交易对列表"""
    # 常见的加密货币交易对
    popular_symbols = [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
        'XRPUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT',
        'LINKUSDT', 'LTCUSDT', 'ATOMUSDT', 'VETUSDT', 'FILUSDT',
        'TRXUSDT', 'ETCUSDT', 'XLMUSDT', 'ALGOUSDT', 'THETAUSDT'
    ]
    
    return {
        "popular_symbols": popular_symbols,
        "total_count": len(popular_symbols),
        "note": "这些是常见的交易对，实际支持的交易对可能更多"
    }


@router.get("/intervals")
async def get_supported_intervals():
    """获取支持的时间间隔列表"""
    return {
        "supported_intervals": [
            {"value": "1m", "label": "1分钟", "seconds": 60},
            {"value": "5m", "label": "5分钟", "seconds": 300},
            {"value": "15m", "label": "15分钟", "seconds": 900},
            {"value": "1h", "label": "1小时", "seconds": 3600},
            {"value": "4h", "label": "4小时", "seconds": 14400},
            {"value": "1d", "label": "1天", "seconds": 86400}
        ],
        "default": "5m",  # 改为5分钟默认
        "recommended": ["5m", "15m", "1h"]  # 推荐5分钟粒度
    } 


@router.post("/validate-symbols", response_model=APIResponse)
async def validate_symbols(symbols: List[str]):
    """验证交易对是否存在，并返回详细信息"""
    try:
        validation_results = {}
        valid_symbols = []
        invalid_symbols = []
        
        for symbol in symbols:
            try:
                # 尝试获取少量数据来验证交易对
                test_data = await market_data_service.get_klines(
                    symbol=symbol.upper(),
                    interval='5m',  # 改为5分钟
                    start_time=datetime.utcnow() - timedelta(hours=24),
                    end_time=datetime.utcnow(),
                    limit=10,
                    force_download=True
                )
                
                if not test_data.empty:
                    validation_results[symbol] = {
                        "valid": True,
                        "data_count": len(test_data),
                        "latest_time": test_data['timestamp'].max().isoformat() if 'timestamp' in test_data.columns else None
                    }
                    valid_symbols.append(symbol)
                else:
                    validation_results[symbol] = {
                        "valid": False,
                        "error": "无法获取数据"
                    }
                    invalid_symbols.append(symbol)
                    
            except Exception as e:
                validation_results[symbol] = {
                    "valid": False,
                    "error": str(e)
                }
                invalid_symbols.append(symbol)
        
        return APIResponse(
            success=True,
            message=f"交易对验证完成: {len(valid_symbols)}/{len(symbols)} 有效",
            data={
                "total_symbols": len(symbols),
                "valid_count": len(valid_symbols),
                "invalid_count": len(invalid_symbols),
                "valid_symbols": valid_symbols,
                "invalid_symbols": invalid_symbols,
                "details": validation_results
            }
        )
        
    except Exception as e:
        logger.error(f"验证交易对失败: {e}")
        raise HTTPException(status_code=500, detail="验证交易对失败")


@router.post("/prepare-backtest-data", response_model=APIResponse)
async def prepare_backtest_data(
    symbols: List[str],
    start_date: datetime,
    end_date: datetime,
    background_tasks: BackgroundTasks
):
    """为回测准备K线数据 - 预下载所有需要的数据"""
    try:
        # 扩展时间范围以包含技术指标计算需要的历史数据
        extended_start = start_date - timedelta(days=30)
        
        # 启动后台任务下载数据
        background_tasks.add_task(
            _download_backtest_data,
            symbols, extended_start, end_date
        )
        
        return APIResponse(
            success=True,
            message=f"已启动 {len(symbols)} 个交易对的回测数据下载任务",
            data={
                "symbols": symbols,
                "start_date": extended_start.isoformat(),
                "end_date": end_date.isoformat(),
                "total_symbols": len(symbols)
            }
        )
        
    except Exception as e:
        logger.error(f"准备回测数据失败: {e}")
        raise HTTPException(status_code=500, detail="准备回测数据失败")


async def _download_backtest_data(symbols: List[str], start_date: datetime, end_date: datetime):
    """后台任务：下载回测所需的K线数据"""
    logger.info(f"开始为回测下载 {len(symbols)} 个交易对的数据")
    
    successful_downloads = 0
    failed_downloads = []
    
    for symbol in symbols:
        try:
            logger.info(f"下载 {symbol} 的历史数据...")
            
            data = await market_data_service.get_klines(
                symbol=symbol,
                interval='5m',  # 改为5分钟
                start_time=start_date,
                end_time=end_date,
                force_download=True
            )
            
            if not data.empty:
                successful_downloads += 1
                logger.info(f"成功下载 {symbol} 的 {len(data)} 条数据")
            else:
                failed_downloads.append(symbol)
                logger.warning(f"未能下载 {symbol} 的数据")
                
        except Exception as e:
            failed_downloads.append(symbol)
            logger.error(f"下载 {symbol} 数据失败: {e}")
    
    logger.info(f"回测数据下载完成: {successful_downloads}/{len(symbols)} 成功")
    if failed_downloads:
        logger.warning(f"失败的交易对: {', '.join(failed_downloads)}") 