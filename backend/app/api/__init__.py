from fastapi import APIRouter
from .signals import router as signals_router
from .sources import router as sources_router
from .backtest import router as backtest_router
from .freqtrade import router as freqtrade_router
from .dashboard import router as dashboard_router
from .websocket import router as websocket_router
from .signal_management import router as signal_management_router
from .trading import router as trading_router
from .market_data import router as market_data_router

# 创建主API路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(signals_router, prefix="/signals", tags=["signals"])
api_router.include_router(sources_router, prefix="/sources", tags=["sources"])
api_router.include_router(backtest_router, prefix="/backtest", tags=["backtest"])
api_router.include_router(freqtrade_router, prefix="/freqtrade", tags=["freqtrade"])
api_router.include_router(dashboard_router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(websocket_router, prefix="/ws", tags=["websocket"])
api_router.include_router(signal_management_router, prefix="/signal-management", tags=["signal-management"])
api_router.include_router(trading_router, prefix="/trading", tags=["trading"])
api_router.include_router(market_data_router, prefix="/market-data", tags=["market-data"]) 