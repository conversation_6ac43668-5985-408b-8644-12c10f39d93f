from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import func, cast, Date
from typing import List, Optional, Dict, Any
import logging

from app.database import get_db
from app.models import SignalSource, TelegramSignal, SignalStatus
from app.schemas import (
    SignalSourceResponse, SignalSourceCreate, SignalSourceUpdate,
    PaginationParams, PaginatedResponse, APIResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=List[SignalSourceResponse])
async def get_sources(
    active_only: bool = Query(True, description="只获取活跃的信号源"),
    db: Session = Depends(get_db)
):
    """获取信号源列表"""
    query = db.query(SignalSource)
    if active_only:
        query = query.filter(SignalSource.is_active == True)
    sources = query.all()
    return sources


@router.get("/{source_id}", response_model=SignalSourceResponse)
async def get_source(source_id: int, db: Session = Depends(get_db)):
    """获取单个信号源详情"""
    source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="信号源不存在")
    return source


@router.post("/", response_model=SignalSourceResponse)
async def create_source(source_data: SignalSourceCreate, db: Session = Depends(get_db)):
    """创建信号源"""
    data = source_data.model_dump()
    source = SignalSource(
        name=data.get('name'),
        telegram_chat_id=data['telegram_chat_id'],
        description=data.get('description'),
        is_active=data.get('is_active', True)
    )
    db.add(source)
    db.commit()
    db.refresh(source)
    return source


@router.put("/{source_id}", response_model=SignalSourceResponse)
async def update_source(
    source_id: int,
    source_data: SignalSourceUpdate,
    db: Session = Depends(get_db)
):
    """更新信号源"""
    source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="信号源不存在")
    
    update_data = source_data.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(source, key, value)
    
    db.commit()
    db.refresh(source)
    return source


@router.delete("/{source_id}")
async def delete_source(source_id: int, db: Session = Depends(get_db)):
    """删除信号源"""
    source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="信号源不存在")
    
    # 检查是否有依赖的信号记录
    signal_count = db.query(TelegramSignal).filter(TelegramSignal.source_id == source_id).count()
    if signal_count > 0:
        raise HTTPException(
            status_code=400, 
            detail=f"无法删除信号源，因为有 {signal_count} 个依赖的信号记录。请先删除所有相关信号后再删除信号源。"
        )
    
    db.delete(source)
    db.commit()
    return {"message": "信号源已删除"}


@router.post("/telegram/auth")
async def telegram_auth(auth_data: Dict[str, Any]):
    """Telegram用户认证"""
    try:
        from app.services.telegram_collector import telegram_collector
        
        phone_number = auth_data.get("phone_number")
        code = auth_data.get("code")
        password = auth_data.get("password")
        
        if not phone_number:
            return {
                "success": False,
                "error": "手机号码是必需的"
            }
        
        result = await telegram_collector.authenticate(phone_number, code, password)
        
        # 如果认证成功，保存会话字符串到配置并启动监听
        if result.get("success") and result.get("session_string"):
            from app.services.config_manager import config_manager
            from app.database import get_db
            import asyncio
            
            db = next(get_db())
            try:
                config_manager.set_config(
                    "telegram_session_string", 
                    result["session_string"], 
                    db
                )
                config_manager.set_config(
                    "telegram_phone_number", 
                    phone_number, 
                    db
                )
                
                # 启动消息监听
                asyncio.create_task(telegram_collector.start_monitoring())
                logger.info("用户登录成功，已启动消息监听")
                
            finally:
                db.close()
        
        return result
        
    except Exception as e:
        return {
            "success": False,
            "error": f"认证失败: {str(e)}"
        }


@router.post("/telegram/reset")
async def telegram_reset():
    """重置Telegram认证状态"""
    try:
        from app.services.telegram_collector import telegram_collector
        
        # 重置客户端状态
        if telegram_collector.client:
            await telegram_collector.client.disconnect()
            telegram_collector.client = None
            telegram_collector.is_authenticated = False
            telegram_collector.is_running = False
        
        return {
            "success": True,
            "message": "Telegram认证状态已重置"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"重置失败: {str(e)}"
        }


@router.post("/telegram/logout")
async def telegram_logout():
    """Telegram用户登出"""
    try:
        from app.services.telegram_collector import telegram_collector
        from app.services.config_manager import config_manager
        from app.database import get_db
        
        await telegram_collector.logout()
        
        # 清除会话字符串
        db = next(get_db())
        try:
            config_manager.set_config("telegram_session_string", "", db)
        finally:
            db.close()
        
        return {
            "success": True,
            "message": "用户已登出"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"登出失败: {str(e)}"
        }


@router.get("/telegram/user")
async def get_telegram_user():
    """获取Telegram用户信息"""
    try:
        from app.services.telegram_collector import telegram_collector
        
        user_info = await telegram_collector.get_user_info()
        
        if user_info:
            return {
                "success": True,
                "user": user_info
            }
        else:
            return {
                "success": False,
                "error": "用户未登录"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"获取用户信息失败: {str(e)}"
        }


@router.get("/telegram/dialogs")
async def get_telegram_dialogs(
    search: Optional[str] = Query(None, description="搜索关键词"),
    limit: int = Query(100, description="限制数量")
):
    """获取Telegram用户对话列表"""
    try:
        from app.services.telegram_collector import telegram_collector
        
        # 获取所有对话
        dialogs = await telegram_collector.get_user_dialogs(limit=limit)
        
        # 如果有搜索关键词，进行搜索
        if search:
            dialogs = await telegram_collector.search_dialogs(search, dialogs)
        
        return {
            "success": True,
            "dialogs": dialogs,
            "total": len(dialogs)
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"获取对话列表失败: {str(e)}",
            "dialogs": [],
            "total": 0
        }


@router.post("/telegram/dialogs/refresh")
async def refresh_telegram_dialogs():
    """刷新Telegram对话列表缓存"""
    try:
        from app.services.telegram_collector import telegram_collector
        
        # 清除缓存
        telegram_collector.clear_cache()
        
        # 重新获取对话列表
        dialogs = await telegram_collector.get_user_dialogs(limit=100)
        
        return {
            "success": True,
            "message": "对话列表已刷新",
            "total": len(dialogs)
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"刷新对话列表失败: {str(e)}"
        }


@router.post("/telegram/dialogs/select")
async def select_telegram_dialogs(
    dialog_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """选择要监听的Telegram对话并自动创建信号源"""
    try:
        selected_dialogs = dialog_data.get("selected_dialogs", [])
        if not selected_dialogs:
            return {
                "success": False,
                "message": "请选择至少一个对话"
            }
        
        created_sources = []
        updated_sources = []
        
        for dialog_id in selected_dialogs:
            dialog_id = str(dialog_id)
            
            # 检查是否已存在信号源
            existing_source = db.query(SignalSource).filter(
                SignalSource.telegram_chat_id == dialog_id
            ).first()
            
            if existing_source:
                # 激活现有信号源
                if not existing_source.is_active:
                    existing_source.is_active = True
                    updated_sources.append(existing_source)
            else:
                # 获取对话信息
                from app.services.telegram_collector import telegram_collector
                dialogs = await telegram_collector.get_user_dialogs(limit=500)
                dialog_info = None
                
                for dialog in dialogs:
                    if dialog["id"] == dialog_id:
                        dialog_info = dialog
                        break
                
                if not dialog_info:
                    logger.warning(f"未找到对话信息: {dialog_id}")
                    continue
                
                # 创建新的信号源
                new_source = SignalSource(
                    name=dialog_info.get("title", f"Chat_{dialog_id}"),
                    telegram_chat_id=dialog_id,
                    description=f"Telegram {dialog_info.get('type', 'chat')}: {dialog_info.get('description', '')}",
                    is_active=True,
                    reliability_score=0.0,
                    total_signals=0,
                    successful_signals=0
                )
                
                db.add(new_source)
                created_sources.append(new_source)
        
        # 保存到数据库
        db.commit()
        
        # 刷新对象以获取ID
        for source in created_sources:
            db.refresh(source)
        
        # 更新Telegram收集器的监听列表
        from app.services.telegram_collector import telegram_collector
        await telegram_collector._update_monitored_chats()
        
        return {
            "success": True,
            "message": f"成功创建 {len(created_sources)} 个新信号源，更新 {len(updated_sources)} 个现有信号源",
            "created_sources": [
                {
                    "id": source.id,
                    "name": source.name,
                    "telegram_chat_id": source.telegram_chat_id
                }
                for source in created_sources
            ],
            "updated_sources": [
                {
                    "id": source.id,
                    "name": source.name,
                    "telegram_chat_id": source.telegram_chat_id
                }
                for source in updated_sources
            ]
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"选择对话失败: {e}")
        return {
            "success": False,
            "message": f"选择对话失败: {str(e)}"
        }


@router.post("/{source_id}/test-connection")
async def test_source_connection(source_id: int, db: Session = Depends(get_db)):
    """测试信号源连接"""
    source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="信号源不存在")
    
    try:
        # 尝试获取聊天信息
        from app.services.telegram_collector import telegram_collector
        chat_info = await telegram_collector.get_chat_info(source.telegram_chat_id)
        
        if chat_info:
            return {
                "success": True,
                "message": "连接测试成功",
                "chat_info": chat_info
            }
        else:
            return {
                "success": False,
                "message": "无法连接到指定的Telegram聊天"
            }
            
    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}"
        }


@router.post("/batch-update", response_model=APIResponse)
async def batch_update_sources(
    source_ids: List[int],
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """批量更新信号源"""
    if not source_ids:
        raise HTTPException(status_code=400, detail="请提供要更新的信号源ID列表")
    
    sources = db.query(SignalSource).filter(SignalSource.id.in_(source_ids)).all()
    
    if len(sources) != len(source_ids):
        raise HTTPException(status_code=404, detail="部分信号源不存在")
    
    updated_count = 0
    if is_active is not None:
        for source in sources:
            source.is_active = is_active
            updated_count += 1
    
    db.commit()
    
    return APIResponse(
        success=True,
        message=f"成功更新 {updated_count} 个信号源"
    )


@router.get("/{source_id}/statistics")
async def get_source_statistics(
    source_id: int,
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """获取信号源统计信息"""
    source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="信号源不存在")
    
    from datetime import datetime, timedelta
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # 查询指定时间范围内的信号
    signals_query = db.query(TelegramSignal).filter(
        TelegramSignal.source_id == source_id,
        TelegramSignal.signal_time >= start_date
    )
    
    # 基础统计
    total_signals = signals_query.count()
    executed_signals = signals_query.filter(TelegramSignal.status == SignalStatus.EXECUTED).count()
    failed_signals = signals_query.filter(TelegramSignal.status == SignalStatus.FAILED).count()
    pending_signals = signals_query.filter(TelegramSignal.status == SignalStatus.PENDING).count()
    
    # 成功率
    success_rate = (executed_signals / total_signals * 100) if total_signals > 0 else 0
    
    # 平均置信度
    avg_confidence_result = signals_query.filter(
        TelegramSignal.confidence_score > 0
    ).with_entities(func.avg(TelegramSignal.confidence_score)).scalar()
    avg_confidence = float(avg_confidence_result) if avg_confidence_result else 0
    
    # 按交易对统计
    symbol_stats = signals_query.with_entities(
        TelegramSignal.symbol,
        func.count(TelegramSignal.id).label('count')
    ).group_by(TelegramSignal.symbol).order_by(func.count(TelegramSignal.id).desc()).all()
    
    # 按信号类型统计
    type_stats = signals_query.with_entities(
        TelegramSignal.signal_type,
        func.count(TelegramSignal.id).label('count')
    ).group_by(TelegramSignal.signal_type).all()
    
    # 按日期统计（最近7天）
    daily_stats = signals_query.filter(
        TelegramSignal.signal_time >= datetime.utcnow() - timedelta(days=7)
    ).with_entities(
        cast(TelegramSignal.signal_time, Date).label('date'),
        func.count(TelegramSignal.id).label('count')
    ).group_by(cast(TelegramSignal.signal_time, Date)).order_by('date').all()
    
    return {
        "source_info": SignalSourceResponse.model_validate(source),
        "period_days": days,
        "statistics": {
            "total_signals": total_signals,
            "executed_signals": executed_signals,
            "failed_signals": failed_signals,
            "pending_signals": pending_signals,
            "success_rate": round(success_rate, 2),
            "average_confidence": round(avg_confidence, 3)
        },
        "symbol_distribution": [
            {"symbol": stat.symbol, "count": stat.count}
            for stat in symbol_stats
        ],
        "type_distribution": [
            {"signal_type": stat.signal_type.value, "count": stat.count}
            for stat in type_stats
        ],
        "daily_activity": [
            {"date": stat.date.isoformat(), "count": stat.count}
            for stat in daily_stats
        ]
    }


@router.get("/{source_id}/signals", response_model=PaginatedResponse)
async def get_source_signals(
    source_id: int,
    pagination: PaginationParams = Depends(),
    db: Session = Depends(get_db)
):
    """获取信号源的信号列表"""
    source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="信号源不存在")
    
    query = db.query(TelegramSignal).filter(TelegramSignal.source_id == source_id)
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.size
    signals = query.order_by(TelegramSignal.signal_time.desc()).offset(offset).limit(pagination.size).all()
    
    # 计算页数
    pages = (total + pagination.size - 1) // pagination.size
    
    from app.schemas import TelegramSignalResponse
    return PaginatedResponse(
        items=[TelegramSignalResponse.model_validate(signal) for signal in signals],
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=pages
    )


@router.post("/{source_id}/fetch-history", response_model=APIResponse)
async def fetch_source_history(
    source_id: int,
    limit: int = Query(100, ge=10, le=10000, description="拉取消息数量限制"),
    days_back: int = Query(30, ge=1, le=365, description="向前拉取多少天的消息"),
    skip_replies: bool = Query(True, description="是否跳过回复消息"),
    db: Session = Depends(get_db)
):
    """拉取信号源的历史消息"""
    try:
        # 检查信号源是否存在
        source = db.query(SignalSource).filter(SignalSource.id == source_id).first()
        if not source:
            raise HTTPException(status_code=404, detail="信号源不存在")
        
        # 获取Telegram收集器实例
        from app.services.telegram_collector import telegram_collector
        
        # 检查Telegram客户端状态
        if not telegram_collector.is_authenticated:
            raise HTTPException(
                status_code=400, 
                detail="Telegram客户端未认证，请先登录Telegram账户"
            )
        
        # 拉取历史消息
        result = await telegram_collector.fetch_chat_history(
            chat_id=source.telegram_chat_id,
            limit=limit,
            days_back=days_back,
            skip_replies=skip_replies
        )
        
        if result["success"]:
            # 更新信号源的总信号数
            new_signals_count = result["data"]["signals_found"]
            source.total_signals = db.query(TelegramSignal).filter(
                TelegramSignal.source_id == source_id
            ).count()
            db.commit()
            
            return APIResponse(
                success=True,
                message=result["message"],
                data=result["data"]
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"拉取信号源历史消息失败: {e}")
        raise HTTPException(status_code=500, detail="拉取历史消息失败") 