from fastapi import APIRouter, HTTPException
from app.services.freqtrade_client import freqtrade_client
from app.services.freqtrade_manager import freqtrade_manager

router = APIRouter()


@router.get("/status")
async def get_freqtrade_status():
    """获取Freqtrade状态"""
    status = await freqtrade_client.get_status()
    if status:
        return {"success": True, "data": status}
    else:
        raise HTTPException(status_code=503, detail="无法连接到Freqtrade")


@router.get("/balance")
async def get_freqtrade_balance():
    """获取账户余额"""
    balance = await freqtrade_client.get_balance()
    if balance:
        return {"success": True, "data": balance}
    else:
        raise HTTPException(status_code=503, detail="无法获取余额信息")


@router.get("/trades")
async def get_freqtrade_trades():
    """获取交易历史"""
    trades = await freqtrade_client.get_trades()
    if trades is not None:
        return {"success": True, "data": trades}
    else:
        raise HTTPException(status_code=503, detail="无法获取交易历史")


@router.post("/start")
async def start_freqtrade():
    """启动Freqtrade交易"""
    success = await freqtrade_client.start_trading()
    if success:
        return {"success": True, "message": "Freqtrade已启动"}
    else:
        raise HTTPException(status_code=500, detail="启动Freqtrade失败")


@router.post("/stop")
async def stop_freqtrade():
    """停止Freqtrade交易"""
    success = await freqtrade_client.stop_trading()
    if success:
        return {"success": True, "message": "Freqtrade已停止"}
    else:
        raise HTTPException(status_code=500, detail="停止Freqtrade失败")


@router.get("/manager/status")
async def get_manager_status():
    """获取Freqtrade管理器状态"""
    try:
        status = await freqtrade_manager.get_status()
        return {"success": True, "data": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取管理器状态失败: {str(e)}") 