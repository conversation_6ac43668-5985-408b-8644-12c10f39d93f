from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import json
import logging

from app.database import get_db
from app.models import BacktestResult, BacktestStatus, SignalSource
from app.schemas import (
    BacktestRequest, BacktestResponse, PaginationParams, 
    PaginatedResponse, APIResponse
)
from app.services.backtest_engine import BacktestEngine
from app.services.vectorbt_backtest_engine import VectorbtBacktestEngine
from app.services.market_data import MarketDataService

logger = logging.getLogger(__name__)

router = APIRouter()

# 尝试导入Celery任务
try:
    from app.tasks import run_backtest_celery_task
    from app.celery_app import celery_app
    CELERY_AVAILABLE = True
    logger.info("Celery可用，将使用Celery处理回测任务")
except ImportError:
    CELERY_AVAILABLE = False
    logger.warning("Celery不可用，将使用后台任务处理回测")


@router.get("/symbols")
async def get_available_symbols():
    """获取可用的交易对列表"""
    market_data = MarketDataService()
    try:
        symbols = await market_data.get_available_symbols()
        return {
            "success": True,
            "symbols": symbols
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "symbols": ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT']
        }


@router.get("/", response_model=PaginatedResponse)
async def get_backtests(
    pagination: PaginationParams = Depends(),
    status: Optional[BacktestStatus] = Query(None),
    symbol: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取回测列表"""
    import math
    
    def clean_float_value(value):
        """清理浮点数值，将无穷大和NaN转换为None"""
        if value is None:
            return None
        if math.isinf(value) or math.isnan(value):
            return None
        return value
    
    query = db.query(BacktestResult)
    
    if status:
        query = query.filter(BacktestResult.status == status)
    
    if symbol:
        query = query.filter(BacktestResult.symbol == symbol.upper())
    
    # 获取总数
    total = query.count()
    
    # 分页
    offset = (pagination.page - 1) * pagination.size
    backtests = query.order_by(BacktestResult.created_at.desc()).offset(offset).limit(pagination.size).all()
    
    # 清理数据中的无效浮点数
    cleaned_backtests = []
    for backtest in backtests:
        # 清理浮点数字段
        backtest.final_balance = clean_float_value(backtest.final_balance)
        backtest.total_return = clean_float_value(backtest.total_return)
        backtest.win_rate = clean_float_value(backtest.win_rate)
        backtest.profit_factor = clean_float_value(backtest.profit_factor)
        backtest.max_drawdown = clean_float_value(backtest.max_drawdown)
        backtest.sharpe_ratio = clean_float_value(backtest.sharpe_ratio)
        cleaned_backtests.append(backtest)
    
    # 计算页数
    pages = (total + pagination.size - 1) // pagination.size
    
    return PaginatedResponse(
        items=[BacktestResponse.model_validate(backtest) for backtest in cleaned_backtests],
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=pages
    )


@router.get("/{backtest_id}", response_model=BacktestResponse)
async def get_backtest(backtest_id: int, db: Session = Depends(get_db)):
    """获取单个回测详情"""
    backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
    if not backtest:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    return BacktestResponse.model_validate(backtest)


@router.post("/", response_model=BacktestResponse)
async def create_backtest(
    backtest_data: BacktestRequest, 
    db: Session = Depends(get_db)
):
    """创建新回测"""
    # 验证信号源是否存在
    sources = db.query(SignalSource).filter(SignalSource.id.in_(backtest_data.source_ids)).all()
    if len(sources) != len(backtest_data.source_ids):
        raise HTTPException(status_code=404, detail="部分信号源不存在")
    
    # 验证日期范围
    if backtest_data.start_date >= backtest_data.end_date:
        raise HTTPException(status_code=400, detail="开始日期必须早于结束日期")
    
    if backtest_data.end_date > datetime.utcnow():
        raise HTTPException(status_code=400, detail="结束日期不能超过当前时间")
    
    # 检查时间范围是否合理（不超过1年）
    if (backtest_data.end_date - backtest_data.start_date).days > 365:
        raise HTTPException(status_code=400, detail="回测时间范围不能超过1年")
    
    # 检查是否有重复的回测任务
    existing_backtest = db.query(BacktestResult).filter(
        BacktestResult.name == backtest_data.name,
        BacktestResult.status.in_([BacktestStatus.PENDING, BacktestStatus.RUNNING])
    ).first()
    
    if existing_backtest:
        raise HTTPException(status_code=400, detail="存在同名的进行中回测任务")
    
    # 处理交易对
    symbol = 'ALL'  # 默认为所有交易对
    if backtest_data.symbol:
        symbol = backtest_data.symbol.upper()
        if not symbol.endswith('USDT'):
            symbol += 'USDT'
    
    # 创建回测记录
    source_ids_str = ','.join(map(str, backtest_data.source_ids))
    signal_filters_str = json.dumps(backtest_data.signal_filters) if backtest_data.signal_filters else None
    
    backtest = BacktestResult(
        name=backtest_data.name,
        description=backtest_data.description,
        symbol=symbol,
        start_date=backtest_data.start_date,
        end_date=backtest_data.end_date,
        initial_balance=backtest_data.initial_balance,
        source_ids=source_ids_str,
        signal_filters=signal_filters_str,
        status=BacktestStatus.PENDING
    )
    
    db.add(backtest)
    db.commit()
    db.refresh(backtest)
    
    # 提交回测任务
    if CELERY_AVAILABLE:
        # 使用Celery异步任务
        task = run_backtest_celery_task.delay(backtest.id)
        logger.info(f"回测任务 {backtest.id} 已提交到Celery，任务ID: {task.id}")
        
        # 将任务ID保存到数据库（如果需要的话）
        backtest.error_message = f"celery_task_id:{task.id}"  # 临时存储任务ID
        db.commit()
    else:
        # 使用简单的后台任务
        from app.tasks import task_scheduler
        await task_scheduler.schedule_backtest_processing(backtest.id)
        logger.info(f"回测任务 {backtest.id} 已提交到后台任务队列")
    
    return BacktestResponse.model_validate(backtest)


@router.delete("/{backtest_id}", response_model=APIResponse)
async def delete_backtest(backtest_id: int, db: Session = Depends(get_db)):
    """删除回测"""
    backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
    if not backtest:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    # 检查回测状态，正在运行的不能删除
    if backtest.status == BacktestStatus.RUNNING:
        raise HTTPException(status_code=400, detail="正在运行的回测无法删除")
    
    db.delete(backtest)
    db.commit()
    
    return APIResponse(success=True, message="回测删除成功")


@router.post("/{backtest_id}/restart", response_model=APIResponse)
async def restart_backtest(
    backtest_id: int, 
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """重新开始回测"""
    backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
    if not backtest:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    # 检查回测状态
    if backtest.status == BacktestStatus.RUNNING:
        raise HTTPException(status_code=400, detail="回测正在运行中，无法重新开始")
    
    # 重置回测状态
    backtest.status = BacktestStatus.PENDING
    backtest.started_at = None
    backtest.completed_at = None
    backtest.final_balance = None
    backtest.total_return = None
    backtest.total_trades = None
    backtest.winning_trades = None
    backtest.losing_trades = None
    backtest.win_rate = None
    backtest.profit_factor = None
    backtest.max_drawdown = None
    backtest.sharpe_ratio = None
    backtest.result_json = None
    backtest.error_message = None
    
    db.commit()
    
    # 重新提交后台任务
    background_tasks.add_task(run_backtest_task, backtest.id)
    
    return APIResponse(success=True, message="回测任务已重新提交")


@router.post("/{backtest_id}/stop", response_model=APIResponse)
async def stop_backtest(
    backtest_id: int, 
    db: Session = Depends(get_db)
):
    """停止正在运行的回测"""
    backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
    if not backtest:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    # 检查回测状态
    if backtest.status not in [BacktestStatus.RUNNING, BacktestStatus.PENDING]:
        return APIResponse(
            success=False, 
            message=f"回测状态为 {backtest.status}，无法停止"
        )
    
    try:
        # 如果有Celery任务ID，尝试取消Celery任务
        if CELERY_AVAILABLE and backtest.error_message and "celery_task_id:" in backtest.error_message:
            task_id = backtest.error_message.split("celery_task_id:")[1]
            celery_app.control.revoke(task_id, terminate=True)
            logger.info(f"已取消Celery任务: {task_id}")
        
        # 更新回测状态为已取消
        backtest.status = BacktestStatus.FAILED
        backtest.error_message = "回测被用户手动停止"
        backtest.completed_at = datetime.utcnow()
        
        # 清理财务计算字段
        backtest.final_balance = None
        backtest.total_return = None
        backtest.total_trades = None
        backtest.winning_trades = None
        backtest.losing_trades = None
        backtest.win_rate = None
        backtest.profit_factor = None
        backtest.max_drawdown = None
        backtest.sharpe_ratio = None
        backtest.result_json = None
        
        db.commit()
        
        return APIResponse(success=True, message="回测已成功停止")
        
    except Exception as e:
        logger.error(f"停止回测失败: {e}")
        return APIResponse(success=False, message=f"停止回测失败: {str(e)}")


@router.get("/{backtest_id}/results")
async def get_backtest_results(backtest_id: int, db: Session = Depends(get_db)):
    """获取回测详细结果"""
    import math
    
    def clean_float_value(value):
        """清理浮点数值，将无穷大和NaN转换为None"""
        if value is None:
            return None
        if math.isinf(value) or math.isnan(value):
            return None
        return value
    
    backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
    if not backtest:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    if backtest.status != BacktestStatus.COMPLETED:
        raise HTTPException(status_code=400, detail="回测尚未完成")
    
    # 清理数据中的无效浮点数
    backtest.final_balance = clean_float_value(backtest.final_balance)
    backtest.total_return = clean_float_value(backtest.total_return)
    backtest.win_rate = clean_float_value(backtest.win_rate)
    backtest.profit_factor = clean_float_value(backtest.profit_factor)
    backtest.max_drawdown = clean_float_value(backtest.max_drawdown)
    backtest.sharpe_ratio = clean_float_value(backtest.sharpe_ratio)
    
    import json
    detailed_results = {}
    if backtest.result_json:
        try:
            detailed_results = json.loads(backtest.result_json)
            # 清理detailed_results中的无效数值
            if 'summary' in detailed_results:
                summary = detailed_results['summary']
                for key, value in summary.items():
                    if isinstance(value, float):
                        summary[key] = clean_float_value(value)
        except json.JSONDecodeError:
            detailed_results = {}
    
    # 提取web图表数据
    web_charts = {}
    if 'web_charts' in detailed_results:
        web_charts = detailed_results['web_charts']

    return {
        "backtest_info": BacktestResponse.model_validate(backtest),
        "summary": {
            "initial_balance": backtest.initial_balance,
            "final_balance": backtest.final_balance,
            "total_return": backtest.total_return,
            "total_trades": backtest.total_trades,
            "winning_trades": backtest.winning_trades,
            "losing_trades": backtest.losing_trades,
            "win_rate": backtest.win_rate,
            "profit_factor": backtest.profit_factor,
            "max_drawdown": backtest.max_drawdown,
            "sharpe_ratio": backtest.sharpe_ratio
        },
        "detailed_results": detailed_results,
        "charts": web_charts  # 新增：web图表数据
    }


@router.get("/statistics/overview")
async def get_backtest_statistics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """获取回测统计概览"""
    start_date = datetime.utcnow() - timedelta(days=days)
    
    query = db.query(BacktestResult).filter(BacktestResult.created_at >= start_date)
    
    # 基础统计
    total_backtests = query.count()
    completed_backtests = query.filter(BacktestResult.status == BacktestStatus.COMPLETED).count()
    running_backtests = query.filter(BacktestResult.status == BacktestStatus.RUNNING).count()
    failed_backtests = query.filter(BacktestResult.status == BacktestStatus.FAILED).count()
    
    # 成功率
    success_rate = (completed_backtests / total_backtests * 100) if total_backtests > 0 else 0
    
    # 平均收益率（仅已完成的回测）
    completed_query = query.filter(BacktestResult.status == BacktestStatus.COMPLETED)
    avg_return_result = completed_query.with_entities(
        func.avg(BacktestResult.total_return)
    ).scalar()
    avg_return = float(avg_return_result) if avg_return_result else 0
    
    # 按交易对统计
    symbol_stats = query.with_entities(
        BacktestResult.symbol,
        func.count(BacktestResult.id).label('count'),
        func.avg(BacktestResult.total_return).label('avg_return')
    ).group_by(BacktestResult.symbol).order_by(func.count(BacktestResult.id).desc()).all()
    
    # 按状态统计
    status_stats = query.with_entities(
        BacktestResult.status,
        func.count(BacktestResult.id).label('count')
    ).group_by(BacktestResult.status).all()
    
    return {
        "period_days": days,
        "overview": {
            "total_backtests": total_backtests,
            "completed_backtests": completed_backtests,
            "running_backtests": running_backtests,
            "failed_backtests": failed_backtests,
            "success_rate": round(success_rate, 2),
            "average_return": round(avg_return, 2)
        },
        "symbol_stats": [
            {
                "symbol": stat.symbol,
                "count": stat.count,
                "avg_return": round(float(stat.avg_return), 2) if stat.avg_return else 0
            }
            for stat in symbol_stats
        ],
        "status_distribution": [
            {
                "status": stat.status.value,
                "count": stat.count
            }
            for stat in status_stats
        ]
    }


async def run_backtest_task(backtest_id: int):
    """后台运行回测任务"""
    from app.database import SessionLocal
    
    db = SessionLocal()
    try:
        # 获取回测对象
        backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
        if not backtest:
            raise HTTPException(status_code=404, detail="回测不存在")
        
        # 使用新的VectorBT引擎
        engine = VectorbtBacktestEngine(db)
        await engine.run_backtest(backtest)  # 传递backtest对象而不是ID
    except Exception as e:
        print(f"回测任务执行失败: {e}")
        # 更新回测状态为失败
        backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
        if backtest:
            backtest.status = BacktestStatus.FAILED
            backtest.error_message = str(e)
            backtest.completed_at = datetime.utcnow()
            db.commit()
    finally:
        db.close()


@router.get("/{backtest_id}/progress")
async def get_backtest_progress(backtest_id: int, db: Session = Depends(get_db)):
    """获取回测进度"""
    try:
        backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
        if not backtest:
            raise HTTPException(status_code=404, detail="回测不存在")
        
        def safe_isoformat(dt):
            """安全的datetime序列化"""
            if dt is None:
                return None
            try:
                return dt.isoformat()
            except Exception:
                # 如果isoformat失败，转换为字符串
                return str(dt)
        
        progress_info = {
            "backtest_id": backtest.id,
            "name": backtest.name,
            "symbol": backtest.symbol,
            "status": backtest.status.value,
            "started_at": safe_isoformat(backtest.started_at),
            "completed_at": safe_isoformat(backtest.completed_at),
            "error_message": backtest.error_message,
            "duration_seconds": None
        }
        
        # 计算执行时间
        try:
            if backtest.started_at:
                from datetime import timezone
                end_time = backtest.completed_at or datetime.now(timezone.utc)
                
                # 确保两个datetime都有相同的时区信息
                if backtest.started_at.tzinfo is None:
                    # 如果started_at是naive，假设它是UTC
                    started_at = backtest.started_at.replace(tzinfo=timezone.utc)
                else:
                    started_at = backtest.started_at
                
                if end_time.tzinfo is None:
                    # 如果end_time是naive，假设它是UTC
                    end_time = end_time.replace(tzinfo=timezone.utc)
                
                duration = end_time - started_at
                progress_info["duration_seconds"] = int(duration.total_seconds())
        except Exception as e:
            logger.warning(f"计算执行时间失败: {e}")
            progress_info["duration_seconds"] = None
        
        # 如果使用Celery，获取任务状态
        if CELERY_AVAILABLE and backtest.error_message and backtest.error_message.startswith("celery_task_id:"):
            task_id = backtest.error_message.replace("celery_task_id:", "")
            try:
                from celery.result import AsyncResult
                task_result = AsyncResult(task_id, app=celery_app)
                
                if task_result.state == 'PROGRESS':
                    # 获取进度信息
                    task_info = task_result.info
                    progress_info.update({
                        "celery_state": task_result.state,
                        "progress_percentage": task_info.get('progress', 0),
                        "current_stage": task_info.get('stage', 'unknown'),
                        "stage_message": task_info.get('message', ''),
                        "signal_count": task_info.get('signal_count'),
                        "symbol_count": task_info.get('symbol_count'),
                        "successful_symbols": task_info.get('successful_symbols'),
                        "failed_symbols": task_info.get('failed_symbols')
                    })
                elif task_result.state == 'SUCCESS':
                    # 任务完成
                    progress_info.update({
                        "celery_state": task_result.state,
                        "progress_percentage": 100,
                        "current_stage": "completed",
                        "stage_message": "回测完成"
                    })
                elif task_result.state == 'FAILURE':
                    # 任务失败
                    error_info = task_result.info if task_result.info else {}
                    progress_info.update({
                        "celery_state": task_result.state,
                        "progress_percentage": 0,
                        "current_stage": "failed",
                        "stage_message": "回测失败",
                        "celery_error": str(error_info),
                        "error_details": {
                            "error_type": error_info.get('error_type', 'UnknownError'),
                            "error_message": error_info.get('error_message', str(error_info)),
                            "suggestions": _get_error_suggestions(error_info)
                        }
                    })
                else:
                    # 其他状态
                    progress_info.update({
                        "celery_state": task_result.state,
                        "progress_percentage": 0,
                        "current_stage": task_result.state.lower(),
                        "stage_message": f"任务状态: {task_result.state}"
                    })
                    
            except Exception as e:
                logger.error(f"获取Celery任务状态失败: {e}")
                progress_info["celery_error"] = f"无法获取任务状态: {str(e)}"
        
        # 如果正在运行但没有Celery信息，提供简单的进度估算
        elif backtest.status == BacktestStatus.RUNNING and backtest.started_at:
            from datetime import timezone
            current_time = datetime.now(timezone.utc)
            
            # 确保时区一致
            if backtest.started_at.tzinfo is None:
                started_at = backtest.started_at.replace(tzinfo=timezone.utc)
            else:
                started_at = backtest.started_at
                
            elapsed = current_time - started_at
            total_days = (backtest.end_date - backtest.start_date).days
            
            # 简单的进度估算（基于时间）
            estimated_duration = timedelta(seconds=total_days * 3)  # 假设每天需要3秒
            progress_percentage = min(elapsed.total_seconds() / estimated_duration.total_seconds() * 100, 95)
            
            progress_info.update({
                "progress_percentage": round(progress_percentage, 1),
                "elapsed_seconds": int(elapsed.total_seconds()),
                "estimated_remaining_seconds": max(0, int(estimated_duration.total_seconds() - elapsed.total_seconds())),
                "current_stage": "processing",
                "stage_message": "正在处理回测数据..."
            })
        
        # 如果回测失败，提供错误分析和建议
        elif backtest.status == BacktestStatus.FAILED and backtest.error_message:
            progress_info["error_details"] = {
                "error_type": _classify_error(backtest.error_message),
                "error_message": backtest.error_message,
                "suggestions": _get_error_suggestions_from_message(backtest.error_message)
            }
    
        return progress_info
        
    except Exception as e:
        logger.error(f"获取回测进度失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取回测进度失败: {str(e)}")


def _classify_error(error_message: str) -> str:
    """分类错误类型"""
    error_lower = error_message.lower()
    
    if "softtimelimitexceeded" in error_lower:
        return "TIMEOUT"
    elif "no data" in error_lower or "无法获取" in error_lower:
        return "DATA_ERROR"
    elif "no signals" in error_lower or "未找到" in error_lower:
        return "NO_SIGNALS"
    elif "connection" in error_lower or "网络" in error_lower:
        return "NETWORK_ERROR"
    elif "permission" in error_lower or "权限" in error_lower:
        return "PERMISSION_ERROR"
    else:
        return "UNKNOWN_ERROR"


def _get_error_suggestions_from_message(error_message: str) -> list:
    """根据错误消息提供建议"""
    error_type = _classify_error(error_message)
    return _get_error_suggestions({"error_type": error_type, "error_message": error_message})


def _get_error_suggestions(error_info: dict) -> list:
    """根据错误信息提供解决建议"""
    error_type = error_info.get('error_type', 'UNKNOWN_ERROR')
    error_message = error_info.get('error_message', '')
    
    suggestions = []
    
    if error_type == "TIMEOUT":
        suggestions.extend([
            "回测任务超时，通常是因为数据量过大或网络问题",
            "建议缩小回测时间范围（如从30天改为7天）",
            "检查网络连接是否稳定",
            "如果是第一次回测某些交易对，系统需要下载历史数据，耗时较长是正常的"
        ])
    elif error_type == "DATA_ERROR":
        suggestions.extend([
            "部分交易对的历史数据无法获取",
            "这些交易对可能在币安交易所不存在或已下架",
            "建议检查交易对名称是否正确",
            "可以尝试使用主流交易对（如BTCUSDT、ETHUSDT）进行回测"
        ])
    elif error_type == "NO_SIGNALS":
        suggestions.extend([
            "在指定时间范围内未找到符合条件的信号",
            "请检查信号源是否正确选择",
            "确认回测时间范围包含有效的历史信号",
            "可以扩大时间范围或调整信号筛选条件"
        ])
    elif error_type == "NETWORK_ERROR":
        suggestions.extend([
            "网络连接问题导致无法获取市场数据",
            "请检查网络连接是否正常",
            "如果问题持续，可能是交易所API限制，请稍后重试"
        ])
    elif error_type == "PERMISSION_ERROR":
        suggestions.extend([
            "权限不足或API配置错误",
            "请检查交易所API密钥配置",
            "确认API密钥有足够的权限访问市场数据"
        ])
    else:
        suggestions.extend([
            "遇到未知错误，请查看详细错误信息",
            "可以尝试重新启动回测",
            "如果问题持续，请联系技术支持"
        ])
    
    # 根据错误消息添加特定建议
    if "交易对" in error_message and "不存在" in error_message:
        suggestions.append("建议使用主流交易对或检查交易对名称拼写")
    
    return suggestions

@router.get("/{backtest_id}/task-status")
async def get_backtest_task_status(backtest_id: int, db: Session = Depends(get_db)):
    """获取回测Celery任务的详细状态"""
    if not CELERY_AVAILABLE:
        raise HTTPException(status_code=503, detail="Celery服务不可用")
    
    backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
    if not backtest:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    if not backtest.error_message or not backtest.error_message.startswith("celery_task_id:"):
        raise HTTPException(status_code=400, detail="该回测没有关联的Celery任务")
    
    task_id = backtest.error_message.replace("celery_task_id:", "")
    
    try:
        from celery.result import AsyncResult
        task_result = AsyncResult(task_id, app=celery_app)
        
        return {
            "task_id": task_id,
            "state": task_result.state,
            "info": task_result.info,
            "successful": task_result.successful() if hasattr(task_result, 'successful') else None,
            "failed": task_result.failed() if hasattr(task_result, 'failed') else None,
            "ready": task_result.ready() if hasattr(task_result, 'ready') else None,
            "result": task_result.result if task_result.state == 'SUCCESS' else None
        }
        
    except Exception as e:
        logger.error(f"获取Celery任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.get("/templates/signal-filters")
async def get_signal_filter_templates():
    """获取信号过滤器模板"""
    templates = [
        {
            "name": "高置信度信号",
            "description": "仅使用置信度大于0.7的信号",
            "filters": {
                "min_confidence": 0.7
            }
        },
        {
            "name": "仅买入信号",
            "description": "只处理买入和做多信号",
            "filters": {
                "signal_types": ["buy", "long"]
            }
        },
        {
            "name": "工作日信号",
            "description": "仅使用工作日产生的信号",
            "filters": {
                "weekdays_only": True
            }
        },
        {
            "name": "高频信号源",
            "description": "来自信号数量较多的源",
            "filters": {
                "min_source_signals": 10
            }
        }
    ]
    
    return {"templates": templates}


@router.post("/compare", response_model=APIResponse)
async def compare_backtests(
    backtest_ids: List[int],
    db: Session = Depends(get_db)
):
    """比较多个回测结果"""
    if len(backtest_ids) < 2:
        raise HTTPException(status_code=400, detail="至少需要选择2个回测进行比较")
    
    if len(backtest_ids) > 5:
        raise HTTPException(status_code=400, detail="最多只能比较5个回测")
    
    backtests = db.query(BacktestResult).filter(BacktestResult.id.in_(backtest_ids)).all()
    
    if len(backtests) != len(backtest_ids):
        raise HTTPException(status_code=404, detail="部分回测不存在")
    
    # 检查所有回测都已完成
    incomplete_backtests = [bt for bt in backtests if bt.status != BacktestStatus.COMPLETED]
    if incomplete_backtests:
        incomplete_names = [bt.name for bt in incomplete_backtests]
        raise HTTPException(
            status_code=400, 
            detail=f"以下回测尚未完成: {', '.join(incomplete_names)}"
        )
    
    # 构建比较结果
    comparison_data = []
    for backtest in backtests:
        comparison_data.append({
            "id": backtest.id,
            "name": backtest.name,
            "symbol": backtest.symbol,
            "initial_balance": backtest.initial_balance,
            "final_balance": backtest.final_balance,
            "total_return": backtest.total_return,
            "total_trades": backtest.total_trades,
            "win_rate": backtest.win_rate,
            "profit_factor": backtest.profit_factor,
            "max_drawdown": backtest.max_drawdown,
            "sharpe_ratio": backtest.sharpe_ratio,
            "start_date": backtest.start_date.isoformat(),
            "end_date": backtest.end_date.isoformat()
        })
    
    # 计算排名
    for metric in ["total_return", "win_rate", "profit_factor", "sharpe_ratio"]:
        sorted_data = sorted(comparison_data, key=lambda x: x.get(metric, 0) or 0, reverse=True)
        for rank, item in enumerate(sorted_data, 1):
            item[f"{metric}_rank"] = rank
    
    # 最大回撤排名（越小越好）
    sorted_data = sorted(comparison_data, key=lambda x: abs(x.get("max_drawdown", 0) or 0))
    for rank, item in enumerate(sorted_data, 1):
        item["max_drawdown_rank"] = rank
    
    return {
        "success": True,
        "message": "回测比较完成",
        "data": {
            "backtests": comparison_data,
            "best_performance": {
                "highest_return": max(comparison_data, key=lambda x: x.get("total_return", 0) or 0),
                "highest_win_rate": max(comparison_data, key=lambda x: x.get("win_rate", 0) or 0),
                "lowest_drawdown": min(comparison_data, key=lambda x: abs(x.get("max_drawdown", 0) or 0))
            }
        }
    }


@router.get("/performance/summary")
async def get_performance_summary(db: Session = Depends(get_db)):
    """获取回测性能汇总"""
    # 获取已完成的回测
    completed_backtests = db.query(BacktestResult).filter(
        BacktestResult.status == BacktestStatus.COMPLETED
    ).all()
    
    if not completed_backtests:
        return {
            "total_backtests": 0,
            "avg_return": 0,
            "avg_win_rate": 0,
            "best_backtest": None,
            "worst_backtest": None
        }
    
    # 计算汇总统计
    returns = [bt.total_return for bt in completed_backtests if bt.total_return is not None]
    win_rates = [bt.win_rate for bt in completed_backtests if bt.win_rate is not None]
    
    best_backtest = max(completed_backtests, key=lambda x: x.total_return or 0)
    worst_backtest = min(completed_backtests, key=lambda x: x.total_return or 0)
    
    return {
        "total_backtests": len(completed_backtests),
        "avg_return": round(sum(returns) / len(returns) * 100, 2) if returns else 0,
        "avg_win_rate": round(sum(win_rates) / len(win_rates) * 100, 2) if win_rates else 0,
        "best_backtest": {
            "id": best_backtest.id,
            "name": best_backtest.name,
            "return": round((best_backtest.total_return or 0) * 100, 2)
        },
        "worst_backtest": {
            "id": worst_backtest.id,
            "name": worst_backtest.name,
            "return": round((worst_backtest.total_return or 0) * 100, 2)
        }
    } 

@router.get("/config", response_model=APIResponse)
async def get_backtest_config(db: Session = Depends(get_db)):
    """获取回测配置"""
    try:
        from app.config import settings
        from app.services.config_manager import config_manager
        
        # 获取所有相关配置
        config = {
            # 系统基础配置
            'max_concurrent_trades': settings.max_concurrent_trades,
            'default_stake_amount': settings.default_stake_amount,
            'auto_trading_enabled': settings.auto_trading_enabled,
            
            # 回测专用配置
            'backtest_trade_amount_mode': settings.backtest_trade_amount_mode,
            'backtest_trade_amount_value': settings.backtest_trade_amount_value,
            'backtest_max_positions': settings.backtest_max_positions,
            'backtest_max_allocation_percent': settings.backtest_max_allocation_percent,
        }
        
        # 计算实际生效值
        effective_max_positions = min(config['backtest_max_positions'], config['max_concurrent_trades'])
        
        # 配置合理性检查
        warnings = []
        
        if config['backtest_trade_amount_mode'] == 'fixed_amount':
            if abs(config['backtest_trade_amount_value'] - config['default_stake_amount']) > 1:
                warnings.append(f"回测固定金额({config['backtest_trade_amount_value']})与系统默认下注金额({config['default_stake_amount']})不匹配")
        
        elif config['backtest_trade_amount_mode'] == 'percentage':
            max_usage = config['backtest_trade_amount_value'] * effective_max_positions
            if max_usage > config['backtest_max_allocation_percent']:
                warnings.append(f"配置冲突: {effective_max_positions}个{config['backtest_trade_amount_value']*100}%的仓位将使用{max_usage*100}%资金，超过限制{config['backtest_max_allocation_percent']*100}%")
        
        return APIResponse(
            success=True,
            data={
                'config': config,
                'effective_values': {
                    'max_positions': effective_max_positions,
                    'position_limited_by': 'backtest_max_positions' if config['backtest_max_positions'] <= config['max_concurrent_trades'] else 'max_concurrent_trades'
                },
                'warnings': warnings,
                'description': {
                    'trade_amount_mode': '资金分配模式: fixed_amount(固定金额) 或 percentage(百分比)',
                    'trade_amount_value': '每笔交易金额(固定模式:USDT) 或 百分比(0-1)',
                    'max_positions': '最大同时持仓数(受系统并发限制)',
                    'max_allocation_percent': '最大资金使用比例(0-1)'
                }
            },
            message="回测配置获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取回测配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/validate-config", response_model=APIResponse)
async def validate_backtest_config(
    request: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """验证回测配置的合理性"""
    try:
        from app.config import settings
        
        # 提取配置参数
        trade_amount_mode = request.get('trade_amount_mode', settings.backtest_trade_amount_mode)
        trade_amount_value = float(request.get('trade_amount_value', settings.backtest_trade_amount_value))
        max_positions = int(request.get('max_positions', settings.backtest_max_positions))
        max_allocation_percent = float(request.get('max_allocation_percent', settings.backtest_max_allocation_percent))
        initial_balance = float(request.get('initial_balance', 10000))
        
        # 系统限制
        system_max_concurrent = settings.max_concurrent_trades
        system_default_stake = settings.default_stake_amount
        
        # 计算有效值
        effective_max_positions = min(max_positions, system_max_concurrent)
        
        # 验证结果
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'calculations': {}
        }
        
        # 验证交易金额模式
        if trade_amount_mode == 'fixed_amount':
            single_trade_amount = trade_amount_value
            max_total_usage = single_trade_amount * effective_max_positions
            usage_percent = max_total_usage / initial_balance
            
            validation_result['calculations'] = {
                'single_trade_amount': single_trade_amount,
                'max_simultaneous_positions': effective_max_positions,
                'max_total_usage': max_total_usage,
                'usage_percent': usage_percent,
                'remaining_balance': initial_balance - max_total_usage
            }
            
            # 检查是否与系统默认值匹配
            if abs(single_trade_amount - system_default_stake) > 1:
                validation_result['warnings'].append(f"固定金额({single_trade_amount})与系统默认下注金额({system_default_stake})不匹配")
            
            # 检查是否会透支
            if max_total_usage > initial_balance:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"资金不足: 需要{max_total_usage}, 只有{initial_balance}")
                
        elif trade_amount_mode == 'percentage':
            single_trade_percent = trade_amount_value
            max_total_percent = single_trade_percent * effective_max_positions
            single_trade_amount = initial_balance * single_trade_percent
            max_total_usage = initial_balance * max_total_percent
            
            validation_result['calculations'] = {
                'single_trade_percent': single_trade_percent,
                'single_trade_amount': single_trade_amount,
                'max_simultaneous_positions': effective_max_positions,
                'max_total_percent': max_total_percent,
                'max_total_usage': max_total_usage,
                'remaining_balance': initial_balance - max_total_usage
            }
            
            # 检查是否超过最大分配比例
            if max_total_percent > max_allocation_percent:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"超过最大资金使用限制: {max_total_percent*100}% > {max_allocation_percent*100}%")
        
        # 检查持仓数限制
        if max_positions > system_max_concurrent:
            validation_result['warnings'].append(f"回测最大持仓数({max_positions})受系统并发限制({system_max_concurrent})")
        
        return APIResponse(
            success=True,
            data=validation_result,
            message="配置验证完成"
        )
        
    except Exception as e:
        logger.error(f"验证回测配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 