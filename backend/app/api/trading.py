from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, List

from app.database import get_db
from app.services.trading_monitor import trading_monitor
from app.services.price_monitor import price_monitor
from app.models import TelegramSignal, SignalStatus

router = APIRouter(prefix="/trading", tags=["trading"])


@router.get("/status")
async def get_trading_status():
    """获取交易系统状态"""
    try:
        status = await trading_monitor.get_system_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/prices")
async def get_current_prices():
    """获取当前价格缓存"""
    try:
        prices = {}
        for symbol, price_data in price_monitor.price_cache.items():
            prices[symbol] = {
                "price": price_data["price"],
                "timestamp": price_data["timestamp"].isoformat(),
                "bid": price_data.get("bid"),
                "ask": price_data.get("ask")
            }
        
        return {
            "success": True,
            "data": {
                "prices": prices,
                "total_symbols": len(prices),
                "last_update": max([p["timestamp"] for p in prices.values()]) if prices else None
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/active-signals")
async def get_active_signals(db: Session = Depends(get_db)):
    """获取活跃信号列表"""
    try:
        active_signals = db.query(TelegramSignal).filter(
            TelegramSignal.status.in_([
                SignalStatus.PENDING,
                SignalStatus.PROCESSING,
                SignalStatus.EXECUTED
            ])
        ).order_by(TelegramSignal.signal_time.desc()).all()
        
        signals_data = []
        for signal in active_signals:
            # 获取当前价格
            current_price = price_monitor.get_current_price(signal.symbol)
            
            # 计算价格变化
            price_change = None
            price_change_percent = None
            if current_price and signal.entry_price:
                price_change = current_price - signal.entry_price
                price_change_percent = (price_change / signal.entry_price) * 100
            
            signals_data.append({
                "id": signal.id,
                "symbol": signal.symbol,
                "signal_type": signal.signal_type.value,
                "entry_price": signal.entry_price,
                "current_price": current_price,
                "price_change": price_change,
                "price_change_percent": price_change_percent,
                "stop_loss": signal.stop_loss,
                "take_profit": signal.take_profit,
                "status": signal.status.value,
                "signal_time": signal.signal_time.isoformat(),
                "entry_points_count": len(signal.entry_points),
                "tp_points_count": len(signal.take_profit_points)
            })
        
        return {
            "success": True,
            "data": {
                "signals": signals_data,
                "total": len(signals_data)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start-monitoring")
async def start_monitoring():
    """手动启动交易监控"""
    try:
        if trading_monitor.is_running():
            return {
                "success": False,
                "message": "交易监控已在运行中"
            }
        
        import asyncio
        asyncio.create_task(trading_monitor.start_all_monitors())
        
        return {
            "success": True,
            "message": "交易监控启动成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop-monitoring")
async def stop_monitoring():
    """手动停止交易监控"""
    try:
        trading_monitor.stop_all_monitors()
        
        return {
            "success": True,
            "message": "交易监控停止成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/signal/{signal_id}/details")
async def get_signal_details(signal_id: int, db: Session = Depends(get_db)):
    """获取信号详细信息"""
    try:
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        if not signal:
            raise HTTPException(status_code=404, detail="信号不存在")
        
        # 获取当前价格
        current_price = price_monitor.get_current_price(signal.symbol)
        
        # 入场点信息
        entry_points = []
        for ep in signal.entry_points:
            entry_points.append({
                "id": ep.id,
                "price": ep.price,
                "allocation": ep.allocation,
                "status": ep.status,
                "actual_price": ep.actual_price,
                "filled_quantity": ep.filled_quantity,
                "filled_at": ep.filled_at.isoformat() if ep.filled_at else None
            })
        
        # 止盈点信息
        tp_points = []
        for tp in signal.take_profit_points:
            tp_points.append({
                "id": tp.id,
                "price": tp.price,
                "allocation": tp.allocation,
                "status": tp.status,
                "actual_price": tp.actual_price,
                "filled_quantity": tp.filled_quantity,
                "pnl": tp.pnl,
                "pnl_percentage": tp.pnl_percentage,
                "filled_at": tp.filled_at.isoformat() if tp.filled_at else None
            })
        
        return {
            "success": True,
            "data": {
                "signal": {
                    "id": signal.id,
                    "symbol": signal.symbol,
                    "signal_type": signal.signal_type.value,
                    "entry_price": signal.entry_price,
                    "current_price": current_price,
                    "stop_loss": signal.stop_loss,
                    "take_profit": signal.take_profit,
                    "leverage": signal.leverage,
                    "status": signal.status.value,
                    "signal_time": signal.signal_time.isoformat(),
                    "raw_message": signal.raw_message
                },
                "entry_points": entry_points,
                "take_profit_points": tp_points
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))