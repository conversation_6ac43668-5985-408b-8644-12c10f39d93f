from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from app.services.signal_parser import EnhancedSignalParser
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/test", tags=["test"])

class SingleSignalRequest(BaseModel):
    signal_text: str

class BatchSignalRequest(BaseModel):
    signal_texts: List[str]

class SignalTestResult(BaseModel):
    success: bool
    signal_type: Optional[str] = None
    symbol: Optional[str] = None
    entry_prices: Optional[List[float]] = None
    stop_loss: Optional[float] = None
    take_profit_targets: Optional[List[float]] = None
    leverage: Optional[int] = None
    error: Optional[str] = None
    raw_text: str

@router.post("/signal/single", response_model=SignalTestResult)
async def test_single_signal(request: SingleSignalRequest):
    """测试单个信号解析"""
    try:
        parser = EnhancedSignalParser()
        result = await parser.parse_message(request.signal_text)
        
        if result:
            return SignalTestResult(
                success=True,
                signal_type=result.get('signal_type'),
                symbol=result.get('symbol'),
                entry_prices=result.get('entry_prices'),
                stop_loss=result.get('stop_loss'),
                take_profit_targets=result.get('take_profit_targets'),
                leverage=result.get('leverage'),
                raw_text=request.signal_text
            )
        else:
            return SignalTestResult(
                success=False,
                error="无法解析信号",
                raw_text=request.signal_text
            )
    except Exception as e:
        logger.error(f"Signal parsing error: {e}")
        return SignalTestResult(
            success=False,
            error=str(e),
            raw_text=request.signal_text
        )

@router.post("/signal/batch", response_model=List[SignalTestResult])
async def test_batch_signals(request: BatchSignalRequest):
    """批量测试信号解析"""
    results = []
    parser = EnhancedSignalParser()
    
    for signal_text in request.signal_texts:
        try:
            result = await parser.parse_message(signal_text)
            
            if result:
                results.append(SignalTestResult(
                    success=True,
                    signal_type=result.get('signal_type'),
                    symbol=result.get('symbol'),
                    entry_prices=result.get('entry_prices'),
                    stop_loss=result.get('stop_loss'),
                    take_profit_targets=result.get('take_profit_targets'),
                    leverage=result.get('leverage'),
                    raw_text=signal_text
                ))
            else:
                results.append(SignalTestResult(
                    success=False,
                    error="无法解析信号",
                    raw_text=signal_text
                ))
        except Exception as e:
            logger.error(f"Signal parsing error: {e}")
            results.append(SignalTestResult(
                success=False,
                error=str(e),
                raw_text=signal_text
            ))
    
    return results