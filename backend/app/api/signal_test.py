from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from app.services.signal_parser import EnhancedSignalParser
from app.services.llm_signal_parser import LLMSignalParserFactory
from app.services.signal_parser_batch import BatchSignalParserFactory
from app.services.config_manager import config_manager
from app.database import get_db
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Signal Test"])

class SignalTestRequest(BaseModel):
    message: str
    expected_symbol: Optional[str] = None
    expected_type: Optional[str] = None
    parser_type: Optional[str] = None  # 新增：可选择解析器类型

class BatchSignalTestRequest(BaseModel):
    signals: List[SignalTestRequest]

builtin_parser = EnhancedSignalParser()

@router.post("/parse")
async def test_signal_parsing(request: SignalTestRequest, db: Session = Depends(get_db)):
    """测试单个信号解析"""
    try:
        # 选择解析器
        if request.parser_type == "llm":
            parser = LLMSignalParserFactory.create_parser(db)
        elif request.parser_type == "builtin":
            parser = builtin_parser
        else:
            # 从配置中获取默认解析器类型
            parser_type = config_manager.get_config("signal_parser_type", db, "builtin")
            if parser_type == "llm":
                parser = LLMSignalParserFactory.create_parser(db)
            else:
                parser = builtin_parser
        
        result = await parser.parse_message(request.message)
        
        if result:
            validation = parser.validate_signal(result)
            return {
                "success": True,
                "parsed": True,
                "data": {
                    "symbol": result.get("symbol"),
                    "signal_type": result.get("signal_type").value if result.get("signal_type") else None,
                    "entry_price": result.get("entry_price"),
                    "entry_price_range": result.get("entry_price_range", []),
                    "stop_loss": result.get("stop_loss"),
                    "take_profit": result.get("take_profit"),
                    "take_profit_targets": result.get("take_profit_targets", []),
                    "leverage": result.get("leverage"),
                    "confidence_score": result.get("confidence_score"),
                    "format_type": result.get("format_type")
                },
                "validation": validation,
                "expected_match": {
                    "symbol": request.expected_symbol == result.get("symbol") if request.expected_symbol else None,
                    "type": request.expected_type == result.get("signal_type").value if request.expected_type and result.get("signal_type") else None
                }
            }
        else:
            return {
                "success": True,
                "parsed": False,
                "message": "无法解析此信号",
                "expected_match": {
                    "symbol": False if request.expected_symbol else None,
                    "type": False if request.expected_type else None
                }
            }
    except Exception as e:
        logger.error(f"信号解析测试失败: {e}")
        return {"success": False, "error": str(e)}

@router.post("/batch")
async def batch_test_signal_parsing(request: BatchSignalTestRequest, db: Session = Depends(get_db)):
    """批量测试信号解析 - 优化版本"""
    try:
        start_time = datetime.now()
        
        # 确定解析器类型（假设所有信号使用相同类型）
        parser_type = None
        if request.signals:
            first_signal = request.signals[0]
            if first_signal.parser_type:
                parser_type = first_signal.parser_type
            else:
                parser_type = config_manager.get_config("signal_parser_type", db, "builtin")
        
        # 提取所有消息
        messages = [signal.message for signal in request.signals]
        
        # 使用批量解析器
        batch_parser = BatchSignalParserFactory.get_parser(db)
        parse_results = await batch_parser.parse_messages_batch(messages, parser_type)
        
        # 构建响应结果
        results = []
        stats = {"total": len(messages), "parsed": 0, "failed": 0, "symbol_match": 0, "type_match": 0}
        
        for i, (signal_req, result) in enumerate(zip(request.signals, parse_results)):
            try:
                if result:
                    stats["parsed"] += 1
                    
                    # 验证信号
                    if parser_type == "llm":
                        # 对于LLM解析器，创建一个临时实例进行验证
                        temp_parser = LLMSignalParserFactory.create_parser(db)
                        validation = temp_parser.validate_signal(result)
                    else:
                        validation = builtin_parser.validate_signal(result)
                    
                    symbol_match = signal_req.expected_symbol == result.get("symbol") if signal_req.expected_symbol else None
                    type_match = signal_req.expected_type == result.get("signal_type").value if signal_req.expected_type and result.get("signal_type") else None
                    
                    if symbol_match: stats["symbol_match"] += 1
                    if type_match: stats["type_match"] += 1
                    
                    results.append({
                        "success": True,
                        "parsed": True,
                        "data": {
                            "symbol": result.get("symbol"),
                            "signal_type": result.get("signal_type").value if result.get("signal_type") else None,
                            "entry_price": result.get("entry_price"),
                            "entry_prices": result.get("entry_prices", []),
                            "stop_loss": result.get("stop_loss"),
                            "take_profit": result.get("take_profit"),
                            "take_profit_targets": result.get("take_profit_targets", []),
                            "leverage": result.get("leverage"),
                            "confidence_score": result.get("confidence_score"),
                            "format_type": result.get("format_type")
                        },
                        "validation": validation,
                        "message": signal_req.message[:100] + "..." if len(signal_req.message) > 100 else signal_req.message,
                        "expected_match": {"symbol": symbol_match, "type": type_match}
                    })
                else:
                    stats["failed"] += 1
                    results.append({
                        "success": True,
                        "parsed": False,
                        "message": "无法解析此信号",
                        "error": "无法解析",
                        "original_message": signal_req.message[:100] + "..." if len(signal_req.message) > 100 else signal_req.message,
                        "expected_match": {"symbol": False if signal_req.expected_symbol else None, "type": False if signal_req.expected_type else None}
                    })
                    
            except Exception as e:
                logger.error(f"处理批量解析结果失败: {e}")
                stats["failed"] += 1
                results.append({
                    "success": False,
                    "parsed": False,
                    "error": str(e),
                    "message": f"处理失败: {str(e)}",
                    "original_message": signal_req.message[:100] + "..." if len(signal_req.message) > 100 else signal_req.message,
                    "expected_match": {"symbol": False if signal_req.expected_symbol else None, "type": False if signal_req.expected_type else None}
                })
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        return {
            "success": True,
            "results": results,
            "stats": {
                **stats,
                "success_rate": round(stats["parsed"] / stats["total"] * 100, 2) if stats["total"] > 0 else 0,
                "symbol_accuracy": round(stats["symbol_match"] / stats["parsed"] * 100, 2) if stats["parsed"] > 0 else 0,
                "type_accuracy": round(stats["type_match"] / stats["parsed"] * 100, 2) if stats["parsed"] > 0 else 0
            },
            "performance": {
                "total_time_seconds": processing_time,
                "average_time_per_signal": processing_time / len(messages) if messages else 0,
                "parser_type": parser_type
            }
        }
        
    except Exception as e:
        logger.error(f"批量解析失败: {e}")
        return {"success": False, "error": str(e)}