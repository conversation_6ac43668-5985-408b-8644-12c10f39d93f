from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.database import get_db
from app.models import TelegramSignal, SignalSource, SignalTrade, SignalStatus
from app.schemas import DashboardData, SignalStatistics, SourcePerformance

router = APIRouter()


@router.get("/", response_model=DashboardData)
async def get_dashboard_data(db: Session = Depends(get_db)):
    """获取仪表板数据"""
    # 信号统计
    total_signals = db.query(TelegramSignal).count()
    pending_signals = db.query(TelegramSignal).filter(TelegramSignal.status == SignalStatus.PENDING).count()
    executed_signals = db.query(TelegramSignal).filter(TelegramSignal.status == SignalStatus.EXECUTED).count()
    failed_signals = db.query(TelegramSignal).filter(TelegramSignal.status == SignalStatus.FAILED).count()
    
    success_rate = (executed_signals / total_signals * 100) if total_signals > 0 else 0
    
    avg_confidence_result = db.query(func.avg(TelegramSignal.confidence_score)).filter(
        TelegramSignal.confidence_score > 0
    ).scalar()
    avg_confidence = float(avg_confidence_result) if avg_confidence_result else 0
    
    signal_stats = SignalStatistics(
        total_signals=total_signals,
        pending_signals=pending_signals,
        executed_signals=executed_signals,
        failed_signals=failed_signals,
        success_rate=round(success_rate, 2),
        average_confidence=round(avg_confidence, 3)
    )
    
    # 最近信号
    recent_signals_query = db.query(TelegramSignal).order_by(TelegramSignal.received_at.desc()).limit(10)
    from app.schemas import TelegramSignalResponse
    recent_signals = [TelegramSignalResponse.model_validate(signal) for signal in recent_signals_query]
    
    # 信号源性能
    sources = db.query(SignalSource).filter(SignalSource.is_active == True).all()
    source_performance = []
    
    for source in sources:
        total_pnl_result = db.query(func.sum(SignalTrade.pnl)).join(TelegramSignal).filter(
            TelegramSignal.source_id == source.id,
            SignalTrade.pnl.isnot(None)
        ).scalar()
        
        avg_pnl_result = db.query(func.avg(SignalTrade.pnl)).join(TelegramSignal).filter(
            TelegramSignal.source_id == source.id,
            SignalTrade.pnl.isnot(None)
        ).scalar()
        
        source_performance.append(SourcePerformance(
            source_id=source.id,
            source_name=source.name,
            total_signals=source.total_signals,
            successful_signals=source.successful_signals,
            success_rate=round(source.reliability_score * 100, 2),
            average_pnl=float(avg_pnl_result) if avg_pnl_result else 0,
            total_pnl=float(total_pnl_result) if total_pnl_result else 0
        ))
    
    # 活跃交易数
    active_trades = db.query(SignalTrade).filter(SignalTrade.status == "open").count()
    
    # 总盈亏
    total_pnl_result = db.query(func.sum(SignalTrade.pnl)).filter(
        SignalTrade.pnl.isnot(None)
    ).scalar()
    total_pnl = float(total_pnl_result) if total_pnl_result else 0
    
    return DashboardData(
        signal_statistics=signal_stats,
        recent_signals=recent_signals,
        source_performance=source_performance,
        active_trades=active_trades,
        total_pnl=total_pnl
    ) 