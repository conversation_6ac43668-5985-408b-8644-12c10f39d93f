from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from typing import List, Dict, Any
import json
import asyncio
import logging
from datetime import datetime

from app.database import get_db, SessionLocal
from app.models import BacktestResult, BacktestStatus

logger = logging.getLogger(__name__)

router = APIRouter()


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.backtest_subscribers: Dict[int, List[WebSocket]] = {}
        
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket连接建立，当前连接数: {len(self.active_connections)}")
        
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            
        # 从回测订阅中移除
        for backtest_id, subscribers in self.backtest_subscribers.items():
            if websocket in subscribers:
                subscribers.remove(websocket)
                
        logger.info(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")
        
    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            self.disconnect(websocket)
            
    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection)
                
        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)
            
    async def send_to_backtest_subscribers(self, backtest_id: int, message: str):
        """发送消息给特定回测的订阅者"""
        if backtest_id not in self.backtest_subscribers:
            return
            
        disconnected = []
        for connection in self.backtest_subscribers[backtest_id]:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"发送回测消息失败: {e}")
                disconnected.append(connection)
                
        # 清理断开的连接
        for connection in disconnected:
            if connection in self.backtest_subscribers[backtest_id]:
                self.backtest_subscribers[backtest_id].remove(connection)
                
    def subscribe_to_backtest(self, backtest_id: int, websocket: WebSocket):
        """订阅回测进度更新"""
        if backtest_id not in self.backtest_subscribers:
            self.backtest_subscribers[backtest_id] = []
        
        if websocket not in self.backtest_subscribers[backtest_id]:
            self.backtest_subscribers[backtest_id].append(websocket)
            logger.info(f"WebSocket订阅回测 {backtest_id} 进度更新")
            
    def unsubscribe_from_backtest(self, backtest_id: int, websocket: WebSocket):
        """取消订阅回测进度更新"""
        if backtest_id in self.backtest_subscribers and websocket in self.backtest_subscribers[backtest_id]:
            self.backtest_subscribers[backtest_id].remove(websocket)
            logger.info(f"WebSocket取消订阅回测 {backtest_id} 进度更新")


manager = ConnectionManager()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    
    try:
        # 发送连接确认消息
        await manager.send_personal_message(json.dumps({
            "type": "connection",
            "status": "connected",
            "timestamp": datetime.utcnow().isoformat(),
            "message": "WebSocket连接已建立"
        }), websocket)
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_websocket_message(message, websocket)
            except json.JSONDecodeError:
                await manager.send_personal_message(json.dumps({
                    "type": "error",
                    "message": "无效的JSON格式",
                    "timestamp": datetime.utcnow().isoformat()
                }), websocket)
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {e}")
                await manager.send_personal_message(json.dumps({
                    "type": "error",
                    "message": f"处理消息失败: {str(e)}",
                    "timestamp": datetime.utcnow().isoformat()
                }), websocket)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket异常: {e}")
        manager.disconnect(websocket)


async def handle_websocket_message(message: dict, websocket: WebSocket):
    """处理WebSocket消息"""
    message_type = message.get("type")
    
    if message_type == "ping":
        # 心跳检测
        await manager.send_personal_message(json.dumps({
            "type": "pong",
            "timestamp": datetime.utcnow().isoformat()
        }), websocket)
        
    elif message_type == "subscribe_backtest":
        # 订阅回测进度
        backtest_id = message.get("backtest_id")
        if backtest_id:
            manager.subscribe_to_backtest(backtest_id, websocket)
            
            # 发送当前状态
            await send_backtest_status(backtest_id, websocket)
            
    elif message_type == "unsubscribe_backtest":
        # 取消订阅回测进度
        backtest_id = message.get("backtest_id")
        if backtest_id:
            manager.unsubscribe_from_backtest(backtest_id, websocket)
            
    elif message_type == "get_backtest_status":
        # 获取回测状态
        backtest_id = message.get("backtest_id")
        if backtest_id:
            await send_backtest_status(backtest_id, websocket)
            
    else:
        await manager.send_personal_message(json.dumps({
            "type": "error",
            "message": f"未知的消息类型: {message_type}",
            "timestamp": datetime.utcnow().isoformat()
        }), websocket)


async def send_backtest_status(backtest_id: int, websocket: WebSocket):
    """发送回测状态"""
    try:
        db = SessionLocal()
        try:
            backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
            if not backtest:
                await manager.send_personal_message(json.dumps({
                    "type": "error",
                    "message": f"回测 {backtest_id} 不存在",
                    "timestamp": datetime.utcnow().isoformat()
                }), websocket)
                return
                
            status_data = {
                "type": "backtest_status",
                "backtest_id": backtest.id,
                "status": backtest.status.value,
                "name": backtest.name,
                "symbol": backtest.symbol,
                "started_at": backtest.started_at.isoformat() if backtest.started_at else None,
                "completed_at": backtest.completed_at.isoformat() if backtest.completed_at else None,
                "error_message": backtest.error_message,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 如果有结果数据，包含结果摘要
            if backtest.status == BacktestStatus.COMPLETED and backtest.result_json:
                try:
                    results = json.loads(backtest.result_json)
                    status_data["results_summary"] = {
                        "final_balance": backtest.final_balance,
                        "total_return": backtest.total_return,
                        "total_trades": backtest.total_trades,
                        "win_rate": backtest.win_rate,
                        "max_drawdown": backtest.max_drawdown
                    }
                except:
                    pass
                    
            await manager.send_personal_message(json.dumps(status_data), websocket)
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"发送回测状态失败: {e}")


# 用于其他模块调用的通知函数
async def notify_backtest_progress(backtest_id: int, progress_data: dict):
    """通知回测进度更新"""
    message = {
        "type": "backtest_progress",
        "backtest_id": backtest_id,
        "timestamp": datetime.utcnow().isoformat(),
        **progress_data
    }
    
    await manager.send_to_backtest_subscribers(backtest_id, json.dumps(message))


async def notify_backtest_completed(backtest_id: int, results: dict = None):
    """通知回测完成"""
    message = {
        "type": "backtest_completed",
        "backtest_id": backtest_id,
        "timestamp": datetime.utcnow().isoformat(),
        "results": results
    }
    
    await manager.send_to_backtest_subscribers(backtest_id, json.dumps(message))


async def notify_backtest_failed(backtest_id: int, error: str):
    """通知回测失败"""
    message = {
        "type": "backtest_failed",
        "backtest_id": backtest_id,
        "error": error,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    await manager.send_to_backtest_subscribers(backtest_id, json.dumps(message))


async def broadcast_system_notification(notification_type: str, message: str, data: dict = None):
    """广播系统通知"""
    notification = {
        "type": "system_notification",
        "notification_type": notification_type,
        "message": message,
        "data": data or {},
        "timestamp": datetime.utcnow().isoformat()
    }
    
    await manager.broadcast(json.dumps(notification))


@router.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计"""
    return {
        "active_connections": len(manager.active_connections),
        "backtest_subscriptions": {
            str(backtest_id): len(subscribers) 
            for backtest_id, subscribers in manager.backtest_subscribers.items()
        },
        "total_backtest_subscribers": sum(len(subscribers) for subscribers in manager.backtest_subscribers.values()),
        "timestamp": datetime.utcnow().isoformat()
    } 