from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import traceback

from app.database import get_db
from app.models import (
    TelegramSignal, SignalSource, SignalStatus, SignalType, SignalTrade,
    TradeEntryPoint, TradeTakeProfitPoint
)
from app.schemas import (
    APIResponse, PaginationParams, PaginatedResponse, CreateAdvancedTradeRequest,
    TradeDetailResponse, CreateTradeRequest, TradeEntryPointResponse,
    TradeTakeProfitPointResponse, TradeEntryPointUpdate, TradeTakeProfitPointUpdate
)
from app.services.signal_parser import EnhancedSignalParser
from pydantic import BaseModel

# 更新请求模型
class TradeUpdateRequest(BaseModel):
    entry_points: List[Dict[str, Any]]
    take_profit_points: List[Dict[str, Any]]

# 辅助函数
def get_signal_by_id(db: Session, signal_id: int) -> Optional[TelegramSignal]:
    """根据ID获取信号"""
    return db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()

# 配置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# 确保控制台输出
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)

# 只添加一次处理器
if not logger.handlers:
    logger.addHandler(console_handler)

router = APIRouter()

# 信号列表接口
@router.get("/", response_model=PaginatedResponse)
async def get_signals(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    symbol: Optional[str] = Query(None, description="交易对符号"),
    signal_type: Optional[SignalType] = Query(None, description="信号类型"),
    status: Optional[SignalStatus] = Query(None, description="信号状态"),
    source_id: Optional[int] = Query(None, description="信号源ID"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """获取信号列表"""
    try:
        # 构建查询
        query = db.query(TelegramSignal).join(SignalSource)
        
        # 添加过滤条件
        if symbol:
            query = query.filter(TelegramSignal.symbol.ilike(f"%{symbol}%"))
        
        if signal_type:
            query = query.filter(TelegramSignal.signal_type == signal_type)
        
        if status:
            query = query.filter(TelegramSignal.status == status)
        
        if source_id:
            query = query.filter(TelegramSignal.source_id == source_id)
        
        if start_date:
            query = query.filter(TelegramSignal.signal_time >= start_date)
        
        if end_date:
            query = query.filter(TelegramSignal.signal_time <= end_date)
        
        if search:
            query = query.filter(
                or_(
                    TelegramSignal.symbol.ilike(f"%{search}%"),
                    TelegramSignal.raw_message.ilike(f"%{search}%")
                )
            )
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        signals = query.order_by(desc(TelegramSignal.signal_time)).offset(offset).limit(page_size).all()
        
        # 格式化数据
        signal_list = []
        for signal in signals:
            # 获取入场点数据
            entry_points = db.query(TradeEntryPoint).filter(
                TradeEntryPoint.signal_id == signal.id
            ).all()
            
            # 获取止盈点数据
            tp_points = db.query(TradeTakeProfitPoint).filter(
                TradeTakeProfitPoint.signal_id == signal.id
            ).all()
            
            # 计算入场点状态
            entry_status = "pending"
            total_entry_filled = 0
            if entry_points:
                filled_points = [ep for ep in entry_points if ep.status == "filled"]
                partial_points = [ep for ep in entry_points if ep.status == "partial"]
                if filled_points and len(filled_points) == len(entry_points):
                    entry_status = "filled"
                elif filled_points or partial_points:
                    entry_status = "partial"
                total_entry_filled = sum(ep.filled_quantity or 0 for ep in entry_points)
            
            # 计算止盈点状态
            tp_status = "pending"
            total_tp_filled = 0
            total_pnl = 0
            if tp_points:
                filled_tp = [tp for tp in tp_points if tp.status == "filled"]
                partial_tp = [tp for tp in tp_points if tp.status == "partial"]
                if filled_tp and len(filled_tp) == len(tp_points):
                    tp_status = "filled"
                elif filled_tp or partial_tp:
                    tp_status = "partial"
                total_tp_filled = sum(tp.filled_quantity or 0 for tp in tp_points)
                total_pnl = sum(tp.pnl or 0 for tp in tp_points)
            
            # 确定整体状态
            overall_status = "pending"
            if entry_status == "filled" and tp_status == "filled":
                overall_status = "completed"
            elif entry_status in ["partial", "filled"] or tp_status in ["partial", "filled"]:
                overall_status = "active"
            
            signal_data = {
                "id": signal.id,
                "symbol": signal.symbol,
                "signal_type": signal.signal_type.value,
                "entry_price": signal.entry_price,  # 保留原始字段用于兼容
                "stop_loss": signal.stop_loss,
                "take_profit": signal.take_profit,  # 保留原始字段用于兼容
                "leverage": signal.leverage,
                "status": signal.status.value,
                "source_id": signal.source_id,
                "signal_time": signal.signal_time.isoformat(),
                "received_at": signal.received_at.isoformat(),
                "raw_message": signal.raw_message,
                
                # 新增字段
                "entry_points_count": len(entry_points),
                "tp_points_count": len(tp_points),
                "entry_status": entry_status,
                "tp_status": tp_status,
                "overall_status": overall_status,
                "total_entry_filled": total_entry_filled,
                "total_tp_filled": total_tp_filled,
                "total_pnl": total_pnl,
                "pnl_percentage": total_pnl / total_entry_filled * 100 if total_entry_filled > 0 else 0,
                "result": f"{'+' if total_pnl > 0 else ''}{total_pnl / total_entry_filled * 100:.1f}%" if total_entry_filled > 0 and total_pnl != 0 else None
            }
            
            signal_list.append(signal_data)
        
        return PaginatedResponse(
            items=signal_list,
            total=total,
            page=page,
            size=page_size,
            pages=(total + page_size - 1) // page_size
        )
        
    except Exception as e:
        logger.error(f"获取信号列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取信号列表失败")

# 信号统计接口
@router.get("/stats/overview", response_model=APIResponse)
async def get_signal_stats(
    db: Session = Depends(get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数")
):
    """获取信号统计数据"""
    try:
        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 总信号数
        total_signals = db.query(TelegramSignal).filter(
            TelegramSignal.signal_time >= start_date
        ).count()
        
        # 按状态统计
        status_stats = db.query(
            TelegramSignal.status,
            func.count(TelegramSignal.id).label('count')
        ).filter(
            TelegramSignal.signal_time >= start_date
        ).group_by(TelegramSignal.status).all()
        
        # 按类型统计
        type_stats = db.query(
            TelegramSignal.signal_type,
            func.count(TelegramSignal.id).label('count')
        ).filter(
            TelegramSignal.signal_time >= start_date
        ).group_by(TelegramSignal.signal_type).all()
        
        # 按信号源统计
        source_stats = db.query(
            SignalSource.telegram_chat_id,
            func.count(TelegramSignal.id).label('count')
        ).select_from(SignalSource).join(TelegramSignal).filter(
            TelegramSignal.signal_time >= start_date
        ).group_by(SignalSource.telegram_chat_id).all()
        
        # 成功率统计 - 基于入场点和止盈点的执行情况
        success_rate = 0
        if total_signals > 0:
            successful_signals = db.query(TelegramSignal).filter(
                and_(
                    TelegramSignal.signal_time >= start_date,
                    TelegramSignal.status == SignalStatus.EXECUTED
                )
            ).count()
            success_rate = (successful_signals / total_signals) * 100
        
        # 计算平均盈亏
        avg_pnl = db.query(
            func.avg(TradeTakeProfitPoint.pnl)
        ).join(TelegramSignal).filter(
            and_(
                TelegramSignal.signal_time >= start_date,
                TradeTakeProfitPoint.pnl.isnot(None)
            )
        ).scalar() or 0
        
        stats_data = {
            "total_signals": total_signals,
            "success_rate": round(success_rate, 2),
            "avg_pnl": round(avg_pnl, 2),
            "status_distribution": {
                status.value: count for status, count in status_stats
            } if status_stats else {},
            "type_distribution": {
                type_.value: count for type_, count in type_stats
            } if type_stats else {},
            "source_distribution": {
                chat_id: count for chat_id, count in source_stats
            } if source_stats else {},
            "period_days": days
        }
        
        return APIResponse(success=True, message="获取信号统计成功", data=stats_data)
        
    except Exception as e:
        logger.error(f"获取信号统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取信号统计失败")

# 信号源列表接口
@router.get("/sources/list", response_model=APIResponse)
async def get_signal_sources(
    db: Session = Depends(get_db)
):
    """获取信号源列表"""
    try:
        # 检查表是否存在，如果查询失败则返回空列表
        try:
            sources = db.query(SignalSource).filter(SignalSource.is_active == True).all()
        except Exception as query_error:
            logger.warning(f"查询信号源表失败，可能表不存在: {query_error}")
            sources = []
        
        source_list = []
        for source in sources:
            try:
                source_data = {
                    "id": source.id,
                    "name": source.name or source.description or source.telegram_chat_id or '',
                    "description": source.description or '',
                    "total_signals": source.total_signals or 0,
                    "successful_signals": source.successful_signals or 0,
                    "reliability_score": source.reliability_score or 0.0
                }
                source_list.append(source_data)
            except Exception as item_error:
                logger.warning(f"处理信号源{getattr(source, 'id', 'unknown')}失败: {item_error}")
                continue
        
        return APIResponse(success=True, message="获取信号源列表成功", data=source_list)
        
    except Exception as e:
        logger.error(f"获取信号源列表失败: {e}", exc_info=True)
        # 返回空列表而不是抛出异常
        return APIResponse(success=True, message="获取信号源列表成功（无数据）", data=[])

# 删除信号接口
@router.delete("/{signal_id:int}", response_model=APIResponse)
async def delete_signal(
    signal_id: int,
    db: Session = Depends(get_db)
):
    """删除信号和所有相关记录"""
    try:
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        
        if not signal:
            raise HTTPException(status_code=404, detail="信号不存在")
        
        # 由于模型中已经设置了cascade="all, delete-orphan"
        # 删除信号时会自动删除所有相关的入场点和止盈点记录
        db.delete(signal)
        db.commit()
        
        return APIResponse(success=True, message="信号删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除信号失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="删除信号失败")

# 更新信号状态接口
@router.put("/{signal_id:int}/status", response_model=APIResponse)
async def update_signal_status(
    signal_id: int,
    status: SignalStatus,
    db: Session = Depends(get_db)
):
    """更新信号状态"""
    try:
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        
        if not signal:
            raise HTTPException(status_code=404, detail="信号不存在")
        
        signal.status = status
        if status == SignalStatus.EXECUTED:
            signal.processed_at = datetime.now()
        
        db.commit()
        
        return APIResponse(success=True, message="信号状态更新成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新信号状态失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="更新信号状态失败")

# 测试日志接口 - 必须放在动态路由之前
@router.get("/test-logging", response_model=APIResponse)
async def test_logging():
    """测试日志功能"""
    try:
        logger.debug("Debug级别日志测试")
        logger.info("Info级别日志测试")
        logger.warning("Warning级别日志测试")
        logger.error("Error级别日志测试")
        
        # 故意抛出一个异常来测试异常日志
        test_exception = False
        if test_exception:
            raise ValueError("这是一个测试异常")
        
        return APIResponse(
            success=True, 
            message="日志测试完成，请查看控制台输出", 
            data={
                "debug": "Debug级别日志测试",
                "info": "Info级别日志测试", 
                "warning": "Warning级别日志测试",
                "error": "Error级别日志测试"
            }
        )
        
    except Exception as e:
        logger.error(f"日志测试异常: {str(e)}\n{traceback.format_exc()}")
        return APIResponse(
            success=False,
            message=f"日志测试异常: {str(e)}"
        )


# 获取交易编辑数据 - 使用类型约束避免路由冲突
@router.get("/{signal_id:int}/edit", response_model=APIResponse)
async def get_trade_edit_data(
    signal_id: int,
    db: Session = Depends(get_db)
):
    """获取交易编辑数据"""
    try:
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        
        if not signal:
            raise HTTPException(status_code=404, detail="交易不存在")
        
        # 获取入场点
        entry_points = db.query(TradeEntryPoint).filter(
            TradeEntryPoint.signal_id == signal_id
        ).all()
        
        # 获取止盈点
        tp_points = db.query(TradeTakeProfitPoint).filter(
            TradeTakeProfitPoint.signal_id == signal_id
        ).all()
        
        # 格式化入场点数据
        entry_points_data = []
        for ep in entry_points:
            entry_points_data.append({
                "id": ep.id,
                "price": ep.price,
                "allocation": ep.allocation,
                "description": ep.description,
                "status": ep.status,
                "filled_quantity": ep.filled_quantity,
                "actual_price": ep.actual_price,
                "editable": ep.status == "pending"  # 只有pending状态才能编辑
            })
        
        # 格式化止盈点数据
        tp_points_data = []
        for tp in tp_points:
            tp_points_data.append({
                "id": tp.id,
                "price": tp.price,
                "allocation": tp.allocation,
                "description": tp.description,
                "status": tp.status,
                "filled_quantity": tp.filled_quantity,
                "actual_price": tp.actual_price,
                "pnl": tp.pnl,
                "pnl_percentage": tp.pnl_percentage,
                "editable": tp.status == "pending"  # 只有pending状态才能编辑
            })
        
        edit_data = {
            "id": signal.id,
            "symbol": signal.symbol,
            "signal_type": signal.signal_type.value,
            "stop_loss": signal.stop_loss,
            "leverage": signal.leverage,
            "raw_message": signal.raw_message,
            "entry_points": entry_points_data,
            "take_profit_points": tp_points_data,
            "editable_fields": {
                "symbol": False,  # 不允许修改交易对
                "signal_type": False,  # 不允许修改信号类型
                "stop_loss": True,
                "leverage": True,
                "raw_message": True,
                "entry_points": any(ep.status == "pending" for ep in entry_points),
                "take_profit_points": any(tp.status == "pending" for tp in tp_points)
            }
        }
        
        return APIResponse(success=True, message="获取编辑数据成功", data=edit_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取编辑数据失败: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="获取编辑数据失败")


# 更新交易 - 使用类型约束避免路由冲突
@router.put("/{signal_id:int}", response_model=APIResponse)
async def update_trade(
    signal_id: int,
    request: CreateTradeRequest,  # 复用创建请求的结构
    db: Session = Depends(get_db)
):
    """更新交易"""
    try:
        logger.info(f"开始更新交易 {signal_id}")
        
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        if not signal:
            raise HTTPException(status_code=404, detail="交易不存在")
        
        # 验证分配比例
        entry_allocation_sum = sum(ep.allocation for ep in request.entry_points)
        tp_allocation_sum = sum(tp.allocation for tp in request.take_profit_points)
        
        if abs(entry_allocation_sum - 1.0) > 0.01:
            raise HTTPException(status_code=400, detail="入场点分配比例总和必须等于1")
        
        if abs(tp_allocation_sum - 1.0) > 0.01:
            raise HTTPException(status_code=400, detail="止盈点分配比例总和必须等于1")
        
        # 更新信号基本信息（只更新允许修改的字段）
        signal.stop_loss = request.stop_loss
        signal.leverage = request.leverage
        signal.raw_message = request.raw_message
        
        # 获取现有的入场点
        existing_entry_points = db.query(TradeEntryPoint).filter(
            TradeEntryPoint.signal_id == signal_id
        ).all()
        
        # 获取现有的止盈点
        existing_tp_points = db.query(TradeTakeProfitPoint).filter(
            TradeTakeProfitPoint.signal_id == signal_id
        ).all()
        
        # 更新入场点
        # 首先收集请求中所有入场点的信息
        req_entry_ids = set()
        req_entry_data = {}
        
        for req_ep in request.entry_points:
            if hasattr(req_ep, 'id') and req_ep.id:
                req_entry_ids.add(req_ep.id)
                req_entry_data[req_ep.id] = req_ep
        
        # 处理现有入场点
        for existing_ep in existing_entry_points:
            if existing_ep.id in req_entry_ids:
                # 更新现有入场点（只有pending状态可以修改）
                if existing_ep.status == "pending":
                    req_ep = req_entry_data[existing_ep.id]
                    existing_ep.price = req_ep.price
                    existing_ep.allocation = req_ep.allocation
                    existing_ep.description = req_ep.description
            # 注意：不再自动删除不在请求中的记录，避免意外删除
        
        # 创建新的入场点（没有id或id不在现有记录中的）
        for req_ep in request.entry_points:
            if not (hasattr(req_ep, 'id') and req_ep.id):
                # 检查是否已存在相同的入场点（防止重复创建）
                existing_same = next((ep for ep in existing_entry_points 
                                    if abs(ep.price - req_ep.price) < 0.00001 
                                    and ep.description == req_ep.description), None)
                
                if not existing_same:
                    # 创建全新的入场点
                    new_entry = TradeEntryPoint(
                        signal_id=signal_id,
                        price=req_ep.price,
                        allocation=req_ep.allocation,
                        description=req_ep.description,
                        status="pending"
                    )
                    db.add(new_entry)
                    logger.info(f"创建新入场点: price={req_ep.price}, description={req_ep.description}")
                else:
                    logger.warning(f"跳过重复的入场点: price={req_ep.price}, description={req_ep.description}, existing_id={existing_same.id}")

        # 更新止盈点
        # 首先收集请求中所有止盈点的信息
        req_tp_ids = set()
        req_tp_data = {}
        
        for req_tp in request.take_profit_points:
            if hasattr(req_tp, 'id') and req_tp.id:
                req_tp_ids.add(req_tp.id)
                req_tp_data[req_tp.id] = req_tp
        
        # 处理现有止盈点
        for existing_tp in existing_tp_points:
            if existing_tp.id in req_tp_ids:
                # 更新现有止盈点（只有pending状态可以修改）
                if existing_tp.status == "pending":
                    req_tp = req_tp_data[existing_tp.id]
                    existing_tp.price = req_tp.price
                    existing_tp.allocation = req_tp.allocation
                    existing_tp.description = req_tp.description
            # 注意：不再自动删除不在请求中的记录，避免意外删除
        
        # 创建新的止盈点（没有id或id不在现有记录中的）
        for req_tp in request.take_profit_points:
            if not (hasattr(req_tp, 'id') and req_tp.id):
                # 检查是否已存在相同的止盈点（防止重复创建）
                existing_same = next((tp for tp in existing_tp_points 
                                    if abs(tp.price - req_tp.price) < 0.00001 
                                    and tp.description == req_tp.description), None)
                
                if not existing_same:
                    # 创建全新的止盈点
                    new_tp = TradeTakeProfitPoint(
                        signal_id=signal_id,
                        price=req_tp.price,
                        allocation=req_tp.allocation,
                        description=req_tp.description,
                        status="pending"
                    )
                    db.add(new_tp)
                    logger.info(f"创建新止盈点: price={req_tp.price}, description={req_tp.description}")
                else:
                    logger.warning(f"跳过重复的止盈点: price={req_tp.price}, description={req_tp.description}, existing_id={existing_same.id}")
        
        db.commit()
        
        logger.info(f"交易 {signal_id} 更新成功")
        return APIResponse(success=True, message="交易更新成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新交易失败: {str(e)}\n{traceback.format_exc()}")
        db.rollback()
        raise HTTPException(status_code=500, detail="更新交易失败")


# 获取交易详情 - 使用类型约束
@router.get("/{signal_id:int}/detail", response_model=APIResponse)
async def get_trade_detail(
    signal_id: int,
    db: Session = Depends(get_db)
):
    """获取交易详情"""
    try:
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        
        if not signal:
            raise HTTPException(status_code=404, detail="交易不存在")
        
        # 获取入场点
        entry_points = db.query(TradeEntryPoint).filter(
            TradeEntryPoint.signal_id == signal_id
        ).all()
        
        # 获取止盈点
        tp_points = db.query(TradeTakeProfitPoint).filter(
            TradeTakeProfitPoint.signal_id == signal_id
        ).all()
        
        # 计算总体PnL
        total_pnl = 0.0
        total_pnl_percentage = 0.0
        
        for tp in tp_points:
            if tp.pnl:
                total_pnl += tp.pnl
            if tp.pnl_percentage:
                total_pnl_percentage += tp.pnl_percentage * tp.allocation
        
        # 确定整体状态
        entry_statuses = [ep.status for ep in entry_points]
        tp_statuses = [tp.status for tp in tp_points]
        
        all_statuses = entry_statuses + tp_statuses
        
        if all(status in ["completed", "filled"] for status in all_statuses):
            overall_status = "completed"
        elif any(status in ["filled", "partial"] for status in all_statuses):
            overall_status = "active"
        elif all(status == "cancelled" for status in all_statuses):
            overall_status = "cancelled"
        else:
            overall_status = "pending"
        
        # 格式化入场点数据
        entry_points_data = []
        for ep in entry_points:
            entry_points_data.append({
                "id": ep.id,
                "price": ep.price,
                "allocation": ep.allocation,
                "description": ep.description,
                "status": ep.status,
                "filled_quantity": ep.filled_quantity,
                "actual_price": ep.actual_price
            })
        
        # 格式化止盈点数据
        tp_points_data = []
        for tp in tp_points:
            tp_points_data.append({
                "id": tp.id,
                "price": tp.price,
                "allocation": tp.allocation,
                "description": tp.description,
                "status": tp.status,
                "filled_quantity": tp.filled_quantity,
                "actual_price": tp.actual_price,
                "pnl": tp.pnl,
                "pnl_percentage": tp.pnl_percentage
            })
        
        trade_detail = {
            "signal": {
            "id": signal.id,
            "symbol": signal.symbol,
            "signal_type": signal.signal_type.value,
            "stop_loss": signal.stop_loss,
            "leverage": signal.leverage,
            "raw_message": signal.raw_message,
                "signal_time": signal.signal_time.isoformat() if signal.signal_time else None,
                "overall_status": overall_status
            },
            "entry_points": entry_points_data,
            "take_profit_points": tp_points_data,
            "performance": {
                "total_pnl": total_pnl,
                "total_pnl_percentage": total_pnl_percentage,
                "entry_points_count": len(entry_points),
                "tp_points_count": len(tp_points)
            }
        }
        
        return APIResponse(success=True, message="获取交易详情成功", data=trade_detail)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取交易详情失败: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="获取交易详情失败")

class CreateSignalRequest(BaseModel):
    raw_message: str
    source_name: str = "手动输入"

class ParseSignalRequest(BaseModel):
    raw_message: str

# 测试信号解析接口
@router.post("/parse", response_model=APIResponse)
async def parse_signal_text(
    request: ParseSignalRequest,
    db: Session = Depends(get_db)
):
    """测试解析信号文本"""
    try:
        parser = EnhancedSignalParser()
        parsed_result = await parser.parse_message(request.raw_message)
        
        # 添加详细的调试信息
        debug_info = {
            "original_message": request.raw_message,
            "cleaned_message": parser._clean_message(request.raw_message),
            "cornix_header_match": None,
            "extracted_fields": {}
        }
        
        # 检查Cornix格式匹配
        cleaned_message = parser._clean_message(request.raw_message)
        header_match = parser.cornix_parser.compiled_cornix['header_pattern'].search(cleaned_message)
        if header_match:
            debug_info["cornix_header_match"] = {
                "symbol": header_match.group(1),
                "direction": header_match.group(2)
            }
            
            # 测试各个字段的提取
            debug_info["extracted_fields"] = {
                "entry_price": parser._extract_cornix_entry_price(cleaned_message),
                "stop_loss": parser._extract_cornix_stop_loss(cleaned_message),
                "take_profit_targets": parser._extract_cornix_take_profits(cleaned_message),
                "leverage": parser._extract_cornix_leverage(cleaned_message)
            }
        else:
            # 通用格式测试
            debug_info["extracted_fields"] = {
                "signal_type": parser._extract_signal_type(cleaned_message),
                "symbol": parser._extract_symbol(cleaned_message),
                "entry_price": parser._extract_entry_price(cleaned_message),
                "stop_loss": parser._extract_stop_loss(cleaned_message),
                "take_profit": parser._extract_take_profit(cleaned_message),
                "leverage": parser._extract_leverage(cleaned_message)
            }
        
        if parsed_result:
            return APIResponse(
                success=True, 
                message="信号解析成功", 
                data={
                    "parsed": True,
                    "signal_data": parsed_result,
                    "confidence": parsed_result.get('confidence_score', 0),
                    "debug_info": debug_info
                }
            )
        else:
            return APIResponse(
                success=False, 
                message="无法解析该文本为有效交易信号", 
                data={
                    "parsed": False,
                    "signal_data": None,
                    "confidence": 0,
                    "debug_info": debug_info
                }
            )
        
    except Exception as e:
        logger.error(f"解析信号文本失败: {e}")
        raise HTTPException(status_code=500, detail="解析信号文本失败")

# 手动创建信号接口
@router.post("/create", response_model=APIResponse)
async def create_manual_signal(
    request: CreateSignalRequest,
    db: Session = Depends(get_db)
):
    """手动创建信号"""
    try:
        # 解析信号文本
        parser = EnhancedSignalParser()
        parsed_result = await parser.parse_message(request.raw_message)
        
        if not parsed_result:
            raise HTTPException(status_code=400, detail="无法解析该文本为有效信号")
        
        # 获取或创建信号源
        chat_id = f"manual_{request.source_name.lower().replace(' ', '_')}"
        source = db.query(SignalSource).filter(SignalSource.telegram_chat_id == chat_id).first()
        if not source:
            source = SignalSource(
                description=f"手动输入的信号源: {request.source_name}",
                telegram_chat_id=chat_id,
                is_active=True,
                total_signals=0,
                successful_signals=0
            )
            db.add(source)
            db.flush()  # 获取source.id
        
        # 创建信号记录
        signal = TelegramSignal(
            symbol=parsed_result['symbol'],
            signal_type=parsed_result['signal_type'],
            entry_price=parsed_result.get('entry_price'),
            stop_loss=parsed_result.get('stop_loss'),
            take_profit=parsed_result.get('take_profit'),
            quantity=parsed_result.get('quantity'),
            leverage=parsed_result.get('leverage', 1),
            confidence_score=parsed_result.get('confidence_score', 0.5),
            status=SignalStatus.PENDING,
            source_id=source.id,
            signal_time=datetime.now(),
            received_at=datetime.now(),
            raw_message=request.raw_message
        )
        
        db.add(signal)
        
        # 更新信号源统计
        source.total_signals += 1
        
        db.commit()
        
        return APIResponse(success=True, message="信号创建成功", data={"signal_id": signal.id})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建信号失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="创建信号失败")

class CreateSignalFromFormRequest(BaseModel):
    source_name: str = "手动输入"
    symbol: str
    signal_type: str
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    quantity: Optional[float] = None
    leverage: int = 1
    confidence_score: float = 0.5
    raw_message: str = ""

# 从表单数据创建信号接口
@router.post("/create-from-form", response_model=APIResponse)
async def create_signal_from_form(
    request: CreateSignalFromFormRequest,
    db: Session = Depends(get_db)
):
    """从表单数据创建信号"""
    try:
        # 验证信号类型
        try:
            signal_type_enum = SignalType(request.signal_type.lower())
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的信号类型: {request.signal_type}")
        
        # 获取或创建信号源
        chat_id = f"manual_{request.source_name.lower().replace(' ', '_')}"
        source = db.query(SignalSource).filter(SignalSource.telegram_chat_id == chat_id).first()
        if not source:
            source = SignalSource(
                description=f"手动输入的信号源: {request.source_name}",
                telegram_chat_id=chat_id,
                is_active=True,
                total_signals=0,
                successful_signals=0
            )
            db.add(source)
            db.flush()  # 获取source.id
        
        # 标准化交易对符号
        symbol = request.symbol.upper()
        if not symbol.endswith('USDT') and not symbol.endswith('USD'):
            symbol += 'USDT'
        
        # 创建信号记录
        signal = TelegramSignal(
            symbol=symbol,
            signal_type=signal_type_enum,
            entry_price=request.entry_price,
            stop_loss=request.stop_loss,
            take_profit=request.take_profit,
            quantity=request.quantity,
            leverage=request.leverage,
            confidence_score=request.confidence_score,
            status=SignalStatus.PENDING,
            source_id=source.id,
            signal_time=datetime.now(),
            received_at=datetime.now(),
            raw_message=request.raw_message
        )
        
        db.add(signal)
        
        # 更新信号源统计
        source.total_signals += 1
        
        db.commit()
        
        return APIResponse(success=True, message="交易创建成功", data={"signal_id": signal.id})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"从表单创建信号失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="创建交易失败")

# 高级交易配置相关API
@router.post("/advanced-trade", response_model=APIResponse)
async def create_advanced_trade(
    request: CreateAdvancedTradeRequest,
    db: Session = Depends(get_db)
):
    """创建高级交易配置（支持多入场价和多止盈点）"""
    try:
        logger.info(f"收到高级交易创建请求: {request}")
        config = request.config
        
        # 验证入场点分配总和
        if config.auto_distribute_funds:
            # 自动平均分配资金
            entry_count = len(config.entry_points)
            allocation_per_entry = 1.0 / entry_count
            for entry in config.entry_points:
                entry.allocation = allocation_per_entry
        else:
            # 检查手动分配是否合理
            total_entry_allocation = sum(entry.allocation for entry in config.entry_points)
            if abs(total_entry_allocation - 1.0) > 0.001:  # 允许小的浮点误差
                raise HTTPException(status_code=400, detail=f"入场点资金分配总和必须为1.0，当前为{total_entry_allocation}")
        
        # 验证止盈点分配总和
        total_tp_allocation = sum(tp.allocation for tp in config.take_profit_points)
        if abs(total_tp_allocation - 1.0) > 0.001:
            raise HTTPException(status_code=400, detail=f"止盈点分配总和必须为1.0，当前为{total_tp_allocation}")
        
        # 获取或创建默认信号源
        source = db.query(SignalSource).filter(SignalSource.telegram_chat_id == "advanced_trade").first()
        if not source:
            source = SignalSource(
                telegram_chat_id="advanced_trade",
                description="高级交易配置创建的信号源",
                is_active=True
            )
            db.add(source)
            db.flush()
        
        # 创建主信号
            signal = TelegramSignal(
                source_id=source.id,
                symbol=config.symbol,
                signal_type=config.signal_type,
                stop_loss=config.stop_loss,
                leverage=config.leverage,
            raw_message=config.raw_message or f"高级交易配置: {config.description or config.symbol}",
                signal_time=datetime.now(),
                status=SignalStatus.PENDING
            )
            
            db.add(signal)
            db.flush()
        
        # 创建入场点
        for i, entry_point in enumerate(config.entry_points):
            from app.models import TradeEntryPoint
            entry = TradeEntryPoint(
                signal_id=signal.id,
                price=entry_point.price,
                allocation=entry_point.allocation,
                description=entry_point.description or f"入场点 {i+1}",
                status="pending"
            )
            db.add(entry)
        
        # 创建止盈点
        for i, tp_point in enumerate(config.take_profit_points):
            from app.models import TradeTakeProfitPoint
            tp = TradeTakeProfitPoint(
                signal_id=signal.id,
                price=tp_point.price,
                allocation=tp_point.allocation,
                description=tp_point.description or f"止盈点 {i+1}",
                status="pending"
            )
            db.add(tp)
        
        db.commit()
        
        logger.info(f"高级交易创建成功，信号ID: {signal.id}")
        
        return APIResponse(
            success=True, 
            message="高级交易创建成功", 
            data={
                "signal_id": signal.id,
                "entry_points_count": len(config.entry_points),
                "take_profit_points_count": len(config.take_profit_points)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"创建高级交易配置失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建高级交易配置失败: {str(e)}")


@router.get("/advanced-trade/template", response_model=APIResponse)
async def get_advanced_trade_template():
    """获取高级交易模板"""
    template = {
        "symbol": "BTCUSDT",
        "signal_type": "long",
        "entry_points": [
            {"price": 45000, "allocation": 0.5, "description": "主要入场点"},
            {"price": 44000, "allocation": 0.5, "description": "补仓点"}
        ],
        "take_profit_points": [
            {"price": 47000, "allocation": 0.5, "description": "第一止盈"},
            {"price": 48000, "allocation": 0.5, "description": "第二止盈"}
        ],
        "stop_loss": 43000,
        "leverage": 10,
        "total_quantity": 0.1,
        "description": "示例交易配置"
    }
    
    return APIResponse(success=True, message="获取模板成功", data=template)


# 创建新的交易
@router.post("/create-trade", response_model=APIResponse)
async def create_trade(
    request: CreateTradeRequest,
    db: Session = Depends(get_db)
):
    """创建新的交易"""
    try:
        # 验证分配比例
        entry_allocation_sum = sum(ep.allocation for ep in request.entry_points)
        tp_allocation_sum = sum(tp.allocation for tp in request.take_profit_points)
        
        if abs(entry_allocation_sum - 1.0) > 0.01:
            raise HTTPException(status_code=400, detail="入场点分配比例总和必须等于1")
        
        if abs(tp_allocation_sum - 1.0) > 0.01:
            raise HTTPException(status_code=400, detail="止盈点分配比例总和必须等于1")
        
        # 创建默认信号源（如果需要）
        default_source = db.query(SignalSource).filter(
            SignalSource.telegram_chat_id == "manual_trade"
        ).first()
        
        if not default_source:
            default_source = SignalSource(
                telegram_chat_id="manual_trade",
                description="手动创建的交易",
                is_active=True
            )
            db.add(default_source)
            db.commit()
            db.refresh(default_source)
        
        # 创建信号
        signal = TelegramSignal(
            source_id=default_source.id,
            symbol=request.symbol,
            signal_type=request.signal_type,
            stop_loss=request.stop_loss,
            leverage=request.leverage,
            raw_message=request.raw_message or "",
            signal_time=datetime.now(),
            status=SignalStatus.PENDING
        )
        
        db.add(signal)
        db.commit()
        db.refresh(signal)
        
        # 创建入场点
        for ep_data in request.entry_points:
            entry_point = TradeEntryPoint(
                signal_id=signal.id,
                price=ep_data.price,
                allocation=ep_data.allocation,
                description=ep_data.description or f"入场点 {ep_data.price}",
                status="pending"
            )
            db.add(entry_point)
        
        # 创建止盈点
        for tp_data in request.take_profit_points:
            tp_point = TradeTakeProfitPoint(
                signal_id=signal.id,
                price=tp_data.price,
                allocation=tp_data.allocation,
                description=tp_data.description or f"止盈点 {tp_data.price}",
                status="pending"
            )
            db.add(tp_point)
        
        db.commit()
        
        return APIResponse(success=True, message="交易创建成功", data={"signal_id": signal.id})
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建交易失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="创建交易失败")


# 更新入场点
@router.put("/entry-point/{entry_point_id}", response_model=APIResponse)
async def update_entry_point(
    entry_point_id: int,
    request: TradeEntryPointUpdate,
    db: Session = Depends(get_db)
):
    """更新入场点"""
    try:
        entry_point = db.query(TradeEntryPoint).filter(
            TradeEntryPoint.id == entry_point_id
        ).first()
        
        if not entry_point:
            raise HTTPException(status_code=404, detail="入场点不存在")
        
        # 只允许更新未成交的入场点
        if entry_point.status == "filled":
            raise HTTPException(status_code=400, detail="已成交的入场点不能修改")
        
        # 更新字段
        if request.price is not None:
            entry_point.price = request.price
        if request.allocation is not None:
            entry_point.allocation = request.allocation
        if request.description is not None:
            entry_point.description = request.description
        if request.status is not None:
            entry_point.status = request.status
        
        db.commit()
        
        return APIResponse(success=True, message="入场点更新成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新入场点失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="更新入场点失败")


# 更新止盈点
@router.put("/take-profit-point/{tp_point_id}", response_model=APIResponse)
async def update_take_profit_point(
    tp_point_id: int,
    request: TradeTakeProfitPointUpdate,
    db: Session = Depends(get_db)
):
    """更新止盈点"""
    try:
        logger.info(f"更新止盈点请求: tp_point_id={tp_point_id}, data={request}")
        
        tp_point = db.query(TradeTakeProfitPoint).filter(
            TradeTakeProfitPoint.id == tp_point_id
        ).first()
        
        if not tp_point:
            raise HTTPException(status_code=404, detail="止盈点不存在")
        
        # 只允许更新未成交的止盈点
        if tp_point.status == "filled":
            raise HTTPException(status_code=400, detail="已成交的止盈点不能修改")
        
        # 更新字段
        if request.price is not None:
            tp_point.price = request.price
        if request.allocation is not None:
            tp_point.allocation = request.allocation
        if request.description is not None:
            tp_point.description = request.description
        if request.status is not None:
            tp_point.status = request.status
        
        db.commit()
        
        logger.info(f"止盈点更新成功: {tp_point_id}")
        return APIResponse(success=True, message="止盈点更新成功")
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"更新止盈点失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        db.rollback()
        raise HTTPException(status_code=500, detail="更新止盈点失败")


# 调试高级交易创建接口
@router.post("/debug-advanced-trade", response_model=APIResponse)
async def debug_advanced_trade(
    request: CreateAdvancedTradeRequest,
    db: Session = Depends(get_db)
):
    """调试版本的高级交易创建接口"""
    try:
        logger.info("=" * 50)
        logger.info("开始处理高级交易创建请求")
        logger.info(f"请求数据: {request}")
        logger.info(f"请求类型: {type(request)}")
        
        config = request.config
        logger.info(f"配置数据: {config}")
        logger.info(f"配置类型: {type(config)}")
        
        logger.info(f"入场点数量: {len(config.entry_points)}")
        logger.info(f"止盈点数量: {len(config.take_profit_points)}")
        
        for i, ep in enumerate(config.entry_points):
            logger.info(f"入场点{i+1}: 价格={ep.price}, 分配={ep.allocation}, 描述={ep.description}")
        
        for i, tp in enumerate(config.take_profit_points):
            logger.info(f"止盈点{i+1}: 价格={tp.price}, 分配={tp.allocation}, 描述={tp.description}")
        
        # 检查数据库连接
        logger.info("检查数据库连接...")
        test_query = db.query(SignalSource).count()
        logger.info(f"当前信号源数量: {test_query}")
        
        # 验证分配比例
        logger.info("验证分配比例...")
        total_entry_allocation = sum(ep.allocation for ep in config.entry_points)
        total_tp_allocation = sum(tp.allocation for tp in config.take_profit_points)
        logger.info(f"入场点总分配: {total_entry_allocation}")
        logger.info(f"止盈点总分配: {total_tp_allocation}")
        
        if abs(total_entry_allocation - 1.0) > 0.001:
            error_msg = f"入场点分配总和不等于1.0: {total_entry_allocation}"
            logger.error(error_msg)
            raise HTTPException(status_code=400, detail=error_msg)
        
        if abs(total_tp_allocation - 1.0) > 0.001:
            error_msg = f"止盈点分配总和不等于1.0: {total_tp_allocation}"
            logger.error(error_msg)
            raise HTTPException(status_code=400, detail=error_msg)
        
        logger.info("验证通过，开始创建信号...")
        
        return APIResponse(
            success=True,
            message="调试完成，数据验证通过",
            data={
                "entry_allocation": total_entry_allocation,
                "tp_allocation": total_tp_allocation,
                "signal_sources_count": test_query
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"调试失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=f"调试失败: {str(e)}")

# 信号详情接口 - 通用接口
@router.get("/{signal_id:int}", response_model=APIResponse)
async def get_signal_detail(
    signal_id: int,
    db: Session = Depends(get_db)
):
    """获取信号详情"""
    try:
        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
        
        if not signal:
            raise HTTPException(status_code=404, detail="信号不存在")
        
        # 获取交易记录
        trades = db.query(SignalTrade).filter(SignalTrade.signal_id == signal_id).all()
        
        trade_list = []
        for trade in trades:
            trade_data = {
                "id": trade.id,
                "trade_id": trade.trade_id,
                "order_id": trade.order_id,
                "symbol": trade.symbol,
                "side": trade.side,
                "entry_price": trade.entry_price,
                "exit_price": trade.exit_price,
                "quantity": trade.quantity,
                "pnl": trade.pnl,
                "pnl_percentage": trade.pnl_percentage,
                "fee": trade.fee,
                "status": trade.status,
                "entry_time": trade.entry_time.isoformat() if trade.entry_time else None,
                "exit_time": trade.exit_time.isoformat() if trade.exit_time else None
            }
            trade_list.append(trade_data)
        
        signal_data = {
            "id": signal.id,
            "symbol": signal.symbol,
            "signal_type": signal.signal_type.value,
            "entry_price": signal.entry_price,
            "stop_loss": signal.stop_loss,
            "take_profit": signal.take_profit,
            "quantity": signal.quantity,
            "leverage": signal.leverage,
            "confidence_score": signal.confidence_score,
            "status": signal.status.value,
            "source_name": signal.source.telegram_chat_id,
            "source_id": signal.source_id,
            "signal_time": signal.signal_time.isoformat(),
            "received_at": signal.received_at.isoformat(),
            "processed_at": signal.processed_at.isoformat() if signal.processed_at else None,
            "raw_message": signal.raw_message,
            "trades": trade_list
        }
        
        return APIResponse(success=True, message="获取信号详情成功", data=signal_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取信号详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取信号详情失败")

@router.post("/{signal_id}/update")
async def update_trade(signal_id: int, request: TradeUpdateRequest, db: Session = Depends(get_db)):
    """更新交易记录"""
    try:
        signal = get_signal_by_id(db, signal_id)
        if not signal:
            raise HTTPException(status_code=404, detail="Signal not found")
        
        # 获取现有的入场点和止盈点
        existing_entry_points = db.query(TradeEntryPoint).filter_by(signal_id=signal_id).all()
        existing_tp_points = db.query(TradeTakeProfitPoint).filter_by(signal_id=signal_id).all()
        
        # 收集请求中所有入场点的信息
        req_entry_ids = set()
        req_entry_data = {}
        
        for req_ep in request.entry_points:
            if hasattr(req_ep, 'id') and req_ep.id:
                req_entry_ids.add(req_ep.id)
                req_entry_data[req_ep.id] = req_ep
        
        # 处理现有入场点
        for existing_ep in existing_entry_points:
            if existing_ep.id in req_entry_ids:
                # 更新现有入场点（只有pending状态可以修改）
                if existing_ep.status == "pending":
                    req_ep = req_entry_data[existing_ep.id]
                    existing_ep.price = req_ep.price
                    existing_ep.allocation = req_ep.allocation
                    existing_ep.description = req_ep.description
            # 注意：不再自动删除不在请求中的记录，避免意外删除
        
        # 创建新的入场点（没有id或id不在现有记录中的）
        for req_ep in request.entry_points:
            if not (hasattr(req_ep, 'id') and req_ep.id):
                # 检查是否已存在相同的入场点（防止重复创建）
                existing_same = next((ep for ep in existing_entry_points 
                                    if abs(ep.price - req_ep.price) < 0.00001 
                                    and ep.description == req_ep.description), None)
                
                if not existing_same:
                    # 创建全新的入场点
                    new_entry = TradeEntryPoint(
                        signal_id=signal_id,
                        price=req_ep.price,
                        allocation=req_ep.allocation,
                        description=req_ep.description,
                        status="pending"
                    )
                    db.add(new_entry)
                    logger.info(f"创建新入场点: price={req_ep.price}, description={req_ep.description}")
                else:
                    logger.warning(f"跳过重复的入场点: price={req_ep.price}, description={req_ep.description}, existing_id={existing_same.id}")

        # 更新止盈点
        # 首先收集请求中所有止盈点的信息
        req_tp_ids = set()
        req_tp_data = {}
        
        for req_tp in request.take_profit_points:
            if hasattr(req_tp, 'id') and req_tp.id:
                req_tp_ids.add(req_tp.id)
                req_tp_data[req_tp.id] = req_tp
        
        # 处理现有止盈点
        for existing_tp in existing_tp_points:
            if existing_tp.id in req_tp_ids:
                # 更新现有止盈点（只有pending状态可以修改）
                if existing_tp.status == "pending":
                    req_tp = req_tp_data[existing_tp.id]
                    existing_tp.price = req_tp.price
                    existing_tp.allocation = req_tp.allocation
                    existing_tp.description = req_tp.description
            # 注意：不再自动删除不在请求中的记录，避免意外删除
        
        # 创建新的止盈点（没有id或id不在现有记录中的）
        for req_tp in request.take_profit_points:
            if not (hasattr(req_tp, 'id') and req_tp.id):
                # 检查是否已存在相同的止盈点（防止重复创建）
                existing_same = next((tp for tp in existing_tp_points 
                                    if abs(tp.price - req_tp.price) < 0.00001 
                                    and tp.description == req_tp.description), None)
                
                if not existing_same:
                    # 创建全新的止盈点
                    new_tp = TradeTakeProfitPoint(
                        signal_id=signal_id,
                        price=req_tp.price,
                        allocation=req_tp.allocation,
                        description=req_tp.description,
                        status="pending"
                    )
                    db.add(new_tp)
                    logger.info(f"创建新止盈点: price={req_tp.price}, description={req_tp.description}")
                else:
                    logger.warning(f"跳过重复的止盈点: price={req_tp.price}, description={req_tp.description}, existing_id={existing_same.id}")
        
        db.commit()
        
        return {"message": "Trade updated successfully"}
    
    except Exception as e:
        logger.error(f"更新交易失败 (signal_id={signal_id}): {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{signal_id}/entry_points/{entry_point_id}")
async def delete_entry_point(signal_id: int, entry_point_id: int, db: Session = Depends(get_db)):
    """删除入场点"""
    try:
        # 验证信号存在
        signal = get_signal_by_id(db, signal_id)
        if not signal:
            raise HTTPException(status_code=404, detail="Signal not found")
        
        # 查找入场点
        entry_point = db.query(TradeEntryPoint).filter_by(
            id=entry_point_id, 
            signal_id=signal_id
        ).first()
        
        if not entry_point:
            raise HTTPException(status_code=404, detail="Entry point not found")
        
        # 只能删除pending状态的入场点
        if entry_point.status != "pending":
            raise HTTPException(status_code=400, detail=f"Cannot delete entry point with status: {entry_point.status}")
        
        db.delete(entry_point)
        db.commit()
        
        logger.info(f"删除入场点成功: signal_id={signal_id}, entry_point_id={entry_point_id}")
        return {"message": "Entry point deleted successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除入场点失败: signal_id={signal_id}, entry_point_id={entry_point_id}, error={str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{signal_id}/take_profit_points/{take_profit_id}")
async def delete_take_profit_point(signal_id: int, take_profit_id: int, db: Session = Depends(get_db)):
    """删除止盈点"""
    try:
        # 验证信号存在
        signal = get_signal_by_id(db, signal_id)
        if not signal:
            raise HTTPException(status_code=404, detail="Signal not found")
        
        # 查找止盈点
        take_profit = db.query(TradeTakeProfitPoint).filter_by(
            id=take_profit_id, 
            signal_id=signal_id
        ).first()
        
        if not take_profit:
            raise HTTPException(status_code=404, detail="Take profit point not found")
        
        # 只能删除pending状态的止盈点
        if take_profit.status != "pending":
            raise HTTPException(status_code=400, detail=f"Cannot delete take profit point with status: {take_profit.status}")
        
        db.delete(take_profit)
        db.commit()
        
        logger.info(f"删除止盈点成功: signal_id={signal_id}, take_profit_id={take_profit_id}")
        return {"message": "Take profit point deleted successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除止盈点失败: signal_id={signal_id}, take_profit_id={take_profit_id}, error={str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

 