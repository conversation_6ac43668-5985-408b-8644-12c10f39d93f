from pydantic_settings import BaseSettings
from typing import List, Optional, Any
from sqlalchemy.orm import Session


class Settings(BaseSettings):
    # 启动必需的配置（不能通过页面修改）
    database_url: str = "postgresql://postgres:postgres@localhost:5432/husky"
    redis_url: str = "redis://localhost:6379/0"
    secret_key: str = "your_secret_key_here_change_in_production"
    algorithm: str = "HS256"
    debug: bool = True
    environment: str = "development"
    allowed_origins: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # 动态配置项（可以通过页面配置的项目）
    # 这些属性将在运行时从数据库加载
    _dynamic_configs: dict = {}
    _db_session: Optional[Session] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    def set_db_session(self, db: Session):
        """设置数据库会话，用于动态配置"""
        self._db_session = db
    
    def get_dynamic_config(self, key: str, default: Any = None) -> Any:
        """获取动态配置值"""
        if not self._db_session:
            return default
        
        from app.services.config_manager import config_manager
        return config_manager.get_config(key, self._db_session, default)
    
    # 动态配置属性
    @property
    def telegram_api_id(self) -> Optional[int]:
        """Telegram API ID"""
        value = self.get_dynamic_config("telegram_api_id", None)
        return int(value) if value else None
    
    @property
    def telegram_api_hash(self) -> str:
        """Telegram API Hash"""
        return self.get_dynamic_config("telegram_api_hash", "")
    
    @property
    def telegram_session_string(self) -> str:
        """Telegram会话字符串"""
        return self.get_dynamic_config("telegram_session_string", "")
    
    @property
    def telegram_phone_number(self) -> str:
        """Telegram手机号"""
        return self.get_dynamic_config("telegram_phone_number", "")
    
    @property
    def access_token_expire_minutes(self) -> int:
        return self.get_dynamic_config("access_token_expire_minutes", 30)
    
    @property
    def freqtrade_api_url(self) -> str:
        return self.get_dynamic_config("freqtrade_api_url", "http://localhost:8080")
    
    @property
    def freqtrade_api_username(self) -> str:
        return self.get_dynamic_config("freqtrade_api_username", "freqtrade")
    
    @property
    def freqtrade_api_password(self) -> str:
        return self.get_dynamic_config("freqtrade_api_password", "freqtrade")
    
    @property
    def binance_api_key(self) -> Optional[str]:
        return self.get_dynamic_config("binance_api_key", None)
    
    @property
    def binance_secret_key(self) -> Optional[str]:
        return self.get_dynamic_config("binance_secret_key", None)
    
    @property
    def log_level(self) -> str:
        return self.get_dynamic_config("log_level", "INFO")
    
    @property
    def log_file(self) -> str:
        return self.get_dynamic_config("log_file", "logs/app.log")
    
    @property
    def max_concurrent_trades(self) -> int:
        return self.get_dynamic_config("max_concurrent_trades", 5)
    
    @property
    def default_stake_amount(self) -> float:
        return self.get_dynamic_config("default_stake_amount", 100.0)
    
    @property
    def auto_trading_enabled(self) -> bool:
        return self.get_dynamic_config("auto_trading_enabled", False)
    
    @property
    def backtest_trade_amount_mode(self) -> str:
        """回测交易金额模式"""
        return self.get_dynamic_config("backtest_trade_amount_mode", "percentage")
    
    @property
    def backtest_trade_amount_value(self) -> float:
        """回测每笔交易金额/百分比"""
        return self.get_dynamic_config("backtest_trade_amount_value", 0.2)
    
    @property
    def backtest_max_positions(self) -> int:
        """回测最大同时持仓数"""
        return self.get_dynamic_config("backtest_max_positions", 5)
    
    @property
    def backtest_max_allocation_percent(self) -> float:
        """回测最大资金使用比例"""
        return self.get_dynamic_config("backtest_max_allocation_percent", 0.8)
    
    @property
    def is_dry_run_mode(self) -> bool:
        """是否为模拟交易模式"""
        return self.get_dynamic_config("is_dry_run_mode", True)
    
    @property
    def trading_mode(self) -> str:
        """交易模式描述"""
        return "dry_run" if self.is_dry_run_mode else "live"
    



# 创建全局配置实例
settings = Settings()


def initialize_settings(db: Session):
    """初始化配置"""
    settings.set_db_session(db)


def get_settings() -> Settings:
    """获取配置实例"""
    return settings 