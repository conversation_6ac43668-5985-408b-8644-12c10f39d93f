from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, Enum, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import enum


Base = declarative_base()


class SignalStatus(str, enum.Enum):
    """信号状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    EXECUTED = "executed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SignalType(str, enum.Enum):
    """信号类型枚举"""
    BUY = "buy"
    SELL = "sell"
    LONG = "long"
    SHORT = "short"
    CLOSE = "close"


class BacktestStatus(str, enum.Enum):
    """回测状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class SignalSource(Base):
    """信号源管理表"""
    __tablename__ = "signal_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), comment="信号源名称")
    telegram_chat_id = Column(String(50), unique=True, nullable=False, comment="Telegram聊天ID")
    description = Column(Text, comment="信号源描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    reliability_score = Column(Float, default=0.0, comment="可靠性评分")
    total_signals = Column(Integer, default=0, comment="总信号数")
    successful_signals = Column(Integer, default=0, comment="成功信号数")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    signals = relationship("TelegramSignal", back_populates="source")


class TelegramSignal(Base):
    """Telegram信号表"""
    __tablename__ = "telegram_signals"
    
    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("signal_sources.id"), nullable=False)
    message_id = Column(String(50), comment="Telegram消息ID")
    
    # 信号内容
    symbol = Column(String(20), nullable=False, comment="交易对符号")
    signal_type = Column(Enum(SignalType), nullable=False, comment="信号类型")
    entry_price = Column(Float, comment="入场价格")
    stop_loss = Column(Float, comment="止损价格")
    take_profit = Column(Float, comment="止盈价格")
    quantity = Column(Float, comment="交易数量")
    leverage = Column(Integer, default=1, comment="杠杆倍数")
    entry_points = Column(Text, comment="入场点JSON数据")
    
    # 元数据
    raw_message = Column(Text, comment="原始消息内容")
    confidence_score = Column(Float, default=0.5, comment="信号置信度分数")
    status = Column(Enum(SignalStatus), default=SignalStatus.PENDING, comment="处理状态")
    error_message = Column(Text, comment="错误信息")
    
    # 时间戳
    signal_time = Column(DateTime(timezone=True), nullable=False, comment="信号时间")
    received_at = Column(DateTime(timezone=True), server_default=func.now(), comment="接收时间")
    processed_at = Column(DateTime(timezone=True), comment="处理时间")
    
    # 关系
    source = relationship("SignalSource", back_populates="signals")
    trades = relationship("SignalTrade", back_populates="signal", cascade="all, delete-orphan")
    entry_points = relationship("TradeEntryPoint", back_populates="signal", cascade="all, delete-orphan")
    take_profit_points = relationship("TradeTakeProfitPoint", back_populates="signal", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_signal_symbol_time', 'symbol', 'signal_time'),
        Index('idx_signal_status', 'status'),
        Index('idx_signal_source_time', 'source_id', 'signal_time'),
    )


class SignalTrade(Base):
    """信号交易执行记录表"""
    __tablename__ = "signal_trades"
    
    id = Column(Integer, primary_key=True, index=True)
    signal_id = Column(Integer, ForeignKey("telegram_signals.id"), nullable=False)
    
    # 交易信息
    trade_id = Column(String(100), comment="交易ID")
    order_id = Column(String(100), comment="订单ID")
    symbol = Column(String(20), nullable=False)
    side = Column(String(10), nullable=False, comment="买卖方向")
    
    # 价格和数量
    entry_price = Column(Float, comment="实际入场价格")
    exit_price = Column(Float, comment="实际出场价格")
    quantity = Column(Float, comment="交易数量")
    
    # 盈亏
    pnl = Column(Float, comment="盈亏金额")
    pnl_percentage = Column(Float, comment="盈亏百分比")
    fee = Column(Float, comment="手续费")
    
    # 状态和时间
    status = Column(String(20), default="open", comment="交易状态")
    entry_time = Column(DateTime(timezone=True), comment="入场时间")
    exit_time = Column(DateTime(timezone=True), comment="出场时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    signal = relationship("TelegramSignal", back_populates="trades")


class BacktestResult(Base):
    """回测结果表"""
    __tablename__ = "backtest_results"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 回测配置
    name = Column(String(100), nullable=False, comment="回测名称")
    description = Column(Text, comment="回测描述")
    symbol = Column(String(20), nullable=False, comment="交易对")
    start_date = Column(DateTime(timezone=True), nullable=False, comment="开始日期")
    end_date = Column(DateTime(timezone=True), nullable=False, comment="结束日期")
    initial_balance = Column(Float, default=1000.0, comment="初始资金")
    
    # 信号源配置
    source_ids = Column(String(500), comment="使用的信号源ID列表")
    signal_filters = Column(Text, comment="信号过滤条件JSON")
    
    # 回测结果
    final_balance = Column(Float, comment="最终资金")
    total_return = Column(Float, comment="总收益率")
    total_trades = Column(Integer, comment="总交易次数")
    winning_trades = Column(Integer, comment="盈利交易次数")
    losing_trades = Column(Integer, comment="亏损交易次数")
    win_rate = Column(Float, comment="胜率")
    profit_factor = Column(Float, comment="盈利因子")
    max_drawdown = Column(Float, comment="最大回撤")
    sharpe_ratio = Column(Float, comment="夏普比率")
    
    # 状态和时间
    status = Column(Enum(BacktestStatus), default=BacktestStatus.PENDING)
    started_at = Column(DateTime(timezone=True), comment="开始时间")
    completed_at = Column(DateTime(timezone=True), comment="完成时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 结果详情
    result_json = Column(Text, comment="详细结果JSON")
    error_message = Column(Text, comment="错误信息")


class UserSession(Base):
    """用户会话表"""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_token = Column(String(500), unique=True, nullable=False)
    user_id = Column(String(100), nullable=False, comment="用户ID")
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    last_activity = Column(DateTime(timezone=True), server_default=func.now())


class SystemMetrics(Base):
    """系统监控指标表"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20), comment="指标单位")
    tags = Column(Text, comment="标签JSON")
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_metrics_name_time', 'metric_name', 'timestamp'),
    ) 


class ConfigCategory(str, enum.Enum):
    """配置分类枚举"""
    TELEGRAM = "telegram"
    FREQTRADE = "freqtrade"
    EXCHANGE = "exchange"
    LOGGING = "logging"
    TRADING = "trading"
    SYSTEM = "system"
    LLM = "llm"


class ConfigType(str, enum.Enum):
    """配置类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    JSON = "json"
    PASSWORD = "password"
    LIST = "list"


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False, comment="配置键")
    value = Column(Text, comment="配置值")
    default_value = Column(Text, comment="默认值")
    description = Column(Text, comment="配置描述")
    category = Column(Enum(ConfigCategory), nullable=False, comment="配置分类")
    config_type = Column(Enum(ConfigType), default=ConfigType.STRING, comment="配置类型")
    is_required = Column(Boolean, default=False, comment="是否必需")
    is_sensitive = Column(Boolean, default=False, comment="是否敏感信息")
    is_readonly = Column(Boolean, default=False, comment="是否只读")
    validation_rules = Column(Text, comment="验证规则JSON")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_config_category', 'category'),
        Index('idx_config_key', 'key'),
    )


class FreqtradeConfig(Base):
    """Freqtrade配置表"""
    __tablename__ = "freqtrade_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="配置名称")
    description = Column(Text, comment="配置描述")
    config_json = Column(Text, nullable=False, comment="完整配置JSON")
    is_active = Column(Boolean, default=False, comment="是否为当前活动配置")
    is_default = Column(Boolean, default=False, comment="是否为默认配置")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(100), comment="创建者")
    
    # 索引
    __table_args__ = (
        Index('idx_freqtrade_active', 'is_active'),
    )


class ConfigHistory(Base):
    """配置变更历史表"""
    __tablename__ = "config_history"
    
    id = Column(Integer, primary_key=True, index=True)
    config_key = Column(String(100), nullable=False, comment="配置键")
    old_value = Column(Text, comment="旧值")
    new_value = Column(Text, comment="新值")
    changed_by = Column(String(100), comment="变更者")
    change_reason = Column(Text, comment="变更原因")
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_config_history_key_time', 'config_key', 'timestamp'),
    ) 


class TradeEntryPoint(Base):
    """交易入场点表"""
    __tablename__ = "trade_entry_points"
    
    id = Column(Integer, primary_key=True, index=True)
    signal_id = Column(Integer, ForeignKey("telegram_signals.id"), nullable=False)
    
    # 入场点配置
    price = Column(Float, nullable=False, comment="入场价格")
    allocation = Column(Float, nullable=False, comment="资金分配比例")
    description = Column(String(200), comment="入场点描述")
    
    # 执行状态
    status = Column(String(20), default="pending", comment="执行状态: pending, partial, filled, cancelled")
    filled_quantity = Column(Float, default=0, comment="已成交数量")
    actual_price = Column(Float, comment="实际成交价格")
    
    # 订单信息
    order_id = Column(String(100), comment="交易所订单ID")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    filled_at = Column(DateTime(timezone=True), comment="成交时间")
    
    # 关系
    signal = relationship("TelegramSignal", back_populates="entry_points")


class TradeTakeProfitPoint(Base):
    """交易止盈点表"""
    __tablename__ = "trade_take_profit_points"
    
    id = Column(Integer, primary_key=True, index=True)
    signal_id = Column(Integer, ForeignKey("telegram_signals.id"), nullable=False)
    
    # 止盈点配置
    price = Column(Float, nullable=False, comment="止盈价格")
    allocation = Column(Float, nullable=False, comment="平仓比例")
    description = Column(String(200), comment="止盈点描述")
    
    # 执行状态
    status = Column(String(20), default="pending", comment="执行状态: pending, partial, filled, cancelled")
    filled_quantity = Column(Float, default=0, comment="已成交数量")
    actual_price = Column(Float, comment="实际成交价格")
    
    # 订单信息
    order_id = Column(String(100), comment="交易所订单ID")
    
    # 盈亏信息
    pnl = Column(Float, comment="盈亏金额")
    pnl_percentage = Column(Float, comment="盈亏百分比")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    filled_at = Column(DateTime(timezone=True), comment="成交时间")
    
    # 关系
    signal = relationship("TelegramSignal", back_populates="take_profit_points")