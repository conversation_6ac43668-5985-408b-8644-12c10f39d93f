import asyncio
import logging
import traceback
from typing import Optional
from datetime import datetime

from app.database import SessionLocal
from app.models import TelegramSignal, SignalStatus, BacktestResult, BacktestStatus
from app.services.signal_manager import signal_manager

logger = logging.getLogger(__name__)

# 安全的异常信息提取函数
def extract_safe_error_info(exc: Exception) -> dict:
    """提取可序列化的异常信息"""
    try:
        # 确保所有值都是基础类型，可以被Celery序列化
        error_info = {
            'exc_type': type(exc).__name__,  # 使用exc_type而不是error_type
            'error_type': type(exc).__name__,
            'error_message': str(exc)[:500] if exc else 'Unknown error',  # 限制错误消息长度
            'traceback': None  # 暂时不包含traceback，避免序列化问题
        }
        
        # 安全地获取traceback
        try:
            tb_str = traceback.format_exc()
            if tb_str and tb_str.strip() != 'NoneType: None':
                error_info['traceback'] = tb_str[:1000]  # 限制长度
        except Exception:
            pass
            
        return error_info
        
    except Exception:
        # 如果连基础信息提取都失败，返回最简单的错误信息
        return {
            'exc_type': 'UnknownError',
            'error_type': 'UnknownError', 
            'error_message': 'Failed to extract error information',
            'traceback': None
        }

# 如果Celery可用，使用Celery任务
try:
    from celery import Celery
    from app.celery_app import celery_app
    from celery import shared_task
    
    @celery_app.task(bind=True, max_retries=3, name='app.tasks.process_telegram_signal')
    def process_telegram_signal(self, signal_id: int):
        """处理Telegram信号的Celery任务"""
        try:
            # 在Celery任务中运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    signal_manager.manual_execute_signal(signal_id)
                )
                
                if result['success']:
                    logger.info(f"Celery任务成功处理信号: {signal_id}")
                    return {'success': True, 'signal_id': signal_id, 'message': result['message']}
                else:
                    logger.warning(f"Celery任务处理信号失败: {signal_id} - {result['message']}")
                    return {'success': False, 'signal_id': signal_id, 'message': result['message']}
                    
            finally:
                loop.close()
                
        except Exception as exc:
            error_info = extract_safe_error_info(exc)
            logger.error(f"Celery任务异常: {signal_id} - {error_info}")
            
            # 重试机制
            if self.request.retries < self.max_retries:
                logger.info(f"重试处理信号: {signal_id} (第{self.request.retries + 1}次)")
                # 使用安全的异常重试，直接抛出原始异常让Celery处理
                raise self.retry(countdown=60 * (self.request.retries + 1), exc=exc)
            else:
                # 标记信号为失败状态
                try:
                    db = SessionLocal()
                    try:
                        signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
                        if signal:
                            signal.status = SignalStatus.FAILED
                            signal.error_message = error_info.get('error_message', 'Processing failed')[:500]
                            db.commit()
                    finally:
                        db.close()
                except Exception as db_error:
                    logger.error(f"更新信号状态失败: {db_error}")
                
                # 返回简化的错误信息，确保包含 exc_type
                return {
                    'success': False, 
                    'signal_id': signal_id, 
                    'error': {
                        'exc_type': error_info.get('exc_type', 'UnknownError'),
                        'error_type': error_info.get('error_type', 'UnknownError'),
                        'error_message': error_info.get('error_message', 'Signal processing failed')[:200]
                    }
                }

    @celery_app.task(bind=True, max_retries=1, name='app.tasks.run_backtest_celery_task', soft_time_limit=1800, time_limit=2100)
    def run_backtest_celery_task(self, backtest_id: int):
        """运行回测的Celery任务 - 增强版错误处理"""
        from app.services.vectorbt_backtest_engine import VectorbtBacktestEngine
        from app.database import SessionLocal
        from app.models import BacktestResult, BacktestStatus
        from datetime import datetime
        import logging
        
        logger = logging.getLogger(__name__)
        
        def send_progress_notification(data):
            """发送进度通知（安全版本）"""
            try:
                from app.api.websocket import notify_backtest_progress
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(notify_backtest_progress(backtest_id, data))
                loop.close()
            except Exception as e:
                logger.warning(f"发送进度通知失败: {e}")
        
        # 安全的任务执行
        db = SessionLocal()
        try:
            logger.info(f"开始执行回测任务: {backtest_id}")
            
            # 获取回测记录
            backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
            if not backtest:
                logger.error(f"回测记录不存在: {backtest_id}")
                return {
                    'success': False, 
                    'error': {
                        'exc_type': 'NotFound',
                        'error_type': 'NotFound', 
                        'error_message': f'回测记录 {backtest_id} 不存在'
                    }
                }
            
            # 更新状态为运行中
            backtest.status = BacktestStatus.RUNNING
            backtest.started_at = datetime.utcnow()
            db.commit()
            
            # 创建VectorBT回测引擎
            engine = VectorbtBacktestEngine(db)
            
            # 运行异步回测
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 发送开始通知
                progress_data = {'progress': 10, 'stage': 'starting', 'message': '开始执行回测...'}
                send_progress_notification(progress_data)
                
                # 使用公共方法运行回测，包含完整的错误处理
                results = loop.run_until_complete(engine.run_backtest(backtest))
                
                # 完成通知
                progress_data = {
                    'progress': 100, 
                    'stage': 'completed',
                    'message': '回测完成！'
                }
                send_progress_notification(progress_data)
                
                # 发送完成通知
                try:
                    from app.api.websocket import notify_backtest_completed
                    loop.run_until_complete(notify_backtest_completed(backtest_id, results))
                except Exception as e:
                    logger.warning(f"发送完成通知失败: {e}")
                
                logger.info(f"回测 {backtest_id} 成功完成")
                
                # 返回安全的结果
                return {
                    'success': True, 
                    'backtest_id': backtest_id, 
                    'message': '回测完成',
                    'result_summary': {
                        'total_trades': results.get('total_trades', 0),
                        'total_return': results.get('total_return', 0),
                        'win_rate': results.get('win_rate', 0)
                    }
                }
                
            except Exception as e:
                logger.error(f"回测执行异常: {e}")
                
                # 发送失败通知
                try:
                    from app.api.websocket import notify_backtest_failed
                    loop.run_until_complete(notify_backtest_failed(backtest_id, str(e)))
                except Exception as notify_error:
                    logger.warning(f"发送失败通知失败: {notify_error}")
                
                # 返回安全的错误结果，确保包含 exc_type
                return {
                    'success': False, 
                    'error': {
                        'exc_type': type(e).__name__,
                        'error_type': type(e).__name__,
                        'error_message': str(e)[:500]  # 限制错误信息长度
                    }
                }
            finally:
                try:
                    loop.close()
                except:
                    pass
        
        except Exception as e:
            logger.error(f"回测任务异常: {e}")
            return {
                'success': False, 
                'error': {
                    'exc_type': type(e).__name__,
                    'error_type': type(e).__name__,
                    'error_message': str(e)[:500]
                }
            }
        finally:
            try:
                db.close()
            except:
                pass

    # 添加缺失的定时任务
    @celery_app.task(name='app.tasks.sync_active_trades_to_cache')
    def sync_active_trades_to_cache():
        """同步活跃交易到缓存"""
        try:
            logger.info("同步活跃交易到缓存任务执行")
            # 实现同步逻辑
            return {"status": "success", "message": "Sync completed"}
        except Exception as e:
            error_info = extract_safe_error_info(e)
            logger.error(f"同步活跃交易失败: {error_info}")
            return {"status": "error", "error": error_info}

    @celery_app.task(name='app.tasks.monitor_trade_prices')
    def monitor_trade_prices():
        """监控交易价格"""
        try:
            logger.info("监控交易价格任务执行")
            # 实现价格监控逻辑
            return {"status": "success", "message": "Price monitoring completed"}
        except Exception as e:
            error_info = extract_safe_error_info(e)
            logger.error(f"监控交易价格失败: {error_info}")
            return {"status": "error", "error": error_info}

    @celery_app.task(name='app.tasks.cleanup_old_signals')
    def cleanup_old_signals():
        """清理旧信号"""
        try:
            logger.info("清理旧信号任务执行")
            # 实现清理逻辑
            return {"status": "success", "message": "Cleanup completed"}
        except Exception as e:
            error_info = extract_safe_error_info(e)
            logger.error(f"清理旧信号失败: {error_info}")
            return {"status": "error", "error": error_info}

    @celery_app.task(name='app.tasks.update_signal_statistics')
    def update_signal_statistics():
        """更新信号统计"""
        try:
            logger.info("更新信号统计任务执行")
            # 实现统计更新逻辑
            return {"status": "success", "message": "Statistics updated"}
        except Exception as e:
            error_info = extract_safe_error_info(e)
            logger.error(f"更新信号统计失败: {error_info}")
            return {"status": "error", "error": error_info}

    CELERY_AVAILABLE = True
    logger.info("Celery任务已注册")

except ImportError:
    # 如果Celery不可用，使用简单的异步任务
    logger.warning("Celery不可用，使用简单的异步任务处理")
    CELERY_AVAILABLE = False
    
    async def process_telegram_signal(signal_id: int):
        """处理Telegram信号的简单异步任务"""
        try:
            result = await signal_manager.manual_execute_signal(signal_id)
            
            if result['success']:
                logger.info(f"异步任务成功处理信号: {signal_id}")
            else:
                logger.warning(f"异步任务处理信号失败: {signal_id} - {result['message']}")
                
            return result
            
        except Exception as exc:
            error_info = extract_safe_error_info(exc)
            logger.error(f"异步任务异常: {signal_id} - {error_info}")
            
            # 标记信号为失败状态
            try:
                db = SessionLocal()
                try:
                    signal = db.query(TelegramSignal).filter(TelegramSignal.id == signal_id).first()
                    if signal:
                        signal.status = SignalStatus.FAILED
                        db.commit()
                finally:
                    db.close()
            except Exception as db_error:
                logger.error(f"更新信号状态失败: {db_error}")
            
            return {'success': False, 'signal_id': signal_id, 'error': error_info}

    async def run_backtest_celery_task(backtest_id: int):
        """运行回测的简单异步任务"""
        from app.services.vectorbt_backtest_engine import VectorbtBacktestEngine
        
        db = SessionLocal()
        try:
            # 获取回测对象
            backtest = db.query(BacktestResult).filter(BacktestResult.id == backtest_id).first()
            if not backtest:
                raise Exception(f"回测记录 {backtest_id} 不存在")

            engine = VectorbtBacktestEngine(db)
            await engine.run_backtest(backtest)
            return {'success': True, 'backtest_id': backtest_id}
        except Exception as e:
            error_info = extract_safe_error_info(e)
            logger.error(f"回测任务执行失败: {error_info}")
            return {'success': False, 'backtest_id': backtest_id, 'error': error_info}
        finally:
            db.close()


# 信号处理任务调度器
class SignalTaskScheduler:
    """信号和回测任务调度器"""
    
    def __init__(self):
        self.running_tasks = set()
        self.running_backtests = set()
        
    async def schedule_signal_processing(self, signal_id: int):
        """调度信号处理任务"""
        if signal_id in self.running_tasks:
            logger.debug(f"信号 {signal_id} 已在处理队列中")
            return
            
        self.running_tasks.add(signal_id)
        
        try:
            # 尝试使用Celery
            if CELERY_AVAILABLE:
                # 异步提交Celery任务
                process_telegram_signal.delay(signal_id)
                logger.info(f"信号 {signal_id} 已提交到Celery队列")
            else:
                # 使用简单的异步任务
                asyncio.create_task(self._process_signal_async(signal_id))
                logger.info(f"信号 {signal_id} 已提交到异步队列")
                
        except Exception as e:
            logger.error(f"调度信号处理任务失败: {signal_id} - {e}")
        finally:
            # 任务完成后从运行集合中移除
            asyncio.create_task(self._cleanup_task(signal_id))

    async def schedule_backtest_processing(self, backtest_id: int):
        """调度回测处理任务"""
        if backtest_id in self.running_backtests:
            logger.debug(f"回测 {backtest_id} 已在处理队列中")
            return
            
        self.running_backtests.add(backtest_id)
        
        try:
            # 使用简单的异步任务（如果Celery不可用）
            asyncio.create_task(self._process_backtest_async(backtest_id))
            logger.info(f"回测 {backtest_id} 已提交到异步队列")
                
        except Exception as e:
            logger.error(f"调度回测处理任务失败: {backtest_id} - {e}")
        finally:
            # 任务完成后从运行集合中移除
            asyncio.create_task(self._cleanup_backtest_task(backtest_id))
    
    async def _process_signal_async(self, signal_id: int):
        """异步处理信号"""
        try:
            await process_telegram_signal(signal_id)
        except Exception as e:
            logger.error(f"异步处理信号失败: {signal_id} - {e}")
    
    async def _process_backtest_async(self, backtest_id: int):
        """异步处理回测"""
        try:
            await run_backtest_celery_task(backtest_id)
        except Exception as e:
            logger.error(f"异步处理回测失败: {backtest_id} - {e}")
    
    async def _cleanup_task(self, signal_id: int):
        """清理信号任务"""
        # 延迟一段时间后清理，避免重复提交
        await asyncio.sleep(5)
        self.running_tasks.discard(signal_id)

    async def _cleanup_backtest_task(self, backtest_id: int):
        """清理回测任务"""
        # 延迟一段时间后清理，避免重复提交
        await asyncio.sleep(10)
        self.running_backtests.discard(backtest_id)


# 全局任务调度器实例
task_scheduler = SignalTaskScheduler()


# 批量处理信号任务
async def batch_process_signals(signal_ids: list[int]) -> dict:
    """批量处理信号"""
    results = []
    
    for signal_id in signal_ids:
        try:
            result = await signal_manager.manual_execute_signal(signal_id)
            results.append({
                'signal_id': signal_id,
                'success': result['success'],
                'message': result['message']
            })
        except Exception as e:
            error_info = extract_safe_error_info(e)
            logger.error(f"批量处理信号失败: {signal_id} - {error_info}")
            results.append({
                'signal_id': signal_id,
                'success': False,
                'error': error_info
            })
    
    success_count = sum(1 for r in results if r['success'])
    
    return {
        'success': True,
        'results': results,
        'success_count': success_count,
        'total_count': len(signal_ids),
        'message': f"批量处理完成，成功: {success_count}/{len(signal_ids)}"
    }