from celery import Celery
from app.config import settings
import logging
import traceback
import json

logger = logging.getLogger(__name__)


def safe_json_serializer(obj):
    """安全的JSON序列化器，处理异常对象"""
    try:
        if isinstance(obj, Exception):
            return {
                'exc_type': type(obj).__name__,
                'error_type': type(obj).__name__,
                'error_message': str(obj)[:500],
                'traceback': None
            }
        return json.dumps(obj, default=str)
    except Exception:
        return json.dumps({'error': 'Serialization failed', 'type': 'SerializationError'})


# 创建Celery应用实例
celery_app = Celery(
    "husky",  # 改为项目名称
    broker=settings.redis_url,
    backend=settings.redis_url,
    include=["app.tasks"]  # 确保包含任务模块
)

# Celery配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    
    # 修复连接重试警告
    broker_connection_retry_on_startup=True,
    
    # 异常处理配置 - 增强序列化修复
    task_always_eager=False,
    task_store_eager_result=True,
    result_persistent=True,
    
    # 强制使用简单的异常序列化
    task_reject_on_worker_lost=True,
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # 禁用复杂的traceback序列化
    task_track_started=True,
    task_send_sent_event=True,
    result_ignore_backend_errors=True,  # 忽略后端序列化错误
    
    # 简化结果存储
    result_expires=3600,
    result_compression=None,  # 禁用压缩避免序列化问题
    task_compression=None,    # 禁用压缩避免序列化问题
    
    # 任务路由配置
    task_routes={
        "app.tasks.process_telegram_signal": {"queue": "signal_processing"},
        "app.tasks.run_backtest_celery_task": {"queue": "backtest"},
        "app.tasks.execute_freqtrade_backtest": {"queue": "backtest"},
        "app.tasks.monitor_freqtrade_trades": {"queue": "monitoring"},
    },
    
    # 任务重试配置
    task_default_retry_delay=60,
    task_max_retries=3,
    
    # 任务执行时间限制
    task_time_limit=300,
    task_soft_time_limit=240,
    
    # Worker配置
    worker_max_tasks_per_child=1000,
    
    # 心跳检测
    worker_send_task_events=True,
    
    # Beat调度配置
    beat_schedule={
        "sync-active-trades-to-cache": {
            "task": "app.tasks.sync_active_trades_to_cache",
            "schedule": 300.0,  # 每5分钟同步活跃交易到缓存
        },
        "monitor-trade-prices": {
            "task": "app.tasks.monitor_trade_prices",
            "schedule": 10.0,  # 每10秒监控价格和交易状态
        },
        "cleanup-old-signals": {
            "task": "app.tasks.cleanup_old_signals",
            "schedule": 3600.0,  # 每小时执行一次
        },
        "update-signal-statistics": {
            "task": "app.tasks.update_signal_statistics",
            "schedule": 600.0,  # 每10分钟执行一次
        },
    },
)

# 自定义异常序列化处理
def safe_exception_handler(task_id, err, traceback_str, einfo):
    """安全的异常处理器，避免序列化问题"""
    try:
        error_info = {
            'task_id': task_id,
            'error_type': type(err).__name__,
            'error_message': str(err),
            'traceback': traceback_str[:1000] if traceback_str else None  # 限制长度
        }
        logger.error(f"Celery任务失败: {error_info}")
        return error_info
    except Exception as e:
        logger.error(f"异常处理器本身失败: {e}")
        return {
            'task_id': task_id,
            'error_type': 'SerializationError',
            'error_message': 'Failed to serialize original exception',
            'traceback': None
        }

def create_safe_result(success=False, data=None, error=None):
    """创建安全的、可序列化的结果"""
    try:
        result = {
            'success': success,
            'data': data,
            'error': error
        }
        # 测试序列化
        import json
        json.dumps(result)
        return result
    except (TypeError, ValueError) as e:
        logger.warning(f"结果序列化失败，使用简化版本: {e}")
        return {
            'success': success,
            'data': str(data) if data else None,
            'error': str(error) if error else None
        }

# 安全任务装饰器
def safe_task(**kwargs):
    """安全的任务装饰器，确保结果可序列化"""
    def decorator(func):
        @celery_app.task(bind=True, **kwargs)
        def wrapper(self, *args, **kwargs):
            try:
                result = func(self, *args, **kwargs)
                return create_safe_result(success=True, data=result)
            except Exception as e:
                logger.error(f"任务 {func.__name__} 执行失败: {e}")
                error_info = {
                    'error_type': type(e).__name__,
                    'error_message': str(e)[:500]  # 限制长度
                }
                return create_safe_result(success=False, error=error_info)
        return wrapper
    return decorator

# 设置异常处理器
celery_app.conf.task_annotations = {
    '*': {
        'on_failure': safe_exception_handler
    }
}

# 自动发现任务
celery_app.autodiscover_tasks(['app'])


@celery_app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')
    return f'Debug task executed: {self.request.id}'


# 确保在导入时注册所有任务
def register_tasks():
    """手动注册任务以确保它们被发现"""
    try:
        # 导入任务模块以确保任务被注册
        from app import tasks
        logger.info("Tasks module imported successfully")
    except Exception as e:
        logger.error(f"Failed to import tasks module: {e}")


# 初始化时注册任务
register_tasks() 