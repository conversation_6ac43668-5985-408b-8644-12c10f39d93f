-- 初始化 Husky 数据库
-- 添加 TimescaleDB 扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- 创建K线数据表
CREATE TABLE IF NOT EXISTS kline_data (
    id SERIAL,
    symbol VARCHAR(20) NOT NULL,
    interval_type VARCHAR(10) NOT NULL,  -- 1m, 5m, 15m, 1h, 4h, 1d等
    open_time TIMESTAMPTZ NOT NULL,
    close_time TIMESTAMPTZ NOT NULL,
    open_price DECIMAL(20, 8) NOT NULL,
    high_price DECIMAL(20, 8) NOT NULL,
    low_price DECIMAL(20, 8) NOT NULL,
    close_price DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(20, 8) NOT NULL,
    quote_asset_volume DECIMAL(20, 8),
    number_of_trades INTEGER,
    taker_buy_base_asset_volume DECIMAL(20, 8),
    taker_buy_quote_asset_volume DECIMAL(20, 8),
    exchange VARCHAR(20) DEFAULT 'binance',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 创建复合主键
    PRIMARY KEY (symbol, interval_type, open_time)
);

-- 创建时序数据表 (TimescaleDB)
SELECT create_hypertable('kline_data', 'open_time', 
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_kline_symbol_time ON kline_data (symbol, open_time DESC);
CREATE INDEX IF NOT EXISTS idx_kline_symbol_interval ON kline_data (symbol, interval_type, open_time DESC);
CREATE INDEX IF NOT EXISTS idx_kline_exchange ON kline_data (exchange);

-- 设置数据压缩策略 (7天后压缩数据)
SELECT add_compression_policy('kline_data', INTERVAL '7 days', if_not_exists => TRUE);

-- 设置数据保留策略 (保留2年数据)
SELECT add_retention_policy('kline_data', INTERVAL '2 years', if_not_exists => TRUE);

-- 创建连续聚合视图用于快速查询
CREATE MATERIALIZED VIEW IF NOT EXISTS kline_daily
WITH (timescaledb.continuous) AS
SELECT 
    symbol,
    exchange,
    time_bucket('1 day', open_time) AS day,
    first(open_price, open_time) AS open_price,
    max(high_price) AS high_price,
    min(low_price) AS low_price,
    last(close_price, open_time) AS close_price,
    sum(volume) AS volume,
    count(*) AS candle_count
FROM kline_data
WHERE interval_type = '5m'  -- 基于5分钟数据聚合
GROUP BY symbol, exchange, day;

-- 为连续聚合视图添加刷新策略
SELECT add_continuous_aggregate_policy('kline_daily',
    start_offset => INTERVAL '3 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- 创建K线数据完整性检查函数
CREATE OR REPLACE FUNCTION check_kline_data_completeness(
    p_symbol VARCHAR(20),
    p_interval VARCHAR(10),
    p_start_time TIMESTAMPTZ,
    p_end_time TIMESTAMPTZ
) RETURNS TABLE (
    missing_periods TIMESTAMPTZ[]
) AS $$
DECLARE
    expected_interval INTERVAL;
    current_time TIMESTAMPTZ;
    missing_times TIMESTAMPTZ[] := '{}';
BEGIN
    -- 根据interval_type确定时间间隔
    CASE p_interval
        WHEN '1m' THEN expected_interval := INTERVAL '1 minute';
        WHEN '5m' THEN expected_interval := INTERVAL '5 minutes';
        WHEN '15m' THEN expected_interval := INTERVAL '15 minutes';
        WHEN '1h' THEN expected_interval := INTERVAL '1 hour';
        WHEN '4h' THEN expected_interval := INTERVAL '4 hours';
        WHEN '1d' THEN expected_interval := INTERVAL '1 day';
        ELSE expected_interval := INTERVAL '5 minutes';  -- 默认改为5分钟
    END CASE;
    
    -- 检查时间范围内的缺失数据
    current_time := p_start_time;
    WHILE current_time <= p_end_time LOOP
        IF NOT EXISTS (
            SELECT 1 FROM kline_data 
            WHERE symbol = p_symbol 
            AND interval_type = p_interval 
            AND open_time = current_time
        ) THEN
            missing_times := array_append(missing_times, current_time);
        END IF;
        current_time := current_time + expected_interval;
    END LOOP;
    
    RETURN QUERY SELECT missing_times;
END;
$$ LANGUAGE plpgsql;

-- 创建批量插入K线数据的函数
CREATE OR REPLACE FUNCTION insert_kline_batch(
    kline_records JSONB
) RETURNS INTEGER AS $$
DECLARE
    insert_count INTEGER := 0;
    kline_record JSONB;
BEGIN
    FOR kline_record IN SELECT * FROM jsonb_array_elements(kline_records)
    LOOP
        INSERT INTO kline_data (
            symbol, interval_type, open_time, close_time,
            open_price, high_price, low_price, close_price,
            volume, quote_asset_volume, number_of_trades,
            taker_buy_base_asset_volume, taker_buy_quote_asset_volume,
            exchange
        ) VALUES (
            kline_record->>'symbol',
            kline_record->>'interval_type',
            (kline_record->>'open_time')::TIMESTAMPTZ,
            (kline_record->>'close_time')::TIMESTAMPTZ,
            (kline_record->>'open_price')::DECIMAL,
            (kline_record->>'high_price')::DECIMAL,
            (kline_record->>'low_price')::DECIMAL,
            (kline_record->>'close_price')::DECIMAL,
            (kline_record->>'volume')::DECIMAL,
            (kline_record->>'quote_asset_volume')::DECIMAL,
            (kline_record->>'number_of_trades')::INTEGER,
            (kline_record->>'taker_buy_base_asset_volume')::DECIMAL,
            (kline_record->>'taker_buy_quote_asset_volume')::DECIMAL,
            COALESCE(kline_record->>'exchange', 'binance')
        ) ON CONFLICT (symbol, interval_type, open_time) DO UPDATE SET
            close_time = EXCLUDED.close_time,
            open_price = EXCLUDED.open_price,
            high_price = EXCLUDED.high_price,
            low_price = EXCLUDED.low_price,
            close_price = EXCLUDED.close_price,
            volume = EXCLUDED.volume,
            quote_asset_volume = EXCLUDED.quote_asset_volume,
            number_of_trades = EXCLUDED.number_of_trades,
            taker_buy_base_asset_volume = EXCLUDED.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume = EXCLUDED.taker_buy_quote_asset_volume;
        
        insert_count := insert_count + 1;
    END LOOP;
    
    RETURN insert_count;
END;
$$ LANGUAGE plpgsql;

-- 创建获取最新K线时间的函数
CREATE OR REPLACE FUNCTION get_latest_kline_time(
    p_symbol VARCHAR(20),
    p_interval VARCHAR(10)
) RETURNS TIMESTAMPTZ AS $$
BEGIN
    RETURN (
        SELECT MAX(open_time) 
        FROM kline_data 
        WHERE symbol = p_symbol 
        AND interval_type = p_interval
    );
END;
$$ LANGUAGE plpgsql;

-- 授权给应用用户（如果有的话）
-- GRANT ALL PRIVILEGES ON kline_data TO husky_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO husky_user; 