import logging
import asyncio
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON>NResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.config import settings, initialize_settings
from app.database import init_database, get_db
from app.api import api_router
from app.api.config import router as config_router
from app.api.test import router as test_router
from app.services.telegram_collector import telegram_collector
from app.services.trading_monitor import trading_monitor
import app.tasks  # 导入任务模块

# 配置日志
def setup_logging():
    """设置日志配置"""
    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 创建处理器列表
    handlers = [logging.StreamHandler()]  # 控制台输出
    
    # 如果有日志文件配置，添加文件处理器
    try:
        if hasattr(settings, 'log_file') and settings.log_file:
            handlers.append(logging.FileHandler(settings.log_file))
    except Exception as e:
        print(f"Warning: Failed to create log file handler: {e}")
    
    # 配置日志
    logging.basicConfig(
        level=getattr(logging, getattr(settings, 'log_level', 'INFO').upper()),
        format=log_format,
        handlers=handlers,
        force=True  # 强制重新配置
    )

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Starting Telegram Trading System...")
    
    try:
        # 初始化数据库
        init_database()
        
        # 初始化配置（需要数据库连接）
        db = next(get_db())
        try:
            # 初始化默认配置
            from app.services.config_manager import config_manager
            config_manager.initialize_default_configs(db)
            
            # 设置数据库会话
            initialize_settings(db)
            logger.info("Configuration initialized")
        finally:
            db.close()
        
        # 重新设置日志（使用动态配置）
        setup_logging()
        
        # 初始化Telegram收集器
        if settings.telegram_api_id and settings.telegram_api_hash:
            try:
                # 初始化收集器（包含自动登录检查）
                await telegram_collector.initialize()
                
                # 如果自动登录成功，启动消息监听
                if telegram_collector.is_authenticated:
                    asyncio.create_task(telegram_collector.start_monitoring())
                    logger.info("Telegram收集器已启动（自动登录成功）")
                else:
                    logger.info("Telegram收集器已初始化，等待用户登录")
                    
            except Exception as e:
                logger.error(f"Telegram收集器初始化失败: {e}")
        else:
            logger.warning("Telegram API ID或API Hash未配置，收集器未初始化")
        
        # 启动交易监控系统
        if settings.auto_trading_enabled:
            try:
                asyncio.create_task(trading_monitor.start_all_monitors())
                logger.info("🚀 交易监控系统已启动")
            except Exception as e:
                logger.error(f"交易监控系统启动失败: {e}")
        else:
            logger.info("自动交易未启用，交易监控系统未启动")
        
        logger.info("Application startup completed")
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("Shutting down Telegram Trading System...")
    
    try:
        # 停止交易监控系统
        trading_monitor.stop_all_monitors()
        logger.info("交易监控系统已停止")
        
        # 停止Telegram收集器
        await telegram_collector.stop()
        logger.info("Telegram collector stopped")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
    
    logger.info("Application shutdown completed")


# 初始化日志
setup_logging()

# 创建FastAPI应用
app = FastAPI(
    title="Telegram信号交易系统",
    description="基于Telegram群组信号的自动化交易系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加可信主机中间件
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware, 
        allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"]
    )

# 包含API路由
app.include_router(api_router, prefix="/api/v1")
app.include_router(config_router, prefix="/api/v1")
app.include_router(test_router, prefix="/api/v1")


# 全局异常处理器
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "error": "输入数据验证失败",
            "detail": exc.errors()
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "服务器内部错误",
            "detail": str(exc) if settings.debug else "请联系系统管理员"
        }
    )


# 中间件：请求日志
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = asyncio.get_event_loop().time()
    
    response = await call_next(request)
    
    process_time = asyncio.get_event_loop().time() - start_time
    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )
    
    return response


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "telegram-trading-system",
        "version": "1.0.0",
        "environment": settings.environment
    }


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Telegram信号交易系统 API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )