#!/usr/bin/env python3
"""
VectorBT集成演示脚本
展示完整的VectorBT回测功能，包括控制台输出和图表生成
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_demo_data():
    """创建演示数据"""
    logger.info("创建演示数据...")
    
    # 创建30天的小时级价格数据
    dates = pd.date_range('2025-01-01', '2025-01-30', freq='1h')
    np.random.seed(42)
    
    # 生成更真实的价格数据
    btc_prices = []
    eth_prices = []
    
    btc_price = 45000
    eth_price = 3000
    
    for i in range(len(dates)):
        # 添加一些趋势和波动
        btc_change = np.random.normal(0, 0.002)  # 0.2%的标准波动
        eth_change = np.random.normal(0, 0.003)  # 0.3%的标准波动
        
        # 添加一些相关性
        if np.random.random() < 0.3:  # 30%的时间同向波动
            if btc_change > 0:
                eth_change = abs(eth_change)
            else:
                eth_change = -abs(eth_change)
        
        btc_price *= (1 + btc_change)
        eth_price *= (1 + eth_change)
        
        btc_prices.append(btc_price)
        eth_prices.append(eth_price)
    
    price_data = pd.DataFrame({
        'BTCUSDT': btc_prices,
        'ETHUSDT': eth_prices
    }, index=dates)
    
    logger.info(f"价格数据创建完成: {price_data.shape}")
    logger.info(f"BTC价格范围: ${price_data['BTCUSDT'].min():.0f} - ${price_data['BTCUSDT'].max():.0f}")
    logger.info(f"ETH价格范围: ${price_data['ETHUSDT'].min():.0f} - ${price_data['ETHUSDT'].max():.0f}")
    
    return price_data


def create_trading_signals(price_data):
    """创建交易信号"""
    logger.info("创建交易信号...")
    
    # 初始化信号矩阵
    entries = pd.DataFrame(False, index=price_data.index, columns=price_data.columns)
    exits = pd.DataFrame(False, index=price_data.index, columns=price_data.columns)
    
    # 简单的移动平均策略
    for symbol in price_data.columns:
        prices = price_data[symbol]
        
        # 计算短期和长期移动平均
        ma_short = prices.rolling(window=24).mean()  # 24小时移动平均
        ma_long = prices.rolling(window=72).mean()   # 72小时移动平均
        
        # 生成信号
        for i in range(72, len(prices)):
            # 金叉买入信号
            if (ma_short.iloc[i] > ma_long.iloc[i] and 
                ma_short.iloc[i-1] <= ma_long.iloc[i-1]):
                entries.iloc[i, entries.columns.get_loc(symbol)] = True
            
            # 死叉卖出信号
            elif (ma_short.iloc[i] < ma_long.iloc[i] and 
                  ma_short.iloc[i-1] >= ma_long.iloc[i-1]):
                exits.iloc[i, exits.columns.get_loc(symbol)] = True
    
    logger.info(f"BTC信号数量 - 买入: {entries['BTCUSDT'].sum()}, 卖出: {exits['BTCUSDT'].sum()}")
    logger.info(f"ETH信号数量 - 买入: {entries['ETHUSDT'].sum()}, 卖出: {exits['ETHUSDT'].sum()}")
    
    return entries, exits


def run_vectorbt_backtest(price_data, entries, exits):
    """运行VectorBT回测"""
    logger.info("运行VectorBT回测...")
    
    import vectorbt as vbt
    
    # 配置回测参数
    init_cash = 10000
    fees = 0.001  # 0.1%手续费
    
    # 运行回测
    portfolio = vbt.Portfolio.from_signals(
        close=price_data,
        entries=entries,
        exits=exits,
        init_cash=init_cash,
        fees=fees,
        freq='1h'
    )
    
    logger.info("回测完成，正在计算统计指标...")
    
    # 计算统计指标
    stats = {}
    
    # 基础统计
    stats['initial_balance'] = init_cash
    stats['final_balance'] = portfolio.value().iloc[-1].sum()
    stats['total_return'] = portfolio.total_return().sum()
    stats['total_trades'] = portfolio.trades.count().sum()
    
    # 风险指标 - 确保返回标量值
    sharpe_ratio = portfolio.sharpe_ratio()
    max_drawdown = portfolio.max_drawdown()
    volatility = portfolio.returns().std() * np.sqrt(365 * 24)  # 年化波动率

    stats['sharpe_ratio'] = sharpe_ratio.mean() if hasattr(sharpe_ratio, 'mean') else float(sharpe_ratio)
    stats['max_drawdown'] = max_drawdown.max() if hasattr(max_drawdown, 'max') else float(max_drawdown)
    stats['volatility'] = volatility.mean() if hasattr(volatility, 'mean') else float(volatility)
    
    # 交易统计
    trades = portfolio.trades
    if stats['total_trades'] > 0:
        stats['winning_trades'] = trades.winning.count().sum()
        stats['losing_trades'] = trades.losing.count().sum()
        stats['win_rate'] = stats['winning_trades'] / stats['total_trades']
        
        # 盈亏统计
        gross_profit = trades.winning.pnl.sum().sum()
        gross_loss = abs(trades.losing.pnl.sum().sum())
        stats['gross_profit'] = gross_profit
        stats['gross_loss'] = gross_loss
        stats['profit_factor'] = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # 平均交易
        stats['avg_trade'] = trades.pnl.mean().mean()
        stats['avg_win'] = trades.winning.pnl.mean().mean() if stats['winning_trades'] > 0 else 0
        stats['avg_loss'] = trades.losing.pnl.mean().mean() if stats['losing_trades'] > 0 else 0
    else:
        stats.update({
            'winning_trades': 0, 'losing_trades': 0, 'win_rate': 0,
            'gross_profit': 0, 'gross_loss': 0, 'profit_factor': 0,
            'avg_trade': 0, 'avg_win': 0, 'avg_loss': 0
        })
    
    return portfolio, stats


def display_results(stats, portfolio):
    """显示回测结果"""
    print("\n" + "="*100)
    print("🚀 VECTORBT 回测结果报告")
    print("="*100)
    
    # 基础统计
    print(f"\n📊 基础统计:")
    print(f"   初始资金: ${stats['initial_balance']:,.2f}")
    print(f"   最终资金: ${stats['final_balance']:,.2f}")
    print(f"   净盈亏: ${stats['final_balance'] - stats['initial_balance']:,.2f}")
    print(f"   总收益率: {stats['total_return']:.2%}")
    
    # 交易统计
    print(f"\n📈 交易统计:")
    print(f"   总交易数: {stats['total_trades']:.0f}")
    print(f"   盈利交易: {stats['winning_trades']:.0f}")
    print(f"   亏损交易: {stats['losing_trades']:.0f}")
    print(f"   胜率: {stats['win_rate']:.2%}")
    print(f"   盈亏比: {stats['profit_factor']:.2f}")
    
    # 风险指标
    print(f"\n⚠️ 风险指标:")
    print(f"   最大回撤: {stats['max_drawdown']:.2%}")
    print(f"   夏普比率: {stats['sharpe_ratio']:.2f}")
    print(f"   年化波动率: {stats['volatility']:.2%}")
    
    # 盈亏分析
    print(f"\n💰 盈亏分析:")
    print(f"   总盈利: ${stats['gross_profit']:,.2f}")
    print(f"   总亏损: ${stats['gross_loss']:,.2f}")
    print(f"   平均每笔交易: ${stats['avg_trade']:,.2f}")
    print(f"   平均盈利交易: ${stats['avg_win']:,.2f}")
    print(f"   平均亏损交易: ${stats['avg_loss']:,.2f}")
    
    # 按资产分析
    print(f"\n🏷️ 按资产分析:")
    try:
        # 获取每个资产的统计
        values = portfolio.value()
        returns = portfolio.total_return()
        trade_counts = portfolio.trades.count()

        if hasattr(values, 'columns'):
            for i, symbol in enumerate(values.columns):
                final_value = values.iloc[-1, i] if len(values.shape) > 1 else values.iloc[-1]
                total_return = returns.iloc[i] if hasattr(returns, 'iloc') else returns
                trade_count = trade_counts.iloc[i] if hasattr(trade_counts, 'iloc') else trade_counts

                print(f"   {symbol}:")
                print(f"     最终价值: ${final_value:,.2f}")
                print(f"     收益率: {total_return:.2%}")
                print(f"     交易数: {trade_count:.0f}")
        else:
            print("   组合总体统计已在上方显示")
    except Exception as e:
        logger.warning(f"按资产分析失败: {e}")
        print("   按资产分析暂不可用")
    
    print("\n" + "="*100)
    print("✅ 回测分析完成！")
    print("="*100 + "\n")


def generate_charts(portfolio, price_data):
    """生成图表"""
    logger.info("生成图表...")
    
    try:
        from app.services.vectorbt_visualizer import VectorbtVisualizer
        
        visualizer = VectorbtVisualizer()
        
        # 生成综合报告
        backtest_config = {
            'symbol': 'BTCUSDT,ETHUSDT',
            'start_date': price_data.index[0],
            'end_date': price_data.index[-1],
            'initial_balance': 10000,
            'title': 'VectorBT集成演示回测报告'
        }

        report_data = visualizer.generate_comprehensive_report(
            portfolio=portfolio,
            backtest_config=backtest_config
        )

        html_content = report_data.get('html_content', '')
        
        # 保存HTML文件
        output_file = "vectorbt_demo_report.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"图表已保存到: {output_file}")
        print(f"📊 图表报告已生成: {output_file}")
        print("   可以在浏览器中打开查看详细的可视化分析")
        
        return True
        
    except Exception as e:
        logger.error(f"图表生成失败: {e}")
        print(f"⚠️ 图表生成失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 VectorBT集成演示开始")
    print("="*60)
    
    try:
        # 1. 创建演示数据
        price_data = create_demo_data()
        
        # 2. 创建交易信号
        entries, exits = create_trading_signals(price_data)
        
        # 3. 运行回测
        portfolio, stats = run_vectorbt_backtest(price_data, entries, exits)
        
        # 4. 显示结果
        display_results(stats, portfolio)
        
        # 5. 生成图表
        generate_charts(portfolio, price_data)
        
        print("🎉 VectorBT集成演示完成！")
        print("\n主要特性展示:")
        print("✅ 高性能回测计算 (使用Numba JIT编译)")
        print("✅ 完整的统计指标计算")
        print("✅ 详细的控制台输出")
        print("✅ 交互式图表生成")
        print("✅ 多资产组合分析")
        print("✅ 风险管理指标")
        
        return True
        
    except Exception as e:
        logger.error(f"演示执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        print(f"❌ 演示执行失败: {e}")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 演示被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 演示执行失败: {e}")
        sys.exit(1)
