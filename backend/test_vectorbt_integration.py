#!/usr/bin/env python3
"""
VectorBT集成测试脚本
测试新的VectorBT回测引擎功能
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal, engine
from app.models import Base, BacktestResult, BacktestStatus, TelegramSignal, SignalType, SignalStatus, SignalSource
from app.services.vectorbt_backtest_engine import VectorbtBacktestEngine
from app.services.vectorbt_adapter import SignalDataAdapter
from app.services.vectorbt_visualizer import VectorbtVisualizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_test_data(db):
    """创建测试数据"""
    logger.info("创建测试数据...")

    # 创建测试信号源
    test_source = SignalSource(
        name="测试信号源",
        description="用于VectorBT集成测试的信号源",
        is_active=True,
        telegram_chat_id="test_channel",
        created_at=datetime.utcnow()
    )

    # 检查是否已存在
    existing_source = db.query(SignalSource).filter(SignalSource.telegram_chat_id == "test_channel").first()
    if not existing_source:
        db.add(test_source)
        db.commit()
        db.refresh(test_source)
        source_id = test_source.id
    else:
        source_id = existing_source.id

    # 创建测试信号
    test_signals = [
        TelegramSignal(
            symbol='BTCUSDT',
            signal_type=SignalType.BUY,
            signal_time=datetime.utcnow() - timedelta(days=7),
            entry_price=45000.0,
            stop_loss=44000.0,
            take_profit=47000.0,
            quantity=0.1,
            leverage=1,
            status=SignalStatus.EXECUTED,
            source_id=source_id,
            raw_message="测试买入信号"
        ),
        TelegramSignal(
            symbol='BTCUSDT',
            signal_type=SignalType.SELL,
            signal_time=datetime.utcnow() - timedelta(days=6),
            entry_price=46000.0,
            stop_loss=47000.0,
            take_profit=44000.0,
            quantity=0.1,
            leverage=1,
            status=SignalStatus.EXECUTED,
            source_id=source_id,
            raw_message="测试卖出信号"
        ),
        TelegramSignal(
            symbol='ETHUSDT',
            signal_type=SignalType.LONG,
            signal_time=datetime.utcnow() - timedelta(days=5),
            entry_price=3000.0,
            stop_loss=2900.0,
            take_profit=3200.0,
            quantity=1.0,
            leverage=2,
            status=SignalStatus.EXECUTED,
            source_id=source_id,
            raw_message="测试做多信号"
        )
    ]
    
    for signal in test_signals:
        db.add(signal)
    
    # 创建测试回测
    test_backtest = BacktestResult(
        name="VectorBT集成测试",
        symbol="BTCUSDT",
        start_date=datetime.utcnow() - timedelta(days=10),
        end_date=datetime.utcnow() - timedelta(days=1),
        initial_balance=10000.0,
        status=BacktestStatus.PENDING,
        source_ids=[source_id],
        created_at=datetime.utcnow()
    )
    
    db.add(test_backtest)
    db.commit()
    
    logger.info(f"创建了 {len(test_signals)} 个测试信号和 1 个测试回测")
    return test_backtest


async def test_data_adapter(db, backtest):
    """测试数据适配器"""
    logger.info("测试数据适配器...")
    
    try:
        adapter = SignalDataAdapter(db)
        
        # 测试数据准备
        price_data, entries, exits = await adapter.prepare_backtest_data(backtest)
        
        logger.info(f"价格数据形状: {price_data.shape}")
        logger.info(f"入场信号数量: {entries.sum().sum()}")
        logger.info(f"出场信号数量: {exits.sum().sum()}")
        
        if price_data.empty:
            logger.warning("价格数据为空，可能是市场数据服务不可用")
            return False
        
        logger.info("✅ 数据适配器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据适配器测试失败: {e}")
        return False


async def test_vectorbt_engine(db, backtest):
    """测试VectorBT引擎"""
    logger.info("测试VectorBT引擎...")
    
    try:
        engine = VectorbtBacktestEngine(db)
        
        # 运行回测
        results = await engine.run_backtest(backtest)
        
        # 检查结果
        if 'summary' in results:
            summary = results['summary']
            logger.info(f"回测结果:")
            logger.info(f"  初始资金: ${summary.get('initial_balance', 0):,.2f}")
            logger.info(f"  最终资金: ${summary.get('final_balance', 0):,.2f}")
            logger.info(f"  总收益率: {summary.get('total_return', 0):.2%}")
            logger.info(f"  总交易数: {summary.get('total_trades', 0)}")
            logger.info(f"  胜率: {summary.get('win_rate', 0):.2%}")
            
            logger.info("✅ VectorBT引擎测试通过")
            return True
        else:
            logger.error("❌ 回测结果格式不正确")
            return False
            
    except Exception as e:
        logger.error(f"❌ VectorBT引擎测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_visualizer():
    """测试可视化器"""
    logger.info("测试可视化器...")
    
    try:
        visualizer = VectorbtVisualizer()
        
        # 测试基础功能
        logger.info("可视化器初始化成功")
        logger.info(f"默认主题: {visualizer.default_theme}")
        logger.info(f"颜色配置: {visualizer.colors}")
        
        logger.info("✅ 可视化器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 可视化器测试失败: {e}")
        return False


async def test_dependencies():
    """测试依赖项"""
    logger.info("测试依赖项...")
    
    try:
        # 测试vectorbt
        import vectorbt as vbt
        logger.info(f"✅ VectorBT版本: {vbt.__version__}")
        
        # 测试plotly
        import plotly
        logger.info(f"✅ Plotly版本: {plotly.__version__}")
        
        # 测试numba
        import numba
        logger.info(f"✅ Numba版本: {numba.__version__}")
        
        # 测试scipy
        import scipy
        logger.info(f"✅ SciPy版本: {scipy.__version__}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ 依赖项缺失: {e}")
        return False


async def run_integration_test():
    """运行完整的集成测试"""
    logger.info("🚀 开始VectorBT集成测试")
    logger.info("="*60)
    
    # 测试依赖项
    if not await test_dependencies():
        logger.error("❌ 依赖项测试失败，请检查安装")
        return False
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 创建测试数据
        backtest = create_test_data(db)
        
        # 测试可视化器
        if not test_visualizer():
            logger.error("❌ 可视化器测试失败")
            return False
        
        # 测试数据适配器
        if not await test_data_adapter(db, backtest):
            logger.error("❌ 数据适配器测试失败")
            return False
        
        # 测试VectorBT引擎
        if not await test_vectorbt_engine(db, backtest):
            logger.error("❌ VectorBT引擎测试失败")
            return False
        
        logger.info("="*60)
        logger.info("🎉 所有测试通过！VectorBT集成成功！")
        logger.info("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 集成测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
        
    finally:
        db.close()


def main():
    """主函数"""
    try:
        # 确保数据库表存在
        Base.metadata.create_all(bind=engine)
        
        # 运行测试
        success = asyncio.run(run_integration_test())
        
        if success:
            print("\n✅ VectorBT集成测试完成！")
            print("现在可以使用新的VectorBT回测引擎了。")
            sys.exit(0)
        else:
            print("\n❌ VectorBT集成测试失败！")
            print("请检查错误信息并修复问题。")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
