<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VectorBT前端集成示例</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .backtest-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #3498db;
        }
        
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .summary-card .value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .summary-card.positive .value {
            color: #27ae60;
        }
        
        .summary-card.negative .value {
            color: #e74c3c;
        }
        
        .charts-section {
            margin-top: 40px;
        }
        
        .chart-container {
            margin: 30px 0;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .chart-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .api-demo {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        
        .api-demo h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 VectorBT前端集成示例</h1>
            <p>展示如何在web页面中显示回测图表</p>
        </div>
        
        <div class="content">
            <div class="success-message">
                ✅ <strong>集成成功！</strong> 现在回测结果可以直接在web页面中显示，无需下载HTML文件。
            </div>
            
            <!-- 回测摘要 -->
            <div class="backtest-summary">
                <div class="summary-card positive">
                    <h3>总收益率</h3>
                    <div class="value">+15.2%</div>
                </div>
                <div class="summary-card">
                    <h3>总交易数</h3>
                    <div class="value">25</div>
                </div>
                <div class="summary-card positive">
                    <h3>胜率</h3>
                    <div class="value">68%</div>
                </div>
                <div class="summary-card negative">
                    <h3>最大回撤</h3>
                    <div class="value">-5.8%</div>
                </div>
                <div class="summary-card">
                    <h3>夏普比率</h3>
                    <div class="value">1.45</div>
                </div>
                <div class="summary-card">
                    <h3>盈亏比</h3>
                    <div class="value">2.1</div>
                </div>
            </div>
            
            <!-- API集成说明 -->
            <div class="api-demo">
                <h3>📡 API集成说明</h3>
                <p>前端通过以下方式获取和显示图表：</p>
                
                <div class="code-block">
// 1. 调用回测结果API
fetch('/api/backtest/123/results')
  .then(response => response.json())
  .then(data => {
    // 2. 获取图表数据
    const charts = data.charts;
    
    if (charts.success) {
      // 3. 将图表HTML插入到页面
      document.getElementById('equity-chart').innerHTML = charts.charts.equity_curve;
      document.getElementById('drawdown-chart').innerHTML = charts.charts.drawdown;
      document.getElementById('returns-chart').innerHTML = charts.charts.returns_distribution;
      document.getElementById('trades-chart').innerHTML = charts.charts.trades_analysis;
    }
  });
                </div>
            </div>
            
            <!-- 图表区域 -->
            <div class="charts-section">
                <h2>📊 回测图表分析</h2>
                
                <div class="chart-container">
                    <div class="chart-title">📈 权益曲线</div>
                    <div id="equity-chart">
                        <div class="loading">
                            <div class="loading-spinner"></div>
                            正在加载权益曲线图表...
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">📉 回撤分析</div>
                    <div id="drawdown-chart">
                        <div class="loading">
                            <div class="loading-spinner"></div>
                            正在加载回撤分析图表...
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">📊 收益分布</div>
                    <div id="returns-chart">
                        <div class="loading">
                            <div class="loading-spinner"></div>
                            正在加载收益分布图表...
                        </div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">💼 交易分析</div>
                    <div id="trades-chart">
                        <div class="loading">
                            <div class="loading-spinner"></div>
                            正在加载交易分析图表...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 技术特点 -->
            <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <h3>🎯 技术特点</h3>
                <ul style="line-height: 1.8;">
                    <li>✅ <strong>无文件下载</strong>：图表直接嵌入web页面，提升用户体验</li>
                    <li>✅ <strong>交互式图表</strong>：使用Plotly.js实现缩放、悬停等交互功能</li>
                    <li>✅ <strong>响应式设计</strong>：自适应不同屏幕尺寸和设备</li>
                    <li>✅ <strong>高性能计算</strong>：VectorBT + Numba JIT编译，10-100倍性能提升</li>
                    <li>✅ <strong>实时更新</strong>：API返回最新的回测结果和图表</li>
                    <li>✅ <strong>易于集成</strong>：简单的HTML插入，无需复杂配置</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟加载图表的过程
        setTimeout(() => {
            document.querySelectorAll('.loading').forEach(loading => {
                loading.innerHTML = '<p style="color: #27ae60; text-align: center;">✅ 图表已准备就绪！在实际应用中，这里会显示真实的VectorBT图表。</p>';
            });
        }, 2000);
        
        // 实际集成时的示例代码
        function loadBacktestCharts(backtestId) {
            // 这是实际使用时的代码示例
            fetch(`/api/backtest/${backtestId}/results`)
                .then(response => response.json())
                .then(data => {
                    if (data.charts && data.charts.success) {
                        const charts = data.charts.charts;
                        
                        // 插入各个图表
                        if (charts.equity_curve) {
                            document.getElementById('equity-chart').innerHTML = charts.equity_curve;
                        }
                        if (charts.drawdown) {
                            document.getElementById('drawdown-chart').innerHTML = charts.drawdown;
                        }
                        if (charts.returns_distribution) {
                            document.getElementById('returns-chart').innerHTML = charts.returns_distribution;
                        }
                        if (charts.trades_analysis) {
                            document.getElementById('trades-chart').innerHTML = charts.trades_analysis;
                        }
                    }
                })
                .catch(error => {
                    console.error('加载图表失败:', error);
                });
        }
        
        // 在实际应用中调用：loadBacktestCharts(123);
    </script>
</body>
</html>
