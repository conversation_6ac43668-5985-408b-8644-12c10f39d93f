#!/usr/bin/env python3
"""
测试web图表功能
验证VectorBT集成能正确生成适合web页面显示的图表
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_web_charts_generation():
    """测试web图表生成功能"""
    logger.info("测试web图表生成功能...")
    
    try:
        import vectorbt as vbt
        from app.services.vectorbt_visualizer import VectorbtVisualizer
        
        # 创建测试数据
        dates = pd.date_range('2025-01-01', '2025-01-10', freq='1h')
        np.random.seed(42)
        
        price_data = pd.DataFrame({
            'BTCUSDT': 45000 + np.cumsum(np.random.randn(len(dates)) * 100)
        }, index=dates)
        
        # 创建简单的交易信号
        entries = pd.DataFrame(False, index=dates, columns=['BTCUSDT'])
        exits = pd.DataFrame(False, index=dates, columns=['BTCUSDT'])
        
        # 每天买入一次
        for i in range(0, len(dates), 24):
            if i < len(dates):
                entries.iloc[i, 0] = True
                if i + 12 < len(dates):
                    exits.iloc[i + 12, 0] = True
        
        # 运行回测
        portfolio = vbt.Portfolio.from_signals(
            close=price_data,
            entries=entries,
            exits=exits,
            init_cash=10000,
            fees=0.001,
            freq='1h'
        )
        
        # 测试web图表生成
        visualizer = VectorbtVisualizer()
        backtest_config = {
            'symbol': 'BTCUSDT',
            'start_date': dates[0],
            'end_date': dates[-1],
            'initial_balance': 10000,
            'title': 'Web图表测试'
        }
        
        web_charts = visualizer.generate_web_charts(portfolio, backtest_config)
        
        # 验证结果
        if web_charts['success']:
            logger.info("✅ Web图表生成成功")
            logger.info(f"生成的图表数量: {len(web_charts['charts'])}")
            
            for chart_name, chart_html in web_charts['charts'].items():
                logger.info(f"  - {chart_name}: {len(chart_html)} 字符")
                
                # 验证HTML包含必要的元素
                if 'div id=' in chart_html and 'Plotly.newPlot' in chart_html:
                    logger.info(f"    ✅ {chart_name} HTML格式正确")
                else:
                    logger.warning(f"    ⚠️ {chart_name} HTML格式可能有问题")
            
            # 创建测试页面
            create_test_html_page(web_charts['charts'])
            
            return True
        else:
            logger.error(f"❌ Web图表生成失败: {web_charts.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Web图表测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def create_test_html_page(charts):
    """创建测试HTML页面"""
    logger.info("创建测试HTML页面...")
    
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VectorBT Web图表测试</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .chart-content {
            background-color: white;
            border-radius: 3px;
            padding: 10px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 VectorBT Web图表测试页面</h1>
        
        <div class="success-message">
            ✅ VectorBT集成成功！图表现在可以直接在web页面中显示，而不是生成HTML文件下载。
        </div>
"""
    
    # 图表名称映射
    chart_titles = {
        'equity_curve': '📈 权益曲线',
        'drawdown': '📉 回撤分析',
        'returns_distribution': '📊 收益分布',
        'trades_analysis': '💼 交易分析'
    }
    
    # 添加每个图表
    for chart_name, chart_html in charts.items():
        title = chart_titles.get(chart_name, chart_name)
        html_content += f"""
        <div class="chart-container">
            <div class="chart-title">{title}</div>
            <div class="chart-content">
                {chart_html}
            </div>
        </div>
"""
    
    html_content += """
        <div style="margin-top: 30px; padding: 15px; background-color: #e8f4fd; border-radius: 5px;">
            <h3>🎯 集成特点</h3>
            <ul>
                <li>✅ 图表直接嵌入web页面，无需下载文件</li>
                <li>✅ 使用Plotly.js实现交互式图表</li>
                <li>✅ 响应式设计，适配不同屏幕尺寸</li>
                <li>✅ 高性能VectorBT回测引擎</li>
                <li>✅ 完整的统计指标和可视化分析</li>
            </ul>
        </div>
    </div>
</body>
</html>
"""
    
    # 保存测试页面
    with open('web_charts_test.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info("测试HTML页面已保存: web_charts_test.html")
    print("📊 测试页面已生成: web_charts_test.html")
    print("   可以在浏览器中打开查看web图表效果")


def test_api_response_format():
    """测试API响应格式"""
    logger.info("测试API响应格式...")
    
    try:
        # 模拟API响应数据
        mock_charts = {
            'success': True,
            'charts': {
                'equity_curve': '<div id="equity-curve">...</div>',
                'drawdown': '<div id="drawdown-chart">...</div>',
                'returns_distribution': '<div id="returns-dist">...</div>'
            },
            'plotly_js_required': True
        }
        
        # 模拟完整的API响应
        api_response = {
            "backtest_info": {"id": 1, "name": "测试回测"},
            "summary": {
                "initial_balance": 10000,
                "final_balance": 11500,
                "total_return": 0.15,
                "total_trades": 25,
                "win_rate": 0.6
            },
            "detailed_results": {"summary": {}},
            "charts": mock_charts
        }
        
        # 验证响应格式
        if 'charts' in api_response and api_response['charts']['success']:
            logger.info("✅ API响应格式正确")
            logger.info(f"包含图表数量: {len(api_response['charts']['charts'])}")
            
            # 验证前端可以正确处理
            charts_data = api_response['charts']['charts']
            for chart_name, chart_html in charts_data.items():
                if isinstance(chart_html, str) and len(chart_html) > 0:
                    logger.info(f"  ✅ {chart_name}: 格式正确")
                else:
                    logger.warning(f"  ⚠️ {chart_name}: 格式可能有问题")
            
            return True
        else:
            logger.error("❌ API响应格式不正确")
            return False
            
    except Exception as e:
        logger.error(f"❌ API响应格式测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始Web图表功能测试")
    logger.info("="*60)
    
    tests = [
        ("Web图表生成测试", test_web_charts_generation),
        ("API响应格式测试", test_api_response_format)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info("="*60)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！Web图表功能正常！")
        print("\n✅ Web图表功能测试完成！")
        print("现在回测结果可以直接在web页面中显示图表了。")
        print("\n📋 使用说明:")
        print("1. 前端需要加载 plotly.js 库")
        print("2. API返回的 charts 字段包含所有图表HTML")
        print("3. 直接将图表HTML插入到页面DOM中即可显示")
        return True
    else:
        logger.error(f"❌ {total - passed} 个测试失败")
        print(f"\n❌ Web图表功能测试失败！")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        sys.exit(1)
