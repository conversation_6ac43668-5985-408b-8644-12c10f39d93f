#!/usr/bin/env python3
"""
VectorBT简单测试脚本
测试VectorBT的基本功能，不依赖复杂的数据获取
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_dependencies():
    """测试依赖项"""
    logger.info("测试依赖项...")
    
    try:
        # 测试vectorbt
        import vectorbt as vbt
        logger.info(f"✅ VectorBT版本: {vbt.__version__}")
        
        # 测试plotly
        import plotly
        logger.info(f"✅ Plotly版本: {plotly.__version__}")
        
        # 测试numba
        import numba
        logger.info(f"✅ Numba版本: {numba.__version__}")
        
        # 测试scipy
        import scipy
        logger.info(f"✅ SciPy版本: {scipy.__version__}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ 依赖项缺失: {e}")
        return False


def test_vectorbt_basic():
    """测试VectorBT基本功能"""
    logger.info("测试VectorBT基本功能...")
    
    try:
        import vectorbt as vbt
        
        # 创建模拟价格数据
        dates = pd.date_range('2025-01-01', '2025-01-31', freq='1H')
        np.random.seed(42)
        
        # 生成随机价格数据
        price_data = pd.DataFrame({
            'BTCUSDT': 45000 + np.cumsum(np.random.randn(len(dates)) * 100),
            'ETHUSDT': 3000 + np.cumsum(np.random.randn(len(dates)) * 50)
        }, index=dates)
        
        logger.info(f"创建价格数据: {price_data.shape}")
        
        # 创建简单的买入信号
        entries = pd.DataFrame(False, index=dates, columns=['BTCUSDT', 'ETHUSDT'])
        exits = pd.DataFrame(False, index=dates, columns=['BTCUSDT', 'ETHUSDT'])
        
        # 每天买入一次
        for i in range(0, len(dates), 24):
            if i < len(dates):
                entries.iloc[i, 0] = True  # BTCUSDT买入
                if i + 12 < len(dates):
                    exits.iloc[i + 12, 0] = True  # 12小时后卖出
        
        logger.info(f"入场信号数量: {entries.sum().sum()}")
        logger.info(f"出场信号数量: {exits.sum().sum()}")
        
        # 运行VectorBT回测
        portfolio = vbt.Portfolio.from_signals(
            close=price_data,
            entries=entries,
            exits=exits,
            init_cash=10000,
            fees=0.001,
            freq='1H'
        )
        
        # 获取基本统计
        total_return = portfolio.total_return()
        sharpe_ratio = portfolio.sharpe_ratio()
        max_drawdown = portfolio.max_drawdown()
        
        logger.info(f"回测结果:")
        logger.info(f"  总收益率: {total_return.iloc[0]:.2%}")
        logger.info(f"  夏普比率: {sharpe_ratio.iloc[0]:.2f}")
        logger.info(f"  最大回撤: {max_drawdown.iloc[0]:.2%}")
        
        # 测试交易统计
        trades = portfolio.trades
        trade_count = trades.count()
        if isinstance(trade_count, pd.Series):
            trade_count = trade_count.sum()

        if trade_count > 0:
            logger.info(f"  总交易数: {trade_count}")
            win_rate = trades.win_rate()
            profit_factor = trades.profit_factor()

            if isinstance(win_rate, pd.Series):
                win_rate = win_rate.iloc[0] if len(win_rate) > 0 else 0
            if isinstance(profit_factor, pd.Series):
                profit_factor = profit_factor.iloc[0] if len(profit_factor) > 0 else 0

            logger.info(f"  胜率: {win_rate:.2%}")
            logger.info(f"  盈亏比: {profit_factor:.2f}")
        else:
            logger.info("  无交易记录")
        
        logger.info("✅ VectorBT基本功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ VectorBT基本功能测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_vectorbt_visualizer():
    """测试VectorBT可视化器"""
    logger.info("测试VectorBT可视化器...")
    
    try:
        from app.services.vectorbt_visualizer import VectorbtVisualizer
        
        visualizer = VectorbtVisualizer()
        logger.info(f"可视化器初始化成功")
        logger.info(f"默认主题: {visualizer.default_theme}")
        logger.info(f"颜色配置: {visualizer.colors}")
        
        logger.info("✅ VectorBT可视化器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ VectorBT可视化器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_plotly_charts():
    """测试Plotly图表生成"""
    logger.info("测试Plotly图表生成...")
    
    try:
        import plotly.graph_objects as go
        import plotly.express as px
        
        # 创建简单的测试图表
        dates = pd.date_range('2025-01-01', '2025-01-10', freq='1D')
        values = np.random.randn(len(dates)).cumsum()
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=dates, y=values, name='Test Data'))
        fig.update_layout(title='Test Chart')
        
        # 转换为HTML
        html_str = fig.to_html(include_plotlyjs='cdn')
        
        logger.info(f"图表HTML长度: {len(html_str)} 字符")
        logger.info("✅ Plotly图表生成测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ Plotly图表生成测试失败: {e}")
        return False


def test_console_display():
    """测试控制台显示功能"""
    logger.info("测试控制台显示功能...")
    
    try:
        # 模拟回测结果
        results = {
            'summary': {
                'initial_balance': 10000.0,
                'final_balance': 11500.0,
                'total_return': 0.15,
                'total_trades': 25,
                'winning_trades': 15,
                'losing_trades': 10,
                'win_rate': 0.6,
                'profit_factor': 1.8,
                'max_drawdown': 0.08,
                'sharpe_ratio': 1.2,
                'gross_profit': 2500.0,
                'gross_loss': 1000.0
            }
        }
        
        # 显示结果
        print("\n" + "="*80)
        print("🚀 VECTORBT 回测结果报告 (测试)")
        print("="*80)
        
        summary = results['summary']
        print(f"\n📊 基础统计:")
        print(f"   初始资金: ${summary['initial_balance']:,.2f}")
        print(f"   最终资金: ${summary['final_balance']:,.2f}")
        print(f"   总收益率: {summary['total_return']:.2%}")
        print(f"   总交易数: {summary['total_trades']}")
        print(f"   胜率: {summary['win_rate']:.2%}")
        print(f"   盈亏比: {summary['profit_factor']:.2f}")
        print(f"   最大回撤: {summary['max_drawdown']:.2%}")
        print(f"   夏普比率: {summary['sharpe_ratio']:.2f}")
        
        print("\n" + "="*80)
        print("✅ 控制台显示测试完成！")
        print("="*80 + "\n")
        
        logger.info("✅ 控制台显示功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 控制台显示功能测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始VectorBT简单集成测试")
    logger.info("="*60)
    
    tests = [
        ("依赖项测试", test_dependencies),
        ("VectorBT基本功能测试", test_vectorbt_basic),
        ("VectorBT可视化器测试", test_vectorbt_visualizer),
        ("Plotly图表生成测试", test_plotly_charts),
        ("控制台显示功能测试", test_console_display)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info("="*60)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！VectorBT集成基础功能正常！")
        print("\n✅ VectorBT简单集成测试完成！")
        print("基础功能测试通过，可以继续进行完整集成。")
        return True
    else:
        logger.error(f"❌ {total - passed} 个测试失败")
        print(f"\n❌ VectorBT简单集成测试失败！")
        print(f"有 {total - passed} 个测试失败，请检查错误信息。")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        sys.exit(1)
