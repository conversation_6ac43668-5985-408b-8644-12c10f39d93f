#!/usr/bin/env python3
"""
信号解析调试脚本
专门调试特定信号的解析问题
"""

import asyncio
import sys
import os
import re

# 添加backend路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.signal_parser import EnhancedSignalParser

# 测试信号
ALPHA_SIGNAL = """Coin #ALPHA/USDT 

Position: SHORT 

Leverage:  Cross25X

Entries: 0.0139 - 0.0142

Targets: 🎯 0.0137, 0.0135, 0.0132, 0.0129, 0.0126

Stop Loss: 0.0144"""

H_SIGNAL = """➡ #H/USDT (LONG)🔼

⌛ LEVERAGE : 20X TO 75X

🟢 LONG BELOW (0.4600)(0.4550)


🪙 TARGETS 

🥇  0.04700

🥈  0.04950

🥉  0.05250



  ⚠STOPLOSS : 0.4280



😎Use only 1%margin of your total portfolio❤🙄😇"""

async def debug_signal(signal_text, signal_name):
    print(f"\n{'='*60}")
    print(f"调试信号: {signal_name}")
    print(f"{'='*60}")
    
    parser = EnhancedSignalParser()
    
    # 测试止盈目标解析
    print("原始信号:")
    print(signal_text)
    print("\n" + "-"*40)
    
    # 测试不同的止盈目标模式
    targets = parser._extract_multiple_take_profits(signal_text)
    print(f"解析的止盈目标: {targets}")
    
    # 手动测试各种模式
    print("\n手动测试各种模式:")
    
    # 1. 测试emoji目标模式
    emoji_targets_pattern = re.compile(r'Targets?\s*[:\uff1a]?\s*\ud83c\udfaf\s*([0-9.,\s]+)', re.IGNORECASE)
    match = emoji_targets_pattern.search(signal_text)
    if match:
        print(f"Emoji目标模式匹配: {match.group(1)}")
        price_str = match.group(1)
        prices = []
        for price_part in re.findall(r'([0-9.]+)', price_str):
            try:
                price = float(price_part)
                prices.append(price)
            except ValueError:
                continue
        print(f"提取的价格: {prices}")
    else:
        print("Emoji目标模式未匹配")
    
    # 2. 测试TARGETS关键词模式
    if 'TARGETS' in signal_text.upper():
        print("发现TARGETS关键词")
        targets_section = re.search(r'TARGETS.*?(?=STOPLOSS|$)', signal_text, re.IGNORECASE | re.DOTALL)
        if targets_section:
            targets_text = targets_section.group(0)
            print(f"TARGETS部分: {repr(targets_text)}")
            prices = []
            for price_match in re.finditer(r'([0-9.]+)', targets_text):
                try:
                    price = float(price_match.group(1))
                    if 0.001 <= price <= 100000:
                        prices.append(price)
                        print(f"找到价格: {price}")
                except ValueError:
                    continue
            print(f"TARGETS模式提取的价格: {prices}")
    
    # 3. 测试奖牌emoji模式
    medal_pattern = re.compile(r'[🥇🥈🥉]\s*([0-9.]+)', re.IGNORECASE)
    medal_matches = []
    for match in medal_pattern.finditer(signal_text):
        try:
            price = float(match.group(1))
            medal_matches.append(price)
            print(f"奖牌emoji匹配: {price}")
        except ValueError:
            continue
    print(f"奖牌emoji模式提取的价格: {medal_matches}")
    
    # 完整解析
    print("\n完整解析结果:")
    result = await parser.parse_message(signal_text)
    if result:
        print(f"止盈目标: {result.get('take_profit_targets', 'N/A')}")
        print(f"杠杆: {result.get('leverage', 'N/A')}")
    else:
        print("解析失败")

async def main():
    await debug_signal(ALPHA_SIGNAL, "ALPHA/USDT")
    await debug_signal(H_SIGNAL, "H/USDT")

if __name__ == "__main__":
    asyncio.run(main())