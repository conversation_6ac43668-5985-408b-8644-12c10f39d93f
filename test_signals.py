#!/usr/bin/env python3
"""
信号解析测试脚本
测试signals.md文件中的所有信号能否正确解析
"""

import asyncio
import sys
import os
from typing import Dict, Any, List

# 添加backend路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.signal_parser import EnhancedSignalParser
from app.models import SignalType

class SignalTester:
    def __init__(self):
        self.parser = EnhancedSignalParser()
        self.test_results = []
    
    def extract_signals_from_file(self, file_path: str) -> List[str]:
        """从文件中提取所有信号"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按分隔符分割信号
        signals = content.split('---')
        
        # 清理每个信号
        cleaned_signals = []
        for signal in signals:
            signal = signal.strip()
            if signal and len(signal) > 20:  # 过滤掉太短的片段
                cleaned_signals.append(signal)
        
        return cleaned_signals
    
    async def test_signal(self, signal_text: str, signal_index: int) -> Dict[str, Any]:
        """测试单个信号"""
        print(f"\n{'='*60}")
        print(f"测试信号 #{signal_index + 1}")
        print(f"{'='*60}")
        print(f"原始信号:\n{signal_text[:200]}...")
        
        try:
            result = await self.parser.parse_message(signal_text)
            
            if result:
                print(f"\n✅ 解析成功!")
                print(f"交易对: {result.get('symbol', 'N/A')}")
                print(f"信号类型: {result.get('signal_type', 'N/A')}")
                print(f"入场价格: {result.get('entry_prices', 'N/A')}")
                print(f"价格区间: {result.get('entry_price_range', 'N/A')}")
                print(f"止损: {result.get('stop_loss', 'N/A')}")
                print(f"止盈目标: {result.get('take_profit_targets', 'N/A')}")
                print(f"杠杆: {result.get('leverage', 'N/A')}")
                print(f"格式类型: {result.get('format_type', 'N/A')}")
                
                return {
                    'index': signal_index + 1,
                    'success': True,
                    'result': result,
                    'signal_text': signal_text
                }
            else:
                print(f"\n❌ 解析失败 - 无法识别信号格式")
                return {
                    'index': signal_index + 1,
                    'success': False,
                    'error': '无法识别信号格式',
                    'signal_text': signal_text
                }
                
        except Exception as e:
            print(f"\n❌ 解析出错: {str(e)}")
            return {
                'index': signal_index + 1,
                'success': False,
                'error': str(e),
                'signal_text': signal_text
            }
    
    async def run_tests(self, signals_file: str):
        """运行所有测试"""
        print(f"开始测试信号文件: {signals_file}")
        
        # 提取信号
        signals = self.extract_signals_from_file(signals_file)
        print(f"找到 {len(signals)} 个信号")
        
        # 测试每个信号
        for i, signal in enumerate(signals):
            test_result = await self.test_signal(signal, i)
            self.test_results.append(test_result)
        
        # 生成汇总报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print(f"\n{'='*80}")
        print("测试汇总报告")
        print(f"{'='*80}")
        
        total_signals = len(self.test_results)
        successful_signals = sum(1 for r in self.test_results if r['success'])
        failed_signals = total_signals - successful_signals
        
        print(f"总信号数: {total_signals}")
        print(f"成功解析: {successful_signals} ({successful_signals/total_signals*100:.1f}%)")
        print(f"解析失败: {failed_signals} ({failed_signals/total_signals*100:.1f}%)")
        
        # 成功信号统计
        if successful_signals > 0:
            print(f"\n✅ 成功解析的信号:")
            signal_types = {}
            symbols = {}
            formats = {}
            
            for result in self.test_results:
                if result['success']:
                    data = result['result']
                    
                    # 统计信号类型
                    signal_type = str(data.get('signal_type', 'Unknown'))
                    signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
                    
                    # 统计交易对
                    symbol = data.get('symbol', 'Unknown')
                    symbols[symbol] = symbols.get(symbol, 0) + 1
                    
                    # 统计格式类型
                    format_type = data.get('format_type', 'Unknown')
                    formats[format_type] = formats.get(format_type, 0) + 1
                    
                    print(f"  #{result['index']}: {symbol} {signal_type}")
            
            print(f"\n信号类型分布:")
            for signal_type, count in signal_types.items():
                print(f"  {signal_type}: {count}")
            
            print(f"\n交易对分布:")
            for symbol, count in symbols.items():
                print(f"  {symbol}: {count}")
            
            print(f"\n格式类型分布:")
            for format_type, count in formats.items():
                print(f"  {format_type}: {count}")
        
        # 失败信号分析
        if failed_signals > 0:
            print(f"\n❌ 解析失败的信号:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  #{result['index']}: {result['error']}")
                    # 显示信号的前100个字符用于分析
                    signal_preview = result['signal_text'][:100].replace('\n', ' ')
                    print(f"    预览: {signal_preview}...")
        
        # 改进建议
        self.generate_improvement_suggestions()
    
    def generate_improvement_suggestions(self):
        """生成改进建议"""
        print(f"\n{'='*80}")
        print("改进建议")
        print(f"{'='*80}")
        
        failed_results = [r for r in self.test_results if not r['success']]
        
        if not failed_results:
            print("🎉 所有信号都成功解析，无需改进!")
            return
        
        print("基于失败的信号分析，建议以下改进:")
        
        # 分析失败信号的特征
        common_patterns = []
        
        for result in failed_results:
            signal_text = result['signal_text'].upper()
            
            # 检查是否包含常见的交易对格式但未被识别
            if any(pair in signal_text for pair in ['USDT', 'BTC', 'ETH', 'BNB']):
                if '/' in signal_text or '#' in signal_text:
                    common_patterns.append("包含交易对但格式特殊")
            
            # 检查是否包含价格信息但未被识别
            if any(keyword in signal_text for keyword in ['ENTRY', 'TARGET', 'TP', 'SL', 'STOP']):
                common_patterns.append("包含价格关键词但格式特殊")
            
            # 检查是否包含方向信息但未被识别
            if any(direction in signal_text for direction in ['LONG', 'SHORT', 'BUY', 'SELL']):
                common_patterns.append("包含方向信息但格式特殊")
        
        # 统计常见问题
        pattern_counts = {}
        for pattern in common_patterns:
            pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
        
        if pattern_counts:
            print("\n发现的常见问题:")
            for pattern, count in pattern_counts.items():
                print(f"  - {pattern}: {count} 个信号")
        
        # 具体改进建议
        suggestions = [
            "1. 增加对特殊格式交易对的识别模式",
            "2. 改进价格提取的正则表达式",
            "3. 增加对中文信号的支持",
            "4. 优化emoji和特殊字符的处理",
            "5. 增加对非标准格式的容错处理"
        ]
        
        print(f"\n具体改进建议:")
        for suggestion in suggestions:
            print(f"  {suggestion}")

async def main():
    """主函数"""
    signals_file = "signals.md"
    
    if not os.path.exists(signals_file):
        print(f"错误: 找不到信号文件 {signals_file}")
        return
    
    tester = SignalTester()
    await tester.run_tests(signals_file)

if __name__ == "__main__":
    asyncio.run(main())