services:
  # PostgreSQL数据库 with TimescaleDB扩展
  postgres:
    image: timescale/timescaledb:latest-pg15
    environment:
      POSTGRES_DB: husky
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - /mnt/user/appdata/husky/postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - husky-network

  backend:
    image: ccr.ccs.tencentyun.com/langhai8045/husky-backend:latest
    environment:
      - DATABASE_URL=*********************************************
      - REDIS_URL=redis://192.168.0.46:6379/15
      - ALLOWED_ORIGINS=["http://192.168.0.46:3000"]
    ports:
      - "8010:8000"
    volumes:
      - /mnt/user/appdata/husky/backend/logs:/app/logs
    depends_on:
      - postgres
    networks:
      - husky-network

  frontend:
    image: ccr.ccs.tencentyun.com/langhai8045/husky-frontend:latest
    environment:
      - NEXT_PUBLIC_API_URL=http://192.168.0.46:8010
      - NEXT_PUBLIC_WS_URL=ws://192.168.0.46:8010
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - husky-network

  # Celery异步任务处理
  celery-worker:
    image: ccr.ccs.tencentyun.com/langhai8045/husky-backend:latest
    command: celery -A app.celery_app worker --loglevel=info --concurrency=2 --queues=celery,signal_processing,backtest,monitoring
    environment:
      - DATABASE_URL=*********************************************
      - REDIS_URL=redis://192.168.0.46:6379/15
    volumes:
      - /mnt/user/appdata/husky/backend/logs:/app/logs
    depends_on:
      - postgres
    networks:
      - husky-network
    restart: unless-stopped

  # Celery Beat 定时任务调度器（可选）
  celery-beat:
    image: ccr.ccs.tencentyun.com/langhai8045/husky-backend:latest
    command: celery -A app.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=*********************************************
      - REDIS_URL=redis://192.168.0.46:6379/15
    volumes:
      - /mnt/user/appdata/husky/backend/logs:/app/logs
    depends_on:
      - postgres
      - celery-worker
    networks:
      - husky-network
    restart: unless-stopped

networks:
  husky-network:
    driver: bridge