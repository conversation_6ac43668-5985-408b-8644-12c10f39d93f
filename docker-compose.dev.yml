services:

  
  backend:
    build: ./backend
    environment:
      - DATABASE_URL=************************************************/husky
      - REDIS_URL=redis://************:6379/15
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/logs:/app/logs
    networks:
      - husky-network

  frontend:
    build: ./frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - husky-network


networks:
  husky-network:
    driver: bridge