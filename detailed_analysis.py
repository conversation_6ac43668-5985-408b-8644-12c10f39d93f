#!/usr/bin/env python3
"""
详细信号解析分析脚本
检查解析结果的准确性
"""

import asyncio
import sys
import os

# 添加backend路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.signal_parser import EnhancedSignalParser
from app.models import SignalType

class DetailedAnalyzer:
    def __init__(self):
        self.parser = EnhancedSignalParser()
    
    async def analyze_signals(self):
        """分析每个信号的解析准确性"""
        
        # 定义测试信号和期望结果
        test_cases = [
            {
                "name": "SKATE/USDT Long",
                "signal": """🚀 SKATE/USDT – Long Setup Alert 🕯

💥 Leverage: 25x – 75x
🎯 Entry Point: 0.04415 (precision is key)

↗ Profit Zones Ahead:🙂

🔤 0.04500– First Breakout
🔤 0.04700 – Momentum Builds
🔤 0.05000 – Bullish Surge
🔤 0.05750 – Moon Zone 🌛

⛔ Stop-Loss: 0.04160 – Protect your capital!""",
                "expected": {
                    "symbol": "SKATEUSDT",
                    "signal_type": SignalType.LONG,
                    "entry_prices": [0.04415],
                    "stop_loss": 0.04160,
                    "take_profit_targets": [0.04500, 0.04700, 0.05000, 0.05750],
                    "leverage": 25  # 或50 (25-75的中间值)
                }
            },
            {
                "name": "TURBO/USDT Short",
                "signal": """⚡️ #TURBO/USDT

📥 Short 

💹 Buy: 0.003957 - 0.004070

🧿 Target: 0.003922 - 0.003883 - 0.003845 - 0.003807 - 0.003769 - 0.003732

🧨 StopLoss: 0.004168 

🔘 Leverage: 20x""",
                "expected": {
                    "symbol": "TURBOUSDT",
                    "signal_type": SignalType.SHORT,
                    "entry_prices": [0.003957, 0.004070],
                    "stop_loss": 0.004168,
                    "take_profit_targets": [0.003922, 0.003883, 0.003845, 0.003807, 0.003769, 0.003732],
                    "leverage": 20
                }
            },
            {
                "name": "BCH/USDT Short",
                "signal": """🏓SHORT BCH / USDT with 5X - 15X leverage

🟢ENTRY :- 486.65$ - 480.5$

💸TP :- 474$ - 469.5$ - 461$ - 453$ - 444$

⛔Stop loss :- 504.6$""",
                "expected": {
                    "symbol": "BCHUSDT",
                    "signal_type": SignalType.SHORT,
                    "entry_prices": [480.5, 486.65],
                    "stop_loss": 504.6,
                    "take_profit_targets": [474, 469.5, 461, 453, 444],
                    "leverage": 10  # 5-15的中间值
                }
            },
            {
                "name": "ALPHA/USDT Short",
                "signal": """Coin #ALPHA/USDT 

Position: SHORT 

Leverage:  Cross25X

Entries: 0.0139 - 0.0142

Targets: 🎯 0.0137, 0.0135, 0.0132, 0.0129, 0.0126

Stop Loss: 0.0144""",
                "expected": {
                    "symbol": "ALPHAUSDT",
                    "signal_type": SignalType.SHORT,
                    "entry_prices": [0.0139, 0.0142],
                    "stop_loss": 0.0144,
                    "take_profit_targets": [0.0137, 0.0135, 0.0132, 0.0129, 0.0126],
                    "leverage": 25
                }
            },
            {
                "name": "H/USDT Long",
                "signal": """➡ #H/USDT (LONG)🔼

⌛ LEVERAGE : 20X TO 75X

🟢 LONG BELOW (0.4600)(0.4550)

🪙 TARGETS 

🥇  0.04700

🥈  0.04950

🥉  0.05250

⚠STOPLOSS : 0.4280""",
                "expected": {
                    "symbol": "HUSDT",
                    "signal_type": SignalType.LONG,
                    "entry_prices": [0.4550, 0.4600],
                    "stop_loss": 0.4280,
                    "take_profit_targets": [0.04700, 0.04950, 0.05250],
                    "leverage": 47  # 20-75的中间值
                }
            },
            {
                "name": "DF/USDT Long",
                "signal": """Long/Buy #DF/USDT  

Entry Point ABOVE ABOVE ABOVE - 3115

Targets: 3130 - 3145 - 3160 - 3175

Leverage - 10x

Stop Loss - 2990""",
                "expected": {
                    "symbol": "DFUSDT",
                    "signal_type": SignalType.LONG,
                    "entry_prices": [3115],  # ABOVE ABOVE ABOVE表示在3115之上
                    "stop_loss": 2990,
                    "take_profit_targets": [3130, 3145, 3160, 3175],
                    "leverage": 10
                }
            },
            {
                "name": "ETH/USDT Short + JTO/USDT Long",
                "signal": """SHORT/SELL #ETH/USDT 

Entry Point BELOW BELOW BELOW - 2521

Targets: 2508 - 2495 - 2483 - 2470

Leverage - 10x
Stop Loss - 2560

--

JTO/USDT📈

1.9055 - 1.8755 多

TP1: 1.9458
TP2: 1.9725
TP3: 1.9947

SL: 1.8400""",
                "expected": {
                    "symbol": "ETHUSDT",  # 应该解析第一个信号
                    "signal_type": SignalType.SHORT,
                    "entry_prices": [2521],  # BELOW BELOW BELOW表示在2521之下
                    "stop_loss": 2560,
                    "take_profit_targets": [2508, 2495, 2483, 2470],
                    "leverage": 10
                }
            }
        ]
        
        print("详细信号解析准确性分析")
        print("=" * 80)
        
        total_tests = len(test_cases)
        accurate_tests = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试 #{i}: {test_case['name']}")
            print("-" * 50)
            
            # 解析信号
            result = await self.parser.parse_message(test_case['signal'])
            
            if not result:
                print("❌ 解析失败")
                continue
            
            # 检查准确性
            accuracy_score = 0
            total_checks = 0
            issues = []
            
            # 检查交易对
            total_checks += 1
            if result.get('symbol') == test_case['expected']['symbol']:
                accuracy_score += 1
                print(f"✅ 交易对: {result.get('symbol')}")
            else:
                issues.append(f"交易对错误: 期望 {test_case['expected']['symbol']}, 实际 {result.get('symbol')}")
                print(f"❌ 交易对: 期望 {test_case['expected']['symbol']}, 实际 {result.get('symbol')}")
            
            # 检查信号类型
            total_checks += 1
            if result.get('signal_type') == test_case['expected']['signal_type']:
                accuracy_score += 1
                print(f"✅ 信号类型: {result.get('signal_type')}")
            else:
                issues.append(f"信号类型错误: 期望 {test_case['expected']['signal_type']}, 实际 {result.get('signal_type')}")
                print(f"❌ 信号类型: 期望 {test_case['expected']['signal_type']}, 实际 {result.get('signal_type')}")
            
            # 检查入场价格
            total_checks += 1
            expected_entries = sorted(test_case['expected']['entry_prices'])
            actual_entries = sorted(result.get('entry_prices', []))
            if self._compare_price_lists(actual_entries, expected_entries):
                accuracy_score += 1
                print(f"✅ 入场价格: {actual_entries}")
            else:
                issues.append(f"入场价格错误: 期望 {expected_entries}, 实际 {actual_entries}")
                print(f"❌ 入场价格: 期望 {expected_entries}, 实际 {actual_entries}")
            
            # 检查止损
            total_checks += 1
            if abs((result.get('stop_loss', 0) or 0) - test_case['expected']['stop_loss']) < 0.001:
                accuracy_score += 1
                print(f"✅ 止损: {result.get('stop_loss')}")
            else:
                issues.append(f"止损错误: 期望 {test_case['expected']['stop_loss']}, 实际 {result.get('stop_loss')}")
                print(f"❌ 止损: 期望 {test_case['expected']['stop_loss']}, 实际 {result.get('stop_loss')}")
            
            # 检查止盈目标
            total_checks += 1
            expected_targets = sorted(test_case['expected']['take_profit_targets'])
            actual_targets = sorted(result.get('take_profit_targets', []))
            if self._compare_price_lists(actual_targets, expected_targets):
                accuracy_score += 1
                print(f"✅ 止盈目标: {actual_targets}")
            else:
                issues.append(f"止盈目标错误: 期望 {expected_targets}, 实际 {actual_targets}")
                print(f"❌ 止盈目标: 期望 {expected_targets}, 实际 {actual_targets}")
            
            # 检查杠杆（允许一定范围）
            total_checks += 1
            expected_leverage = test_case['expected']['leverage']
            actual_leverage = result.get('leverage', 1)
            if abs(actual_leverage - expected_leverage) <= 25:  # 允许25倍的误差
                accuracy_score += 1
                print(f"✅ 杠杆: {actual_leverage}")
            else:
                issues.append(f"杠杆错误: 期望约 {expected_leverage}, 实际 {actual_leverage}")
                print(f"❌ 杠杆: 期望约 {expected_leverage}, 实际 {actual_leverage}")
            
            # 计算准确率
            test_accuracy = accuracy_score / total_checks * 100
            print(f"\n准确率: {test_accuracy:.1f}% ({accuracy_score}/{total_checks})")
            
            if test_accuracy >= 80:
                accurate_tests += 1
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")
                print("问题列表:")
                for issue in issues:
                    print(f"  - {issue}")
        
        # 总体统计
        print(f"\n{'='*80}")
        print("总体统计")
        print(f"{'='*80}")
        print(f"总测试数: {total_tests}")
        print(f"准确测试数: {accurate_tests}")
        print(f"总体准确率: {accurate_tests/total_tests*100:.1f}%")
        
        # 问题分析和改进建议
        self._generate_improvement_suggestions()
    
    def _compare_price_lists(self, actual, expected, tolerance=0.001):
        """比较价格列表，允许小的浮点数误差"""
        if len(actual) != len(expected):
            return False
        
        for a, e in zip(actual, expected):
            if abs(a - e) > tolerance:
                return False
        return True
    
    def _generate_improvement_suggestions(self):
        """生成改进建议"""
        print(f"\n{'='*80}")
        print("改进建议")
        print(f"{'='*80}")
        
        suggestions = [
            "1. 改进止盈目标提取：需要更好地识别多个止盈价格",
            "2. 优化入场价格解析：ABOVE/BELOW关键词需要特殊处理",
            "3. 增强信号类型识别：Short信号被错误识别为Long",
            "4. 改进杠杆范围处理：需要正确处理范围格式（如25x-75x）",
            "5. 优化多信号处理：一个消息包含多个信号时的处理",
            "6. 增强价格格式识别：支持更多价格表示格式",
            "7. 改进emoji和特殊字符处理：保留重要的格式信息"
        ]
        
        for suggestion in suggestions:
            print(suggestion)

async def main():
    analyzer = DetailedAnalyzer()
    await analyzer.analyze_signals()

if __name__ == "__main__":
    asyncio.run(main())